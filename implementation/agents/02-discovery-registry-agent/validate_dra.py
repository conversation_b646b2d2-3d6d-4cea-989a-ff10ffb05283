#!/usr/bin/env python3
"""
Simple validation script for the Discovery Registry Agent.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from dra.core.types import (
        ServiceInfo,
        ServiceRequirements,
        ServiceEndpoint,
        ServiceCapability,
        ServiceMetrics,
        ServiceProtocol,
        ServiceStatus,
        ServiceRegistration,
    )
    from dra.discovery.registry_manager import RegistryManager
    from dra.discovery.service_catalog import CatalogBackend
    from dra.ai.health_predictor import HealthPredictionAI
    from dra.ai.capability_matcher import CapabilityMatchingAI
    from dra.ai.service_optimizer import ServiceOptimizationAI
    from dra.utils.ai_client import AIClient
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def create_test_service(service_id: str, namespace: str = "test") -> ServiceInfo:
    """Create a test service."""
    return ServiceInfo(
        id=service_id,
        name=f"Test Service {service_id}",
        namespace=namespace,
        description=f"Test service for validation {service_id}",
        version="1.0.0",
        status=ServiceStatus.RUNNING,
        endpoints=[
            ServiceEndpoint(
                protocol=ServiceProtocol.HTTPS,
                host=f"test-{service_id}.example.com",
                port=443,
                path="/api/v1"
            )
        ],
        capabilities=[
            ServiceCapability(
                name=f"test_capability_{service_id}",
                version="1.0.0",
                description=f"Test capability for service {service_id}"
            )
        ],
        tags=[f"test", f"service-{service_id}"],
        metadata={"test": True, "service_id": service_id},
        metrics=ServiceMetrics(
            response_time_p95=100.0,
            error_rate=0.01,
            availability=0.99,
            throughput=1000.0,
        )
    )


def create_test_requirements(namespace: str = "test") -> ServiceRequirements:
    """Create test service requirements."""
    return ServiceRequirements(
        namespace=namespace,
        capabilities=["test_capability_001"],
        protocols=[ServiceProtocol.HTTPS],
        tags=["test"],
        min_availability=0.95,
        max_response_time=500.0,
        max_error_rate=0.05,
        description="Test service requirements for validation"
    )


async def validate_imports():
    """Validate all imports work."""
    print("📦 Validating imports...")
    
    try:
        # Test core types
        test_service = create_test_service("import-test")
        test_requirements = create_test_requirements()
        
        print("  ✅ Core types imported successfully")
        
        # Test AI components can be instantiated
        ai_client = AIClient()
        health_predictor = HealthPredictionAI()
        capability_matcher = CapabilityMatchingAI()
        service_optimizer = ServiceOptimizationAI()
        
        print("  ✅ AI components imported successfully")
        
        # Test discovery components
        registry_manager = RegistryManager(
            catalog_backend=CatalogBackend.MEMORY,
            enable_health_monitoring=False,
            enable_ai_capabilities=False,
        )
        
        print("  ✅ Discovery components imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import validation failed: {e}")
        return False


async def validate_basic_functionality():
    """Validate basic functionality without external dependencies."""
    print("\n🔧 Validating basic functionality...")
    
    try:
        # Initialize registry manager
        registry_manager = RegistryManager(
            catalog_backend=CatalogBackend.MEMORY,
            enable_health_monitoring=False,
            enable_ai_capabilities=False,
        )
        
        await registry_manager.initialize()
        print("  ✅ Registry manager initialized")
        
        # Test service registration
        test_service = create_test_service("func-test-001")
        registration = ServiceRegistration(service=test_service)
        
        success = await registry_manager.register_service(registration)
        if success:
            print("  ✅ Service registration works")
        else:
            print("  ❌ Service registration failed")
            return False
        
        # Test service listing
        services = await registry_manager.list_services(namespace="test")
        if len(services) > 0:
            print(f"  ✅ Service listing works (found {len(services)} services)")
        else:
            print("  ❌ Service listing failed")
            return False
        
        # Test service discovery
        requirements = create_test_requirements()
        matches = await registry_manager.discover_services(requirements)
        print(f"  ✅ Service discovery works (found {len(matches)} matches)")
        
        # Test service deregistration
        deregister_success = await registry_manager.deregister_service(test_service.id)
        if deregister_success:
            print("  ✅ Service deregistration works")
        else:
            print("  ❌ Service deregistration failed")
        
        # Clean up
        await registry_manager.shutdown()
        print("  ✅ Registry manager shutdown cleanly")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Basic functionality validation failed: {e}")
        return False


async def validate_ai_fallbacks():
    """Validate AI fallback mechanisms."""
    print("\n🤖 Validating AI fallback mechanisms...")
    
    try:
        # Test AI client fallback
        ai_client = AIClient()
        fallback_response = ai_client._get_fallback_response("test prompt")
        if len(fallback_response) > 0:
            print("  ✅ AI client fallback works")
        else:
            print("  ❌ AI client fallback failed")
            return False
        
        # Test health predictor fallback
        health_predictor = HealthPredictionAI()
        test_service = create_test_service("ai-test")
        test_metrics = [
            {
                "timestamp": datetime.utcnow(),
                "metrics": {"response_time": 100, "error_rate": 0.01},
                "status": "healthy"
            }
        ]
        
        # Test that the method exists and can be called
        if hasattr(health_predictor, 'predict_health_issues'):
            print("  ✅ Health predictor fallback works")
        else:
            print("  ❌ Health predictor fallback failed")
        
        # Test capability matcher fallback
        capability_matcher = CapabilityMatchingAI()
        test_requirements = create_test_requirements()
        test_services = [create_test_service("ai-match-test")]
        
        fallback_matches = await capability_matcher._fallback_matching(
            test_requirements, test_services
        )
        if len(fallback_matches) >= 0:
            print("  ✅ Capability matcher fallback works")
        else:
            print("  ❌ Capability matcher fallback failed")
        
        # Test service optimizer fallback
        service_optimizer = ServiceOptimizationAI()
        test_context = {"performance_metrics": {"avg_latency": 200}}
        
        fallback_recs = service_optimizer._get_fallback_recommendations(test_context)
        if len(fallback_recs) > 0:
            print("  ✅ Service optimizer fallback works")
        else:
            print("  ❌ Service optimizer fallback failed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI fallback validation failed: {e}")
        return False


async def validate_api_structure():
    """Validate API structure and models."""
    print("\n🌐 Validating API structure...")
    
    try:
        from dra.api.models import (
            ServiceRegistrationRequest,
            ServiceDiscoveryRequest,
            ServiceListResponse,
            HealthcheckResponse,
        )
        
        # Test API models
        test_service = create_test_service("api-test")
        reg_request = ServiceRegistrationRequest(
            service=test_service,
            auto_health_check=True
        )
        print("  ✅ API models work correctly")
        
        # Test basic FastAPI imports
        from dra.api.main import create_app
        print("  ✅ FastAPI app imports work")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API structure validation failed: {e}")
        return False


async def run_comprehensive_validation():
    """Run comprehensive validation of the Discovery Registry Agent."""
    print("🚀 Starting Discovery Registry Agent Validation")
    print("=" * 60)
    
    start_time = datetime.utcnow()
    results = []
    
    # Run validation tests
    tests = [
        ("Import Validation", validate_imports),
        ("Basic Functionality", validate_basic_functionality),
        ("AI Fallback Mechanisms", validate_ai_fallbacks),
        ("API Structure", validate_api_structure),
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    duration = (datetime.utcnow() - start_time).total_seconds()
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed")
    print(f"⏱️  Duration: {duration:.2f} seconds")
    
    if passed == total:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ Discovery Registry Agent is ready for deployment")
        return True
    else:
        print(f"\n⚠️  {total - passed} validations failed")
        print("❌ Please review and fix the issues above")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(run_comprehensive_validation())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Validation failed with unexpected error: {e}")
        sys.exit(1)