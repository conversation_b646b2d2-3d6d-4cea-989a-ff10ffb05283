"""
API routes for the Discovery Registry Agent.

This module defines FastAPI route handlers for all service discovery operations.
"""

from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from fastapi.responses import JSONResponse
import structlog

from dra.api.models import (
    ServiceRegistrationRequest,
    ServiceRegistrationResponse,
    ServiceDeregistrationResponse,
    ServiceUpdateRequest,
    ServiceListResponse,
    ServiceListFilters,
    ServiceDiscoveryRequest,
    ServiceDiscoveryResponse,
    ServiceSearchRequest,
    HealthStatusResponse,
    HealthHistoryFilters,
    OptimizationAnalysisRequest,
    OptimizationRecommendationsResponse,
    RegistryStatisticsResponse,
    ErrorResponse,
    HealthcheckResponse,
)
from dra.core.types import ServiceInfo, ServiceStatus, ServiceRegistration
from dra.discovery.registry_manager import RegistryManager


logger = structlog.get_logger(__name__)

# Global registry manager instance (will be injected)
registry_manager: Optional[RegistryManager] = None


def get_registry_manager() -> RegistryManager:
    """Dependency to get registry manager."""
    if not registry_manager:
        raise HTTPException(
            status_code=503,
            detail="Registry manager not initialized"
        )
    return registry_manager


def set_registry_manager(manager: RegistryManager) -> None:
    """Set the global registry manager instance."""
    global registry_manager
    registry_manager = manager


# Create router
router = APIRouter()


# Health and status endpoints

@router.get(
    "/health",
    response_model=HealthcheckResponse,
    summary="API Health Check",
    description="Check the health status of the Discovery Registry Agent API",
)
async def health_check():
    """Check API health status."""
    try:
        manager = get_registry_manager()
        is_healthy = await manager.is_healthy()
        
        # Get component status
        components = {
            "registry_manager": is_healthy,
            "service_catalog": True,  # Will be updated with actual status
            "health_monitor": manager.enable_health_monitoring,
            "ai_capabilities": manager.enable_ai_capabilities,
        }
        
        # Calculate uptime
        uptime = (datetime.utcnow() - manager.startup_time).total_seconds()
        
        return HealthcheckResponse(
            status="healthy" if is_healthy else "unhealthy",
            version="1.0.0",
            uptime_seconds=uptime,
            components=components,
        )
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@router.get(
    "/statistics",
    response_model=RegistryStatisticsResponse,
    summary="Registry Statistics",
    description="Get comprehensive statistics about the service registry",
)
async def get_statistics(
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get registry statistics."""
    try:
        stats = await manager.get_registry_statistics()
        return RegistryStatisticsResponse(**stats)
        
    except Exception as e:
        logger.error("Failed to get statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve statistics")


# Service registration endpoints

@router.post(
    "/services",
    response_model=ServiceRegistrationResponse,
    status_code=201,
    summary="Register Service",
    description="Register a new service in the discovery registry",
)
async def register_service(
    request: ServiceRegistrationRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Register a new service."""
    try:
        # Create registration object
        registration = ServiceRegistration(
            service=request.service,
            ttl=request.ttl,
            auto_health_check=request.auto_health_check,
        )
        
        # Register service
        success = await manager.register_service(registration)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Service registration failed"
            )
        
        return ServiceRegistrationResponse(
            success=True,
            service_id=request.service.id,
            message="Service registered successfully",
            health_monitoring_enabled=request.auto_health_check and manager.enable_health_monitoring,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service registration failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/services/{service_id}",
    response_model=ServiceRegistrationResponse,
    summary="Update Service",
    description="Update service information in the registry",
)
async def update_service(
    service_id: str = Path(..., description="Service ID to update"),
    request: ServiceUpdateRequest = None,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Update service information."""
    try:
        # Ensure service ID matches
        if request.service.id != service_id:
            raise HTTPException(
                status_code=400,
                detail="Service ID in path must match service ID in body"
            )
        
        # Update service
        success = await manager.update_service(request.service)
        
        if not success and not request.force:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return ServiceRegistrationResponse(
            success=True,
            service_id=service_id,
            message="Service updated successfully",
            health_monitoring_enabled=manager.enable_health_monitoring,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service update failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/services/{service_id}",
    response_model=ServiceDeregistrationResponse,
    summary="Deregister Service",
    description="Remove a service from the discovery registry",
)
async def deregister_service(
    service_id: str = Path(..., description="Service ID to deregister"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Deregister a service."""
    try:
        success = await manager.deregister_service(service_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return ServiceDeregistrationResponse(
            success=True,
            service_id=service_id,
            message="Service deregistered successfully",
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Service deregistration failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Service discovery endpoints

@router.get(
    "/services",
    response_model=ServiceListResponse,
    summary="List Services",
    description="List services with optional filtering and pagination",
)
async def list_services(
    namespace: Optional[str] = Query(None, description="Filter by namespace"),
    status: Optional[ServiceStatus] = Query(None, description="Filter by status"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """List services with filtering."""
    try:
        services = await manager.list_services(
            namespace=namespace,
            status=status,
            limit=limit,
            offset=offset,
        )
        
        # For simplicity, we'll return total_count as len(services)
        # In production, you'd want to get the actual total count
        total_count = len(services)
        page = offset // limit + 1
        
        return ServiceListResponse(
            services=services,
            total_count=total_count,
            page=page,
            page_size=limit,
        )
        
    except Exception as e:
        logger.error("Failed to list services", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/services/{service_id}",
    response_model=ServiceInfo,
    summary="Get Service",
    description="Get detailed information about a specific service",
)
async def get_service(
    service_id: str = Path(..., description="Service ID to retrieve"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get service by ID."""
    try:
        service = await manager.get_service(service_id)
        
        if not service:
            raise HTTPException(
                status_code=404,
                detail="Service not found"
            )
        
        return service
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get service", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/discover",
    response_model=ServiceDiscoveryResponse,
    summary="Discover Services",
    description="Discover services based on requirements using AI-powered matching",
)
async def discover_services(
    request: ServiceDiscoveryRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Discover services based on requirements."""
    try:
        matches = await manager.discover_services(
            requirements=request.requirements,
            include_unhealthy=request.include_unhealthy,
        )
        
        # Limit results
        limited_matches = matches[:request.max_results]
        
        return ServiceDiscoveryResponse(
            matches=limited_matches,
            total_evaluated=len(matches),
            ai_powered=manager.enable_ai_capabilities,
        )
        
    except Exception as e:
        logger.error("Service discovery failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/search",
    response_model=List[ServiceInfo],
    summary="Search Services",
    description="Search services using natural language queries",
)
async def search_services(
    request: ServiceSearchRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Search services using natural language."""
    try:
        services = await manager.search_services(
            query=request.query,
            namespace=request.namespace,
            limit=request.limit,
        )
        
        return services
        
    except Exception as e:
        logger.error("Service search failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Health monitoring endpoints

@router.get(
    "/services/{service_id}/health",
    response_model=HealthStatusResponse,
    summary="Get Service Health",
    description="Get comprehensive health information for a service",
)
async def get_service_health(
    service_id: str = Path(..., description="Service ID to check"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get service health information."""
    try:
        if not manager.enable_health_monitoring:
            raise HTTPException(
                status_code=503,
                detail="Health monitoring not enabled"
            )
        
        health_info = await manager.get_service_health(service_id)
        
        if not health_info:
            raise HTTPException(
                status_code=404,
                detail="Service not found or health information unavailable"
            )
        
        return HealthStatusResponse(**health_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get service health", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/services/{service_id}/health/check",
    summary="Trigger Health Check",
    description="Trigger an immediate health check for a service",
)
async def trigger_health_check(
    service_id: str = Path(..., description="Service ID to check"),
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Trigger immediate health check."""
    try:
        if not manager.enable_health_monitoring:
            raise HTTPException(
                status_code=503,
                detail="Health monitoring not enabled"
            )
        
        success = await manager.trigger_health_check(service_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Service not found or health check failed"
            )
        
        return {"message": "Health check triggered successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Health check trigger failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# AI and optimization endpoints

@router.post(
    "/optimization/analyze",
    response_model=OptimizationRecommendationsResponse,
    summary="Get Optimization Recommendations",
    description="Get AI-powered optimization recommendations for service mesh",
)
async def get_optimization_recommendations(
    request: OptimizationAnalysisRequest,
    manager: RegistryManager = Depends(get_registry_manager)
):
    """Get optimization recommendations."""
    try:
        if not manager.enable_ai_capabilities:
            raise HTTPException(
                status_code=503,
                detail="AI capabilities not enabled"
            )
        
        recommendations = await manager.get_optimization_recommendations(
            request.context
        )
        
        return OptimizationRecommendationsResponse(
            recommendations=recommendations,
            analysis_timestamp=datetime.utcnow(),
            ai_powered=True,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Optimization analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


# Error handlers will be added at the app level in main.py