"""
Intelligent service catalog for discovery registry.

This module provides an AI-enhanced service catalog that maintains
a comprehensive registry of services with intelligent search, filtering,
and recommendation capabilities.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timed<PERSON>ta
from enum import Enum

import structlog
from redis import asyncio as aioredis

from dra.core.types import (
    ServiceInfo,
    ServiceRequirements,
    ServiceMatch,
    ServiceStatus,
    ServiceCapability,
    ServiceEndpoint,
    ServiceMetrics,
)
from dra.ai.capability_matcher import CapabilityMatchingAI


logger = structlog.get_logger(__name__)


class CatalogBackend(Enum):
    """Supported catalog backend types."""
    MEMORY = "memory"
    REDIS = "redis"
    ETCD = "etcd"
    CONSUL = "consul"
    KUBERNETES = "kubernetes"


class IntelligentServiceCatalog:
    """
    AI-enhanced service catalog that provides intelligent service discovery,
    search, and recommendation capabilities.
    """

    def __init__(
        self,
        backend: CatalogBackend = CatalogBackend.MEMORY,
        redis_url: Optional[str] = None,
    ) -> None:
        self.backend = backend
        self.redis_url = redis_url or "redis://localhost:6379"
        
        # In-memory storage for memory backend
        self.services: Dict[str, ServiceInfo] = {}
        self.service_index: Dict[str, Set[str]] = {
            "namespace": {},
            "capability": {},
            "tag": {},
            "protocol": {},
        }
        
        # Backend connections
        self.redis_client: Optional[aioredis.Redis] = None
        
        # AI components
        self.capability_matcher: Optional[CapabilityMatchingAI] = None
        
        # Caching
        self.search_cache: Dict[str, List[ServiceInfo]] = {}
        self.cache_ttl = timedelta(minutes=5)
        
        # Statistics
        self.stats = {
            "services_registered": 0,
            "services_deregistered": 0,
            "searches_performed": 0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

    async def initialize(self) -> None:
        """Initialize the service catalog."""
        logger.info(
            "Initializing Intelligent Service Catalog",
            backend=self.backend.value,
        )

        try:
            # Initialize backend connection
            if self.backend == CatalogBackend.REDIS:
                await self._init_redis_backend()
            elif self.backend == CatalogBackend.ETCD:
                await self._init_etcd_backend()
            elif self.backend == CatalogBackend.CONSUL:
                await self._init_consul_backend()
            elif self.backend == CatalogBackend.KUBERNETES:
                await self._init_kubernetes_backend()
            
            # Initialize AI components
            self.capability_matcher = CapabilityMatchingAI()
            await self.capability_matcher.initialize()
            
            # Load existing services from backend
            await self._load_existing_services()
            
            logger.info(
                "Service catalog initialized successfully",
                services_loaded=len(self.services),
            )

        except Exception as e:
            logger.error("Failed to initialize service catalog", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if the service catalog is healthy."""
        try:
            # Check backend health
            backend_healthy = await self._check_backend_health()
            
            # Check AI components health
            ai_healthy = (
                self.capability_matcher is not None
                and await self.capability_matcher.is_healthy()
            )
            
            return backend_healthy and ai_healthy
            
        except Exception:
            return False

    async def register_service(self, service: ServiceInfo) -> bool:
        """
        Register a new service in the catalog.
        
        Args:
            service: Service information to register
            
        Returns:
            True if registration successful
        """
        try:
            logger.info(
                "Registering service",
                service_id=service.id,
                service_name=service.name,
            )
            
            # Validate service info
            if not self._validate_service_info(service):
                logger.error("Invalid service info", service_id=service.id)
                return False
            
            # Store in backend
            success = await self._store_service(service)
            if not success:
                return False
            
            # Update in-memory cache
            self.services[service.id] = service
            
            # Update indices
            self._update_service_indices(service, add=True)
            
            # Clear relevant caches
            self._invalidate_caches()
            
            # Update statistics
            self.stats["services_registered"] += 1
            
            logger.info(
                "Service registered successfully",
                service_id=service.id,
                total_services=len(self.services),
            )
            
            return True

        except Exception as e:
            logger.error(
                "Failed to register service",
                service_id=service.id,
                error=str(e),
            )
            return False

    async def deregister_service(self, service_id: str) -> bool:
        """
        Deregister a service from the catalog.
        
        Args:
            service_id: ID of service to deregister
            
        Returns:
            True if deregistration successful
        """
        try:
            logger.info("Deregistering service", service_id=service_id)
            
            # Get service info before removal
            service = self.services.get(service_id)
            if not service:
                logger.warning("Service not found", service_id=service_id)
                return False
            
            # Remove from backend
            success = await self._remove_service(service_id)
            if not success:
                return False
            
            # Remove from in-memory cache
            del self.services[service_id]
            
            # Update indices
            self._update_service_indices(service, add=False)
            
            # Clear relevant caches
            self._invalidate_caches()
            
            # Update statistics
            self.stats["services_deregistered"] += 1
            
            logger.info(
                "Service deregistered successfully",
                service_id=service_id,
                remaining_services=len(self.services),
            )
            
            return True

        except Exception as e:
            logger.error(
                "Failed to deregister service",
                service_id=service_id,
                error=str(e),
            )
            return False

    async def update_service(self, service: ServiceInfo) -> bool:
        """
        Update service information in the catalog.
        
        Args:
            service: Updated service information
            
        Returns:
            True if update successful
        """
        try:
            # Check if service exists
            if service.id not in self.services:
                logger.warning("Service not found for update", service_id=service.id)
                return False
            
            # Get old service info
            old_service = self.services[service.id]
            
            # Update in backend
            success = await self._store_service(service)
            if not success:
                return False
            
            # Update in-memory cache
            self.services[service.id] = service
            
            # Update indices (remove old, add new)
            self._update_service_indices(old_service, add=False)
            self._update_service_indices(service, add=True)
            
            # Clear relevant caches
            self._invalidate_caches()
            
            logger.info("Service updated successfully", service_id=service.id)
            
            return True

        except Exception as e:
            logger.error(
                "Failed to update service",
                service_id=service.id,
                error=str(e),
            )
            return False

    async def get_service(self, service_id: str) -> Optional[ServiceInfo]:
        """
        Get service information by ID.
        
        Args:
            service_id: Service ID to lookup
            
        Returns:
            Service information if found
        """
        try:
            # Check in-memory cache first
            service = self.services.get(service_id)
            if service:
                return service
            
            # Try to load from backend
            service = await self._load_service(service_id)
            if service:
                self.services[service_id] = service
                self._update_service_indices(service, add=True)
            
            return service

        except Exception as e:
            logger.error(
                "Failed to get service",
                service_id=service_id,
                error=str(e),
            )
            return None

    async def list_services(
        self,
        namespace: Optional[str] = None,
        status: Optional[ServiceStatus] = None,
        tags: Optional[List[str]] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ServiceInfo]:
        """
        List services with optional filtering.
        
        Args:
            namespace: Filter by namespace
            status: Filter by service status
            tags: Filter by tags (any match)
            limit: Maximum services to return
            offset: Pagination offset
            
        Returns:
            List of matching services
        """
        try:
            # Build cache key
            cache_key = self._build_cache_key(
                "list", namespace, status, tags, limit, offset
            )
            
            # Check cache
            cached = self._get_cached_results(cache_key)
            if cached is not None:
                self.stats["cache_hits"] += 1
                return cached
            
            self.stats["cache_misses"] += 1
            
            # Filter services
            filtered_services = []
            
            for service in self.services.values():
                # Apply filters
                if namespace and service.namespace != namespace:
                    continue
                
                if status and service.status != status:
                    continue
                
                if tags:
                    service_tags = set(service.tags)
                    if not any(tag in service_tags for tag in tags):
                        continue
                
                filtered_services.append(service)
            
            # Sort by name for consistent ordering
            filtered_services.sort(key=lambda s: s.name)
            
            # Apply pagination
            result = filtered_services[offset:offset + limit]
            
            # Cache results
            self._cache_results(cache_key, result)
            
            return result

        except Exception as e:
            logger.error("Failed to list services", error=str(e))
            return []

    async def search_services(
        self,
        query: str,
        namespace: Optional[str] = None,
        limit: int = 20,
    ) -> List[ServiceInfo]:
        """
        Search services using natural language query.
        
        Args:
            query: Search query
            namespace: Optional namespace filter
            limit: Maximum results
            
        Returns:
            List of matching services
        """
        try:
            self.stats["searches_performed"] += 1
            
            # Build cache key
            cache_key = self._build_cache_key("search", query, namespace, limit)
            
            # Check cache
            cached = self._get_cached_results(cache_key)
            if cached is not None:
                self.stats["cache_hits"] += 1
                return cached
            
            self.stats["cache_misses"] += 1
            
            # Filter by namespace if specified
            search_pool = list(self.services.values())
            if namespace:
                search_pool = [s for s in search_pool if s.namespace == namespace]
            
            if not search_pool:
                return []
            
            # Use AI-powered search if available
            if self.capability_matcher:
                # Convert query to requirements
                requirements = ServiceRequirements(
                    description=query,
                    namespace=namespace or "default",
                )
                
                # Get AI matches
                matches = await self.capability_matcher.match_services_to_requirements(
                    requirements, search_pool[:100]  # Limit for performance
                )
                
                # Extract services from matches
                result = [match.service for match in matches[:limit]]
            else:
                # Fallback to simple text matching
                result = self._simple_text_search(query, search_pool, limit)
            
            # Cache results
            self._cache_results(cache_key, result)
            
            return result

        except Exception as e:
            logger.error("Failed to search services", error=str(e))
            return []

    async def find_services_by_capability(
        self,
        capability: str,
        namespace: Optional[str] = None,
    ) -> List[ServiceInfo]:
        """
        Find services that provide a specific capability.
        
        Args:
            capability: Capability name to search for
            namespace: Optional namespace filter
            
        Returns:
            List of services with the capability
        """
        try:
            # Use capability index
            service_ids = self.service_index["capability"].get(capability.lower(), set())
            
            services = []
            for service_id in service_ids:
                service = self.services.get(service_id)
                if service:
                    if not namespace or service.namespace == namespace:
                        services.append(service)
            
            return services

        except Exception as e:
            logger.error(
                "Failed to find services by capability",
                capability=capability,
                error=str(e),
            )
            return []

    async def get_service_statistics(self) -> Dict[str, Any]:
        """Get catalog statistics."""
        try:
            namespace_counts = {}
            status_counts = {}
            
            for service in self.services.values():
                # Count by namespace
                namespace_counts[service.namespace] = (
                    namespace_counts.get(service.namespace, 0) + 1
                )
                
                # Count by status
                status_counts[service.status.value] = (
                    status_counts.get(service.status.value, 0) + 1
                )
            
            return {
                "total_services": len(self.services),
                "namespaces": namespace_counts,
                "statuses": status_counts,
                "operations": self.stats,
                "cache_size": len(self.search_cache),
                "backend": self.backend.value,
            }

        except Exception as e:
            logger.error("Failed to get statistics", error=str(e))
            return {}

    # Backend initialization methods

    async def _init_redis_backend(self) -> None:
        """Initialize Redis backend."""
        try:
            self.redis_client = await aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
            )
            await self.redis_client.ping()
            logger.info("Redis backend initialized")
        except Exception as e:
            logger.error("Failed to initialize Redis backend", error=str(e))
            raise

    async def _init_etcd_backend(self) -> None:
        """Initialize etcd backend."""
        # TODO: Implement etcd backend
        logger.warning("etcd backend not yet implemented")

    async def _init_consul_backend(self) -> None:
        """Initialize Consul backend."""
        # TODO: Implement Consul backend
        logger.warning("Consul backend not yet implemented")

    async def _init_kubernetes_backend(self) -> None:
        """Initialize Kubernetes backend."""
        # TODO: Implement Kubernetes backend
        logger.warning("Kubernetes backend not yet implemented")

    # Backend operations

    async def _check_backend_health(self) -> bool:
        """Check if backend is healthy."""
        try:
            if self.backend == CatalogBackend.MEMORY:
                return True
            elif self.backend == CatalogBackend.REDIS and self.redis_client:
                await self.redis_client.ping()
                return True
            # TODO: Add health checks for other backends
            return False
        except Exception:
            return False

    async def _store_service(self, service: ServiceInfo) -> bool:
        """Store service in backend."""
        try:
            if self.backend == CatalogBackend.MEMORY:
                # Already stored in memory
                return True
            elif self.backend == CatalogBackend.REDIS and self.redis_client:
                key = f"service:{service.id}"
                value = json.dumps(service.to_dict())
                await self.redis_client.set(key, value)
                return True
            # TODO: Add storage for other backends
            return False
        except Exception as e:
            logger.error("Failed to store service", error=str(e))
            return False

    async def _remove_service(self, service_id: str) -> bool:
        """Remove service from backend."""
        try:
            if self.backend == CatalogBackend.MEMORY:
                # Already removed from memory
                return True
            elif self.backend == CatalogBackend.REDIS and self.redis_client:
                key = f"service:{service_id}"
                await self.redis_client.delete(key)
                return True
            # TODO: Add removal for other backends
            return False
        except Exception as e:
            logger.error("Failed to remove service", error=str(e))
            return False

    async def _load_service(self, service_id: str) -> Optional[ServiceInfo]:
        """Load service from backend."""
        try:
            if self.backend == CatalogBackend.MEMORY:
                return None  # Memory backend doesn't persist
            elif self.backend == CatalogBackend.REDIS and self.redis_client:
                key = f"service:{service_id}"
                value = await self.redis_client.get(key)
                if value:
                    data = json.loads(value)
                    return ServiceInfo.from_dict(data)
                return None
            # TODO: Add loading for other backends
            return None
        except Exception as e:
            logger.error("Failed to load service", error=str(e))
            return None

    async def _load_existing_services(self) -> None:
        """Load all existing services from backend."""
        try:
            if self.backend == CatalogBackend.MEMORY:
                return  # Nothing to load
            elif self.backend == CatalogBackend.REDIS and self.redis_client:
                # Scan for all service keys
                async for key in self.redis_client.scan_iter("service:*"):
                    value = await self.redis_client.get(key)
                    if value:
                        try:
                            data = json.loads(value)
                            service = ServiceInfo.from_dict(data)
                            self.services[service.id] = service
                            self._update_service_indices(service, add=True)
                        except Exception as e:
                            logger.warning(
                                "Failed to load service",
                                key=key,
                                error=str(e),
                            )
            # TODO: Add loading for other backends
        except Exception as e:
            logger.error("Failed to load existing services", error=str(e))

    # Helper methods

    def _validate_service_info(self, service: ServiceInfo) -> bool:
        """Validate service information."""
        # Basic validation
        if not service.id or not service.name:
            return False
        
        if not service.namespace:
            return False
        
        if not service.endpoints:
            return False
        
        return True

    def _update_service_indices(self, service: ServiceInfo, add: bool) -> None:
        """Update service indices for fast lookup."""
        # Update namespace index
        namespace_set = self.service_index["namespace"].setdefault(
            service.namespace, set()
        )
        if add:
            namespace_set.add(service.id)
        else:
            namespace_set.discard(service.id)
        
        # Update capability index
        for capability in service.capabilities:
            cap_set = self.service_index["capability"].setdefault(
                capability.name.lower(), set()
            )
            if add:
                cap_set.add(service.id)
            else:
                cap_set.discard(service.id)
        
        # Update tag index
        for tag in service.tags:
            tag_set = self.service_index["tag"].setdefault(tag.lower(), set())
            if add:
                tag_set.add(service.id)
            else:
                tag_set.discard(service.id)
        
        # Update protocol index
        for endpoint in service.endpoints:
            proto_set = self.service_index["protocol"].setdefault(
                endpoint.protocol.value, set()
            )
            if add:
                proto_set.add(service.id)
            else:
                proto_set.discard(service.id)

    def _simple_text_search(
        self, query: str, services: List[ServiceInfo], limit: int
    ) -> List[ServiceInfo]:
        """Simple text-based search fallback."""
        query_lower = query.lower()
        scored_services = []
        
        for service in services:
            score = 0.0
            
            # Check name
            if query_lower in service.name.lower():
                score += 10.0
            
            # Check description
            if service.description and query_lower in service.description.lower():
                score += 5.0
            
            # Check capabilities
            for cap in service.capabilities:
                if query_lower in cap.name.lower():
                    score += 3.0
                if cap.description and query_lower in cap.description.lower():
                    score += 1.0
            
            # Check tags
            for tag in service.tags:
                if query_lower in tag.lower():
                    score += 2.0
            
            if score > 0:
                scored_services.append((score, service))
        
        # Sort by score and return top results
        scored_services.sort(key=lambda x: x[0], reverse=True)
        return [service for _, service in scored_services[:limit]]

    def _build_cache_key(self, *args) -> str:
        """Build cache key from arguments."""
        import hashlib
        key_parts = [str(arg) for arg in args if arg is not None]
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_cached_results(self, cache_key: str) -> Optional[List[ServiceInfo]]:
        """Get cached results if still valid."""
        cached = self.search_cache.get(cache_key)
        if cached:
            # Simple TTL check
            # TODO: Implement proper timestamp tracking
            return cached
        return None

    def _cache_results(self, cache_key: str, results: List[ServiceInfo]) -> None:
        """Cache search results."""
        self.search_cache[cache_key] = results
        
        # Simple cache size management
        if len(self.search_cache) > 100:
            # Remove oldest entries
            keys_to_remove = list(self.search_cache.keys())[:20]
            for key in keys_to_remove:
                del self.search_cache[key]

    def _invalidate_caches(self) -> None:
        """Invalidate all caches."""
        self.search_cache.clear()