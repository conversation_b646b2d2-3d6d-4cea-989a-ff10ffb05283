"""
Communication Broker Agent (CBA) client for DRA.

This module provides integration with the Communication Broker Agent
for inter-agent messaging and coordination within the platform.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from uuid import uuid4

import structlog
import httpx
import websockets

from dra.core.types import AgentMessage


logger = structlog.get_logger(__name__)


class CBAClient:
    """
    Client for communicating with the Communication Broker Agent.
    Provides both HTTP and WebSocket communication channels.
    """

    def __init__(
        self,
        cba_base_url: str = "http://localhost:8080",
        cba_ws_url: str = "ws://localhost:8080/ws",
        agent_id: str = "DRA-002",
        api_key: Optional[str] = None,
    ) -> None:
        self.cba_base_url = cba_base_url.rstrip("/")
        self.cba_ws_url = cba_ws_url
        self.agent_id = agent_id
        self.api_key = api_key
        
        # HTTP client
        self.http_client: Optional[httpx.AsyncClient] = None
        
        # WebSocket connection
        self.ws_connection: Optional[websockets.WebSocketServerProtocol] = None
        self.ws_connected = False
        
        # Message handling
        self.message_handlers: Dict[str, Callable] = {}
        self.pending_responses: Dict[str, asyncio.Future] = {}
        
        # Background tasks
        self.ws_listener_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Statistics
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0,
            "connection_failures": 0,
            "reconnections": 0,
        }

    async def initialize(self) -> None:
        """Initialize the CBA client."""
        logger.info("Initializing CBA client", cba_url=self.cba_base_url)
        
        try:
            # Initialize HTTP client
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            self.http_client = httpx.AsyncClient(
                base_url=self.cba_base_url,
                headers=headers,
                timeout=30.0,
            )
            
            # Test CBA connection
            await self._test_cba_connection()
            
            # Register with CBA
            await self._register_agent()
            
            self.running = True
            
            # Start WebSocket connection
            await self._connect_websocket()
            
            logger.info("CBA client initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize CBA client", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the CBA client."""
        logger.info("Shutting down CBA client")
        
        self.running = False
        
        # Stop background tasks
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        if self.ws_listener_task:
            self.ws_listener_task.cancel()
            try:
                await self.ws_listener_task
            except asyncio.CancelledError:
                pass
        
        # Close WebSocket connection
        if self.ws_connection:
            await self.ws_connection.close()
            self.ws_connected = False
        
        # Close HTTP client
        if self.http_client:
            await self.http_client.aclose()
        
        logger.info("CBA client shutdown complete")

    async def send_message(
        self,
        to_agent_id: str,
        message_type: str,
        payload: Dict[str, Any],
        correlation_id: Optional[str] = None,
        wait_for_response: bool = False,
        timeout: float = 30.0,
    ) -> Optional[AgentMessage]:
        """
        Send a message to another agent via CBA.
        
        Args:
            to_agent_id: Target agent ID
            message_type: Type of message
            payload: Message payload
            correlation_id: Optional correlation ID
            wait_for_response: Whether to wait for a response
            timeout: Response timeout in seconds
            
        Returns:
            Response message if wait_for_response is True
        """
        try:
            message = AgentMessage(
                id=str(uuid4()),
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=message_type,
                payload=payload,
                correlation_id=correlation_id or str(uuid4()),
                timestamp=datetime.utcnow(),
            )
            
            # Send via WebSocket if connected, otherwise HTTP
            if self.ws_connected and self.ws_connection:
                await self._send_message_ws(message)
            else:
                await self._send_message_http(message)
            
            self.stats["messages_sent"] += 1
            
            # Wait for response if requested
            if wait_for_response:
                return await self._wait_for_response(
                    message.correlation_id, timeout
                )
            
            return None
            
        except Exception as e:
            logger.error(
                "Failed to send message",
                to_agent=to_agent_id,
                message_type=message_type,
                error=str(e),
            )
            raise

    async def register_message_handler(
        self, message_type: str, handler: Callable[[AgentMessage], None]
    ) -> None:
        """Register a message handler for a specific message type."""
        self.message_handlers[message_type] = handler
        logger.debug("Message handler registered", message_type=message_type)

    async def unregister_message_handler(self, message_type: str) -> None:
        """Unregister a message handler."""
        self.message_handlers.pop(message_type, None)
        logger.debug("Message handler unregistered", message_type=message_type)

    async def publish_service_event(
        self, event_type: str, service_info: Dict[str, Any]
    ) -> None:
        """Publish a service discovery event to interested agents."""
        try:
            await self.send_message(
                to_agent_id="*",  # Broadcast
                message_type=f"service_{event_type}",
                payload={
                    "event_type": event_type,
                    "service_info": service_info,
                    "timestamp": datetime.utcnow().isoformat(),
                },
            )
            
            logger.debug(
                "Service event published",
                event_type=event_type,
                service_id=service_info.get("id"),
            )
            
        except Exception as e:
            logger.error(
                "Failed to publish service event",
                event_type=event_type,
                error=str(e),
            )

    async def get_statistics(self) -> Dict[str, Any]:
        """Get CBA client statistics."""
        return {
            "connection_status": {
                "http_connected": self.http_client is not None,
                "ws_connected": self.ws_connected,
                "cba_base_url": self.cba_base_url,
            },
            "message_stats": self.stats,
            "registered_handlers": list(self.message_handlers.keys()),
            "pending_responses": len(self.pending_responses),
        }

    # Private methods

    async def _test_cba_connection(self) -> None:
        """Test connection to CBA."""
        try:
            if not self.http_client:
                raise RuntimeError("HTTP client not initialized")
            
            response = await self.http_client.get("/health")
            response.raise_for_status()
            
            logger.debug("CBA connection test successful")
            
        except Exception as e:
            logger.error("CBA connection test failed", error=str(e))
            self.stats["connection_failures"] += 1
            raise

    async def _register_agent(self) -> None:
        """Register this agent with CBA."""
        try:
            if not self.http_client:
                raise RuntimeError("HTTP client not initialized")
            
            registration_data = {
                "agent_id": self.agent_id,
                "agent_type": "discovery_registry",
                "capabilities": [
                    "service_registration",
                    "service_discovery",
                    "health_monitoring",
                    "capability_matching",
                ],
                "endpoints": {
                    "health": "/api/v1/health",
                    "metrics": "/api/v1/statistics",
                },
            }
            
            response = await self.http_client.post(
                "/agents/register",
                json=registration_data,
            )
            response.raise_for_status()
            
            logger.info("Agent registered with CBA successfully")
            
        except Exception as e:
            logger.error("Agent registration failed", error=str(e))
            # Don't raise - CBA might not be available yet
            self.stats["connection_failures"] += 1

    async def _connect_websocket(self) -> None:
        """Establish WebSocket connection to CBA."""
        try:
            # Add agent ID to WebSocket URL
            ws_url = f"{self.cba_ws_url}?agent_id={self.agent_id}"
            
            self.ws_connection = await websockets.connect(ws_url)
            self.ws_connected = True
            
            # Start background tasks
            self.ws_listener_task = asyncio.create_task(self._ws_listener_loop())
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            logger.info("WebSocket connection established")
            
        except Exception as e:
            logger.warning("WebSocket connection failed", error=str(e))
            self.ws_connected = False
            # Continue without WebSocket - will use HTTP fallback

    async def _send_message_ws(self, message: AgentMessage) -> None:
        """Send message via WebSocket."""
        try:
            if not self.ws_connection:
                raise RuntimeError("WebSocket not connected")
            
            message_data = {
                "type": "agent_message",
                "data": message.to_dict(),
            }
            
            await self.ws_connection.send(json.dumps(message_data))
            
            logger.debug(
                "Message sent via WebSocket",
                to_agent=message.to_agent_id,
                message_type=message.message_type,
            )
            
        except Exception as e:
            logger.error("WebSocket message send failed", error=str(e))
            # Fallback to HTTP
            await self._send_message_http(message)

    async def _send_message_http(self, message: AgentMessage) -> None:
        """Send message via HTTP."""
        try:
            if not self.http_client:
                raise RuntimeError("HTTP client not initialized")
            
            response = await self.http_client.post(
                "/messages/send",
                json=message.to_dict(),
            )
            response.raise_for_status()
            
            logger.debug(
                "Message sent via HTTP",
                to_agent=message.to_agent_id,
                message_type=message.message_type,
            )
            
        except Exception as e:
            logger.error("HTTP message send failed", error=str(e))
            raise

    async def _wait_for_response(
        self, correlation_id: str, timeout: float
    ) -> Optional[AgentMessage]:
        """Wait for a response message with the given correlation ID."""
        try:
            # Create future for this correlation ID
            future = asyncio.Future()
            self.pending_responses[correlation_id] = future
            
            # Wait for response with timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            
            return response
            
        except asyncio.TimeoutError:
            logger.warning(
                "Response timeout",
                correlation_id=correlation_id,
                timeout=timeout,
            )
            return None
        finally:
            # Clean up
            self.pending_responses.pop(correlation_id, None)

    async def _ws_listener_loop(self) -> None:
        """Background task to listen for WebSocket messages."""
        logger.info("Starting WebSocket listener")
        
        while self.running and self.ws_connected:
            try:
                if not self.ws_connection:
                    break
                
                # Receive message
                message_data = await self.ws_connection.recv()
                message_json = json.loads(message_data)
                
                # Handle message
                await self._handle_received_message(message_json)
                
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket connection closed")
                self.ws_connected = False
                break
            except Exception as e:
                logger.error("WebSocket listener error", error=str(e))
                await asyncio.sleep(1)

    async def _heartbeat_loop(self) -> None:
        """Background task to send heartbeat messages."""
        while self.running and self.ws_connected:
            try:
                if self.ws_connection:
                    heartbeat_data = {
                        "type": "heartbeat",
                        "agent_id": self.agent_id,
                        "timestamp": datetime.utcnow().isoformat(),
                    }
                    
                    await self.ws_connection.send(json.dumps(heartbeat_data))
                
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
                
            except Exception as e:
                logger.error("Heartbeat failed", error=str(e))
                break

    async def _handle_received_message(self, message_data: Dict[str, Any]) -> None:
        """Handle a received message from CBA."""
        try:
            message_type = message_data.get("type")
            
            if message_type == "agent_message":
                # Parse agent message
                agent_message = AgentMessage.from_dict(message_data.get("data", {}))
                
                self.stats["messages_received"] += 1
                
                # Check if this is a response to a pending request
                if agent_message.correlation_id in self.pending_responses:
                    future = self.pending_responses[agent_message.correlation_id]
                    if not future.done():
                        future.set_result(agent_message)
                    return
                
                # Handle via registered handlers
                handler = self.message_handlers.get(agent_message.message_type)
                if handler:
                    try:
                        await handler(agent_message)
                    except Exception as e:
                        logger.error(
                            "Message handler failed",
                            message_type=agent_message.message_type,
                            error=str(e),
                        )
                else:
                    logger.warning(
                        "No handler for message type",
                        message_type=agent_message.message_type,
                    )
            
            elif message_type == "heartbeat_response":
                # Handle heartbeat response
                logger.debug("Heartbeat response received")
            
            else:
                logger.warning("Unknown message type", message_type=message_type)
                
        except Exception as e:
            logger.error("Failed to handle received message", error=str(e))