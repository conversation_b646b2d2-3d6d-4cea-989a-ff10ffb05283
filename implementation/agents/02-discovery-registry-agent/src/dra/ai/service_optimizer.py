"""
AI-powered service mesh optimization.

This module provides intelligent recommendations for service mesh configuration,
routing optimization, and performance improvements using AI analysis.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

import numpy as np
import structlog

from dra.core.types import OptimizationRecommendation, ServiceInfo
from dra.utils.ai_client import AIClient


logger = structlog.get_logger(__name__)


class ServiceOptimizationAI:
    """
    AI-powered service optimization system that analyzes service mesh
    configurations and provides intelligent recommendations for improvements.
    """

    def __init__(self) -> None:
        self.ai_client: Optional[AIClient] = None
        self.recommendation_cache: Dict[str, List[OptimizationRecommendation]] = {}
        self.cache_ttl = timedelta(minutes=30)
        self.optimization_history: Dict[str, List[Dict[str, Any]]] = {}

    async def initialize(self) -> None:
        """Initialize the service optimization system."""
        logger.info("Initializing Service Optimization AI")

        try:
            # Initialize AI client
            self.ai_client = AIClient()
            await self.ai_client.initialize()

            logger.info("Service Optimization AI initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize Service Optimization AI", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if the service optimizer is healthy."""
        try:
            return self.ai_client is not None and await self.ai_client.is_healthy()
        except Exception:
            return False

    async def generate_recommendations(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """
        Generate optimization recommendations based on service mesh context.
        
        Args:
            context: Service mesh context including configuration, metrics, etc.
            
        Returns:
            List of optimization recommendations
        """
        try:
            # Check cache first
            cache_key = self._get_cache_key(context)
            cached_recommendations = self._get_cached_recommendations(cache_key)
            if cached_recommendations:
                return cached_recommendations

            # Analyze different aspects of the service mesh
            recommendations = []

            # 1. Routing optimization
            routing_recs = await self._analyze_routing_optimization(context)
            recommendations.extend(routing_recs)

            # 2. Load balancing improvements
            lb_recs = await self._analyze_load_balancing(context)
            recommendations.extend(lb_recs)

            # 3. Circuit breaker configuration
            cb_recs = await self._analyze_circuit_breakers(context)
            recommendations.extend(cb_recs)

            # 4. Timeout and retry policies
            timeout_recs = await self._analyze_timeout_policies(context)
            recommendations.extend(timeout_recs)

            # 5. Performance optimizations
            perf_recs = await self._analyze_performance_optimizations(context)
            recommendations.extend(perf_recs)

            # 6. Security hardening
            security_recs = await self._analyze_security_improvements(context)
            recommendations.extend(security_recs)

            # Sort by priority and impact
            recommendations.sort(
                key=lambda x: (
                    self._priority_score(x.priority),
                    x.impact_score
                ),
                reverse=True
            )

            # Cache recommendations
            self._cache_recommendations(cache_key, recommendations)

            logger.info(
                "Optimization recommendations generated",
                recommendation_count=len(recommendations),
                categories=self._count_by_category(recommendations),
            )

            return recommendations

        except Exception as e:
            logger.error("Failed to generate recommendations", error=str(e))
            return self._get_fallback_recommendations(context)

    async def _analyze_routing_optimization(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend routing optimizations."""
        try:
            if not self.ai_client:
                return []

            routing_config = context.get("routing_config", {})
            performance_metrics = context.get("performance_metrics", {})
            service_dependencies = context.get("service_dependencies", [])

            prompt = f"""
Analyze the service mesh routing configuration and recommend optimizations:

CURRENT ROUTING CONFIGURATION:
{json.dumps(routing_config, indent=2)}

PERFORMANCE METRICS:
{json.dumps(performance_metrics, indent=2)}

SERVICE DEPENDENCIES:
{json.dumps(service_dependencies, indent=2)}

Provide routing optimization recommendations focusing on:
1. Latency reduction through optimal routing paths
2. Load distribution improvements
3. Failover and resilience enhancements
4. Traffic shaping opportunities
5. Service affinity optimizations

Respond with a JSON array of recommendations:
[{{
    "title": "Brief title of the recommendation",
    "description": "Detailed description of what to change and why",
    "impact_score": 8.5,  // 0-10 scale
    "implementation_effort": "medium",  // low, medium, high
    "priority": "high",  // low, medium, high, critical
    "configuration_changes": {{
        "specific": "configuration changes as JSON"
    }},
    "expected_benefits": ["List of expected improvements"],
    "risks": ["Potential risks or considerations"]
}}]
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=2000,
            )

            recommendations = self._parse_recommendations_response(
                response, "routing"
            )

            return recommendations

        except Exception as e:
            logger.error("Routing optimization analysis failed", error=str(e))
            return []

    async def _analyze_load_balancing(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend load balancing improvements."""
        try:
            if not self.ai_client:
                return []

            lb_config = context.get("load_balancing_config", {})
            service_metrics = context.get("service_metrics", {})

            # Analyze load distribution
            load_distribution = self._calculate_load_distribution(service_metrics)

            prompt = f"""
Analyze load balancing configuration and recommend improvements:

LOAD BALANCING CONFIG:
{json.dumps(lb_config, indent=2)}

SERVICE METRICS:
{json.dumps(service_metrics, indent=2)}

LOAD DISTRIBUTION ANALYSIS:
{json.dumps(load_distribution, indent=2)}

Recommend load balancing optimizations for:
1. Better load distribution across instances
2. Algorithm improvements (round-robin vs weighted vs least-connections)
3. Health check optimization
4. Session affinity configuration
5. Geographic load balancing

Focus on practical improvements that reduce latency and improve reliability.

Respond with a JSON array of recommendations following the same format as before.
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=1500,
            )

            recommendations = self._parse_recommendations_response(
                response, "load_balancing"
            )

            return recommendations

        except Exception as e:
            logger.error("Load balancing analysis failed", error=str(e))
            return []

    async def _analyze_circuit_breakers(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend circuit breaker configurations."""
        try:
            if not self.ai_client:
                return []

            cb_config = context.get("circuit_breaker_config", {})
            error_rates = context.get("error_rates", {})
            failure_patterns = context.get("failure_patterns", [])

            prompt = f"""
Analyze circuit breaker configuration and recommend improvements:

CIRCUIT BREAKER CONFIG:
{json.dumps(cb_config, indent=2)}

ERROR RATES:
{json.dumps(error_rates, indent=2)}

FAILURE PATTERNS:
{json.dumps(failure_patterns, indent=2)}

Recommend circuit breaker optimizations:
1. Threshold adjustments based on error patterns
2. Timeout configuration improvements
3. Half-open state optimization
4. Failure detection sensitivity
5. Recovery policies

Consider the balance between availability and preventing cascading failures.

Respond with a JSON array of recommendations.
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=1500,
            )

            recommendations = self._parse_recommendations_response(
                response, "circuit_breaker"
            )

            return recommendations

        except Exception as e:
            logger.error("Circuit breaker analysis failed", error=str(e))
            return []

    async def _analyze_timeout_policies(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend timeout and retry policy improvements."""
        try:
            if not self.ai_client:
                return []

            timeout_config = context.get("timeout_config", {})
            retry_config = context.get("retry_config", {})
            latency_percentiles = context.get("latency_percentiles", {})

            prompt = f"""
Analyze timeout and retry policies and recommend optimizations:

TIMEOUT CONFIGURATION:
{json.dumps(timeout_config, indent=2)}

RETRY CONFIGURATION:
{json.dumps(retry_config, indent=2)}

LATENCY PERCENTILES:
{json.dumps(latency_percentiles, indent=2)}

Recommend timeout and retry optimizations:
1. Timeout values based on actual latency patterns
2. Retry strategies (exponential backoff, jitter)
3. Idempotency considerations
4. Budget-based retry limits
5. Hedged requests for critical paths

Balance between user experience and resource efficiency.

Respond with a JSON array of recommendations.
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=1500,
            )

            recommendations = self._parse_recommendations_response(
                response, "timeout_retry"
            )

            return recommendations

        except Exception as e:
            logger.error("Timeout policy analysis failed", error=str(e))
            return []

    async def _analyze_performance_optimizations(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend general performance optimizations."""
        try:
            if not self.ai_client:
                return []

            performance_data = context.get("performance_metrics", {})
            resource_usage = context.get("resource_usage", {})
            bottlenecks = self._identify_bottlenecks(performance_data)

            prompt = f"""
Analyze service mesh performance and recommend optimizations:

PERFORMANCE METRICS:
{json.dumps(performance_data, indent=2)}

RESOURCE USAGE:
{json.dumps(resource_usage, indent=2)}

IDENTIFIED BOTTLENECKS:
{json.dumps(bottlenecks, indent=2)}

Recommend performance optimizations:
1. Caching strategies
2. Connection pooling improvements
3. Batch processing opportunities
4. Compression and serialization
5. Resource scaling recommendations
6. Database query optimization hints

Focus on high-impact, practical improvements.

Respond with a JSON array of recommendations.
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=2000,
            )

            recommendations = self._parse_recommendations_response(
                response, "performance"
            )

            return recommendations

        except Exception as e:
            logger.error("Performance optimization analysis failed", error=str(e))
            return []

    async def _analyze_security_improvements(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze and recommend security improvements."""
        try:
            if not self.ai_client:
                return []

            security_config = context.get("security_config", {})
            auth_config = context.get("authentication_config", {})
            tls_config = context.get("tls_config", {})

            prompt = f"""
Analyze service mesh security configuration and recommend improvements:

SECURITY CONFIGURATION:
{json.dumps(security_config, indent=2)}

AUTHENTICATION CONFIG:
{json.dumps(auth_config, indent=2)}

TLS CONFIGURATION:
{json.dumps(tls_config, indent=2)}

Recommend security improvements:
1. mTLS implementation and optimization
2. Authorization policy enhancements
3. Secret management improvements
4. Network policy recommendations
5. Security monitoring and alerting

Focus on defense-in-depth without impacting performance significantly.

Respond with a JSON array of recommendations.
"""

            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.3,
                max_tokens=1500,
            )

            recommendations = self._parse_recommendations_response(
                response, "security"
            )

            return recommendations

        except Exception as e:
            logger.error("Security analysis failed", error=str(e))
            return []

    def _parse_recommendations_response(
        self, response: str, category: str
    ) -> List[OptimizationRecommendation]:
        """Parse AI response into recommendation objects."""
        try:
            # Extract JSON array from response
            start_idx = response.find("[")
            end_idx = response.rfind("]") + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                parsed_recs = json.loads(json_str)
                
                recommendations = []
                for rec in parsed_recs:
                    try:
                        recommendation = OptimizationRecommendation(
                            category=category,
                            title=rec.get("title", "Optimization Recommendation"),
                            description=rec.get("description", ""),
                            impact_score=float(rec.get("impact_score", 5.0)),
                            implementation_effort=rec.get("implementation_effort", "medium"),
                            priority=rec.get("priority", "medium"),
                            configuration_changes=rec.get("configuration_changes", {}),
                            expected_benefits=rec.get("expected_benefits", []),
                        )
                        recommendations.append(recommendation)
                    except Exception as e:
                        logger.warning(
                            "Failed to parse recommendation",
                            error=str(e),
                            recommendation=rec,
                        )
                
                return recommendations

        except Exception as e:
            logger.error("Failed to parse recommendations response", error=str(e))

        return []

    def _get_fallback_recommendations(
        self, context: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Generate basic fallback recommendations when AI is unavailable."""
        recommendations = []

        # Basic routing recommendation
        recommendations.append(
            OptimizationRecommendation(
                category="routing",
                title="Review Routing Configuration",
                description="Manual review of routing configuration recommended due to AI unavailability",
                impact_score=5.0,
                implementation_effort="medium",
                priority="medium",
                expected_benefits=["Potential latency improvements"],
            )
        )

        # Basic performance recommendation
        if "performance_metrics" in context:
            metrics = context["performance_metrics"]
            if metrics.get("avg_latency", 0) > 100:
                recommendations.append(
                    OptimizationRecommendation(
                        category="performance",
                        title="High Latency Detected",
                        description=f"Average latency of {metrics.get('avg_latency')}ms exceeds recommended threshold",
                        impact_score=7.0,
                        implementation_effort="high",
                        priority="high",
                        expected_benefits=["Reduced response times", "Better user experience"],
                    )
                )

        return recommendations

    def _calculate_load_distribution(
        self, service_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate load distribution statistics."""
        try:
            instance_loads = []
            for instance_id, metrics in service_metrics.items():
                if isinstance(metrics, dict):
                    load = metrics.get("request_rate", 0)
                    instance_loads.append(load)

            if not instance_loads:
                return {"status": "no_data"}

            loads = np.array(instance_loads)
            return {
                "mean_load": float(np.mean(loads)),
                "std_deviation": float(np.std(loads)),
                "max_load": float(np.max(loads)),
                "min_load": float(np.min(loads)),
                "imbalance_ratio": float(np.max(loads) / np.mean(loads)) if np.mean(loads) > 0 else 0,
                "coefficient_of_variation": float(np.std(loads) / np.mean(loads)) if np.mean(loads) > 0 else 0,
            }

        except Exception as e:
            logger.error("Failed to calculate load distribution", error=str(e))
            return {"status": "calculation_error"}

    def _identify_bottlenecks(
        self, performance_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Identify performance bottlenecks from metrics."""
        bottlenecks = []

        try:
            # Check for high latency services
            for service_id, metrics in performance_data.items():
                if isinstance(metrics, dict):
                    p99_latency = metrics.get("p99_latency", 0)
                    if p99_latency > 1000:  # 1 second
                        bottlenecks.append({
                            "type": "high_latency",
                            "service": service_id,
                            "metric": "p99_latency",
                            "value": p99_latency,
                            "severity": "high" if p99_latency > 5000 else "medium",
                        })

                    # Check for high error rates
                    error_rate = metrics.get("error_rate", 0)
                    if error_rate > 0.05:  # 5%
                        bottlenecks.append({
                            "type": "high_error_rate",
                            "service": service_id,
                            "metric": "error_rate",
                            "value": error_rate,
                            "severity": "high" if error_rate > 0.1 else "medium",
                        })

                    # Check for resource constraints
                    cpu_usage = metrics.get("cpu_usage", 0)
                    if cpu_usage > 0.8:  # 80%
                        bottlenecks.append({
                            "type": "high_cpu",
                            "service": service_id,
                            "metric": "cpu_usage",
                            "value": cpu_usage,
                            "severity": "high" if cpu_usage > 0.9 else "medium",
                        })

        except Exception as e:
            logger.error("Failed to identify bottlenecks", error=str(e))

        return bottlenecks

    def _priority_score(self, priority: str) -> int:
        """Convert priority string to numeric score."""
        priority_map = {
            "critical": 4,
            "high": 3,
            "medium": 2,
            "low": 1,
        }
        return priority_map.get(priority.lower(), 2)

    def _count_by_category(
        self, recommendations: List[OptimizationRecommendation]
    ) -> Dict[str, int]:
        """Count recommendations by category."""
        counts = {}
        for rec in recommendations:
            counts[rec.category] = counts.get(rec.category, 0) + 1
        return counts

    def _get_cache_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for context."""
        import hashlib
        
        # Create deterministic key from context
        context_str = json.dumps(context, sort_keys=True)
        return hashlib.md5(context_str.encode()).hexdigest()

    def _get_cached_recommendations(
        self, cache_key: str
    ) -> Optional[List[OptimizationRecommendation]]:
        """Get cached recommendations if still valid."""
        cached = self.recommendation_cache.get(cache_key)
        if cached:
            # Simple TTL check (implement proper timestamp tracking later)
            return cached
        return None

    def _cache_recommendations(
        self, cache_key: str, recommendations: List[OptimizationRecommendation]
    ) -> None:
        """Cache recommendations."""
        self.recommendation_cache[cache_key] = recommendations
        
        # Simple cache size management
        if len(self.recommendation_cache) > 50:
            # Remove oldest entries
            keys_to_remove = list(self.recommendation_cache.keys())[:10]
            for key in keys_to_remove:
                del self.recommendation_cache[key]