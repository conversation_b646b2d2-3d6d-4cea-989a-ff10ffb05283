"""
AI-powered health prediction for services.

This module provides intelligent health monitoring and prediction capabilities
using machine learning models and AI reasoning.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import numpy as np
import structlog
from anthropic import AsyncAnthropic
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

from dra.core.types import HealthPrediction, ServiceMetrics, ServiceStatus
from dra.utils.ai_client import AIClient


logger = structlog.get_logger(__name__)


class HealthPredictionAI:
    """
    AI-powered health prediction system that uses machine learning models
    and large language models to predict service health issues.
    """

    def __init__(self) -> None:
        self.ai_client: Optional[AIClient] = None
        self.anomaly_detector: Optional[IsolationForest] = None
        self.scaler: Optional[StandardScaler] = None
        self.historical_data: Dict[str, List[ServiceMetrics]] = {}
        self.prediction_cache: Dict[str, HealthPrediction] = {}
        self.cache_ttl = timedelta(minutes=5)

    async def initialize(self) -> None:
        """Initialize the health prediction system."""
        logger.info("Initializing Health Prediction AI")

        try:
            # Initialize AI client
            self.ai_client = AIClient()
            await self.ai_client.initialize()

            # Initialize anomaly detection model
            self.anomaly_detector = IsolationForest(
                contamination=0.1,  # Expect 10% of data to be anomalous
                random_state=42,
                n_estimators=100,
            )

            # Initialize scaler for feature normalization
            self.scaler = StandardScaler()

            logger.info("Health Prediction AI initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize Health Prediction AI", error=str(e))
            raise

    async def is_healthy(self) -> bool:
        """Check if the health predictor is healthy."""
        try:
            return (
                self.ai_client is not None
                and await self.ai_client.is_healthy()
                and self.anomaly_detector is not None
            )
        except Exception:
            return False

    async def predict_service_health(self, service_id: str) -> Optional[HealthPrediction]:
        """
        Predict health status for a service using AI and ML models.
        
        Args:
            service_id: The ID of the service to predict health for
            
        Returns:
            HealthPrediction with predictions for different time horizons
        """
        try:
            # Check cache first
            cached_prediction = self._get_cached_prediction(service_id)
            if cached_prediction:
                return cached_prediction

            # Get historical metrics for the service
            metrics_history = self.historical_data.get(service_id, [])
            if len(metrics_history) < 5:  # Need minimum data for prediction
                logger.warning(
                    "Insufficient historical data for prediction",
                    service_id=service_id,
                    data_points=len(metrics_history),
                )
                return None

            # Perform ML-based anomaly detection
            ml_prediction = await self._ml_health_prediction(service_id, metrics_history)

            # Perform AI-powered analysis
            ai_prediction = await self._ai_health_prediction(service_id, metrics_history)

            # Combine predictions
            combined_prediction = self._combine_predictions(
                service_id, ml_prediction, ai_prediction
            )

            # Cache the prediction
            self._cache_prediction(service_id, combined_prediction)

            logger.info("Health prediction completed", service_id=service_id)
            return combined_prediction

        except Exception as e:
            logger.error("Health prediction failed", service_id=service_id, error=str(e))
            return None

    async def update_service_metrics(
        self, service_id: str, metrics: ServiceMetrics
    ) -> None:
        """Update metrics for a service."""
        try:
            if service_id not in self.historical_data:
                self.historical_data[service_id] = []

            # Add new metrics
            self.historical_data[service_id].append(metrics)

            # Keep only last 1000 data points to manage memory
            if len(self.historical_data[service_id]) > 1000:
                self.historical_data[service_id] = self.historical_data[service_id][-1000:]

            # Retrain anomaly detector if enough data
            if len(self.historical_data[service_id]) >= 100:
                await self._retrain_anomaly_detector(service_id)

        except Exception as e:
            logger.error("Failed to update service metrics", error=str(e))

    async def _ml_health_prediction(
        self, service_id: str, metrics_history: List[ServiceMetrics]
    ) -> Dict[str, Any]:
        """Perform ML-based health prediction using anomaly detection."""
        try:
            if not self.anomaly_detector or not self.scaler:
                raise RuntimeError("ML models not initialized")

            # Extract features from metrics
            features = self._extract_features(metrics_history)

            if len(features) < 10:  # Need minimum features for ML
                return {"confidence": 0.5, "anomaly_score": 0.0, "risk_level": "low"}

            # Normalize features
            features_scaled = self.scaler.fit_transform(features)

            # Detect anomalies
            anomaly_scores = self.anomaly_detector.decision_function(features_scaled)
            is_anomaly = self.anomaly_detector.predict(features_scaled)

            # Calculate risk metrics
            current_anomaly_score = anomaly_scores[-1]
            recent_anomalies = sum(1 for x in is_anomaly[-10:] if x == -1)
            
            # Determine risk level
            if current_anomaly_score < -0.5 or recent_anomalies > 3:
                risk_level = "high"
                confidence = 0.85
            elif current_anomaly_score < -0.2 or recent_anomalies > 1:
                risk_level = "medium"
                confidence = 0.7
            else:
                risk_level = "low"
                confidence = 0.6

            return {
                "confidence": confidence,
                "anomaly_score": float(current_anomaly_score),
                "risk_level": risk_level,
                "recent_anomalies": recent_anomalies,
            }

        except Exception as e:
            logger.error("ML health prediction failed", error=str(e))
            return {"confidence": 0.5, "anomaly_score": 0.0, "risk_level": "unknown"}

    async def _ai_health_prediction(
        self, service_id: str, metrics_history: List[ServiceMetrics]
    ) -> Dict[str, Any]:
        """Perform AI-powered health prediction using LLM reasoning."""
        try:
            if not self.ai_client:
                raise RuntimeError("AI client not initialized")

            # Prepare recent metrics for analysis
            recent_metrics = metrics_history[-20:]  # Last 20 data points
            metrics_summary = self._summarize_metrics(recent_metrics)

            # Create analysis prompt
            prompt = self._create_health_analysis_prompt(service_id, metrics_summary)

            # Get AI analysis
            response = await self.ai_client.generate_response(
                prompt=prompt,
                temperature=0.2,
                max_tokens=1000,
            )

            # Parse AI response
            ai_analysis = self._parse_ai_health_response(response)

            return ai_analysis

        except Exception as e:
            logger.error("AI health prediction failed", error=str(e))
            return {
                "confidence": 0.5,
                "predictions": {},
                "risk_factors": [],
                "recommendations": [],
            }

    def _combine_predictions(
        self,
        service_id: str,
        ml_prediction: Dict[str, Any],
        ai_prediction: Dict[str, Any],
    ) -> HealthPrediction:
        """Combine ML and AI predictions into a final health prediction."""
        try:
            # Extract confidence scores
            ml_confidence = ml_prediction.get("confidence", 0.5)
            ai_confidence = ai_prediction.get("confidence", 0.5)

            # Weight the predictions based on confidence
            total_weight = ml_confidence + ai_confidence
            if total_weight > 0:
                ml_weight = ml_confidence / total_weight
                ai_weight = ai_confidence / total_weight
            else:
                ml_weight = ai_weight = 0.5

            # Combine risk assessments
            ml_risk = ml_prediction.get("risk_level", "low")
            ai_risk_factors = ai_prediction.get("risk_factors", [])

            # Determine predictions for different time horizons
            predictions = {}
            confidence_scores = {}

            # Base prediction on ML anomaly detection and AI analysis
            if ml_risk == "high" or len(ai_risk_factors) > 2:
                base_status = ServiceStatus.DEGRADED
                base_confidence = max(ml_confidence, ai_confidence)
            elif ml_risk == "medium" or len(ai_risk_factors) > 0:
                base_status = ServiceStatus.HEALTHY
                base_confidence = (ml_confidence + ai_confidence) / 2
            else:
                base_status = ServiceStatus.HEALTHY
                base_confidence = min(ml_confidence, ai_confidence)

            # Create predictions for different time horizons
            predictions["15_minutes"] = base_status
            predictions["1_hour"] = base_status
            predictions["4_hours"] = (
                ServiceStatus.DEGRADED if ml_risk == "high" else base_status
            )

            confidence_scores["15_minutes"] = base_confidence
            confidence_scores["1_hour"] = base_confidence * 0.8
            confidence_scores["4_hours"] = base_confidence * 0.6

            # Combine risk factors and recommendations
            risk_factors = ai_risk_factors.copy()
            if ml_risk in ["medium", "high"]:
                risk_factors.append(f"Anomaly detection: {ml_risk} risk")

            recommendations = ai_prediction.get("recommendations", [])
            if ml_prediction.get("recent_anomalies", 0) > 2:
                recommendations.append("Investigate recent performance anomalies")

            return HealthPrediction(
                service_id=service_id,
                predictions=predictions,
                confidence_scores=confidence_scores,
                risk_factors=risk_factors,
                recommended_actions=recommendations,
            )

        except Exception as e:
            logger.error("Failed to combine predictions", error=str(e))
            # Return safe default prediction
            return HealthPrediction(
                service_id=service_id,
                predictions={"15_minutes": ServiceStatus.UNKNOWN},
                confidence_scores={"15_minutes": 0.5},
                risk_factors=["Prediction combination failed"],
                recommended_actions=["Monitor service manually"],
            )

    def _extract_features(self, metrics_history: List[ServiceMetrics]) -> np.ndarray:
        """Extract numerical features from metrics history."""
        features = []
        for metrics in metrics_history:
            feature_vector = [
                metrics.cpu_usage,
                metrics.memory_usage,
                metrics.request_rate,
                metrics.error_rate,
                metrics.response_time_p50,
                metrics.response_time_p95,
                metrics.response_time_p99,
                metrics.availability,
            ]
            features.append(feature_vector)
        return np.array(features)

    def _summarize_metrics(self, metrics_history: List[ServiceMetrics]) -> Dict[str, Any]:
        """Summarize metrics for AI analysis."""
        if not metrics_history:
            return {}

        # Calculate statistics
        cpu_values = [m.cpu_usage for m in metrics_history]
        memory_values = [m.memory_usage for m in metrics_history]
        error_rates = [m.error_rate for m in metrics_history]
        response_times = [m.response_time_p95 for m in metrics_history]

        return {
            "timespan_minutes": len(metrics_history),
            "cpu_usage": {
                "avg": np.mean(cpu_values),
                "max": np.max(cpu_values),
                "trend": "increasing" if cpu_values[-1] > cpu_values[0] else "stable",
            },
            "memory_usage": {
                "avg": np.mean(memory_values),
                "max": np.max(memory_values),
                "trend": "increasing" if memory_values[-1] > memory_values[0] else "stable",
            },
            "error_rate": {
                "avg": np.mean(error_rates),
                "max": np.max(error_rates),
                "recent": error_rates[-5:] if len(error_rates) >= 5 else error_rates,
            },
            "response_time_p95": {
                "avg": np.mean(response_times),
                "max": np.max(response_times),
                "recent": response_times[-5:] if len(response_times) >= 5 else response_times,
            },
        }

    def _create_health_analysis_prompt(
        self, service_id: str, metrics_summary: Dict[str, Any]
    ) -> str:
        """Create prompt for AI health analysis."""
        return f"""
Analyze service health metrics and predict potential issues:

Service: {service_id}
Metrics Summary: {json.dumps(metrics_summary, indent=2)}

Analyze the following:
1. Current health status and trends
2. Potential failure points and risks
3. Predictions for next 15 minutes, 1 hour, and 4 hours
4. Recommended preventive actions

Consider patterns, trends, and anomalies in the data.

Respond with a JSON object containing:
- confidence: float (0.0-1.0)
- predictions: dict with time horizons and status
- risk_factors: list of identified risks
- recommendations: list of recommended actions
- reasoning: detailed explanation

Example response:
{{
    "confidence": 0.85,
    "predictions": {{
        "15_minutes": "healthy",
        "1_hour": "healthy", 
        "4_hours": "degraded"
    }},
    "risk_factors": ["High CPU usage trend", "Increasing error rate"],
    "recommendations": ["Scale up resources", "Check for memory leaks"],
    "reasoning": "CPU usage has increased 40% in the last hour..."
}}
"""

    def _parse_ai_health_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response for health analysis."""
        try:
            # Try to extract JSON from response
            start_idx = response.find("{")
            end_idx = response.rfind("}") + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                
                # Validate required fields
                return {
                    "confidence": float(parsed.get("confidence", 0.5)),
                    "predictions": parsed.get("predictions", {}),
                    "risk_factors": parsed.get("risk_factors", []),
                    "recommendations": parsed.get("recommendations", []),
                    "reasoning": parsed.get("reasoning", ""),
                }
            else:
                # Fallback parsing
                return {
                    "confidence": 0.5,
                    "predictions": {},
                    "risk_factors": [],
                    "recommendations": [],
                    "reasoning": response,
                }

        except Exception as e:
            logger.error("Failed to parse AI response", error=str(e))
            return {
                "confidence": 0.5,
                "predictions": {},
                "risk_factors": ["AI analysis parsing failed"],
                "recommendations": ["Manual health check required"],
                "reasoning": "Failed to parse AI response",
            }

    async def _retrain_anomaly_detector(self, service_id: str) -> None:
        """Retrain the anomaly detector with new data."""
        try:
            metrics_history = self.historical_data.get(service_id, [])
            if len(metrics_history) < 100:
                return

            features = self._extract_features(metrics_history)
            if self.scaler and self.anomaly_detector:
                features_scaled = self.scaler.fit_transform(features)
                self.anomaly_detector.fit(features_scaled)

            logger.debug("Anomaly detector retrained", service_id=service_id)

        except Exception as e:
            logger.error("Failed to retrain anomaly detector", error=str(e))

    def _get_cached_prediction(self, service_id: str) -> Optional[HealthPrediction]:
        """Get cached prediction if still valid."""
        cached = self.prediction_cache.get(service_id)
        if cached and (datetime.utcnow() - cached.prediction_timestamp) < self.cache_ttl:
            return cached
        return None

    def _cache_prediction(self, service_id: str, prediction: HealthPrediction) -> None:
        """Cache a prediction."""
        self.prediction_cache[service_id] = prediction