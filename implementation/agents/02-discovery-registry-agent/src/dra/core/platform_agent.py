"""
Base PlatformAgent implementation for the AI-Native Agent Platform.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, Optional

import structlog

from dra.core.types import (
    AgentIntelligenceLevel,
    AgentMessage,
    AgentMetrics,
    AgentStatus,
    HealthStatus,
)


logger = structlog.get_logger(__name__)


class PlatformAgent(ABC):
    """
    Base class for all platform agents with standardized lifecycle management,
    communication, and AI integration capabilities.
    """

    def __init__(
        self,
        agent_id: str,
        name: str,
        version: str = "1.0.0",
        intelligence_level: AgentIntelligenceLevel = AgentIntelligenceLevel.MEDIUM,
        description: str = "",
    ) -> None:
        self.agent_id = agent_id
        self.name = name
        self.version = version
        self.intelligence_level = intelligence_level
        self.description = description
        
        self.status = AgentStatus.STOPPED
        self.started_at: Optional[datetime] = None
        self.last_heartbeat: Optional[datetime] = None
        
        # Metrics tracking
        self.metrics = AgentMetrics(agent_id=agent_id)
        
        # Lifecycle management
        self._shutdown_event = asyncio.Event()
        self._tasks: set[asyncio.Task] = set()
        
        # Logger
        self.logger = structlog.get_logger(agent_id)

    async def initialize(self) -> None:
        """Initialize the agent."""
        self.logger.info("Initializing agent", agent_id=self.agent_id)
        self.status = AgentStatus.INITIALIZING
        
        try:
            await self._initialize_agent()
            self.logger.info("Agent initialized successfully")
        except Exception as e:
            self.logger.error("Failed to initialize agent", error=str(e))
            self.status = AgentStatus.ERROR
            raise

    async def start(self) -> None:
        """Start the agent."""
        if self.status == AgentStatus.RUNNING:
            self.logger.warning("Agent is already running")
            return
            
        self.logger.info("Starting agent", agent_id=self.agent_id)
        self.status = AgentStatus.STARTING
        
        try:
            # Start agent-specific functionality
            await self._start_agent()
            
            # Start background tasks
            self._start_background_tasks()
            
            self.status = AgentStatus.RUNNING
            self.started_at = datetime.utcnow()
            self.logger.info("Agent started successfully")
            
        except Exception as e:
            self.logger.error("Failed to start agent", error=str(e))
            self.status = AgentStatus.ERROR
            raise

    async def stop(self) -> None:
        """Stop the agent gracefully."""
        if self.status in (AgentStatus.STOPPED, AgentStatus.STOPPING):
            return
            
        self.logger.info("Stopping agent", agent_id=self.agent_id)
        self.status = AgentStatus.STOPPING
        
        try:
            # Signal shutdown to background tasks
            self._shutdown_event.set()
            
            # Wait for background tasks to complete
            if self._tasks:
                await asyncio.gather(*self._tasks, return_exceptions=True)
                self._tasks.clear()
            
            # Stop agent-specific functionality
            await self._stop_agent()
            
            self.status = AgentStatus.STOPPED
            self.logger.info("Agent stopped successfully")
            
        except Exception as e:
            self.logger.error("Error during agent shutdown", error=str(e))
            self.status = AgentStatus.ERROR
            raise

    async def get_health(self) -> HealthStatus:
        """Get agent health status."""
        try:
            agent_health = await self._get_agent_health()
            
            # Update last heartbeat
            self.last_heartbeat = datetime.utcnow()
            
            return HealthStatus(
                status=agent_health.get("status", "healthy"),
                timestamp=datetime.utcnow(),
                details={
                    "agent_id": self.agent_id,
                    "status": self.status.value,
                    "uptime": self._get_uptime(),
                    "tasks": len(self._tasks),
                    **agent_health.get("details", {}),
                },
                checks=agent_health.get("checks", {}),
                message=agent_health.get("message"),
            )
        except Exception as e:
            self.logger.error("Health check failed", error=str(e))
            return HealthStatus(
                status="unhealthy",
                timestamp=datetime.utcnow(),
                details={"error": str(e)},
                message=f"Health check failed: {e}",
            )

    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process an incoming message from another agent."""
        try:
            self.logger.debug(
                "Processing message",
                from_agent=message.from_agent_id,
                message_type=message.message_type,
            )
            
            # Update metrics
            self.metrics.messages_processed += 1
            
            # Process the message
            response = await self._process_message(message)
            
            return response
            
        except Exception as e:
            self.logger.error("Message processing failed", error=str(e))
            raise

    async def send_message(self, message: AgentMessage) -> None:
        """Send a message to another agent via CBA."""
        try:
            await self._send_message(message)
        except Exception as e:
            self.logger.error("Failed to send message", error=str(e))
            raise

    def get_metrics(self) -> AgentMetrics:
        """Get current agent metrics."""
        self.metrics.timestamp = datetime.utcnow()
        return self.metrics

    def get_info(self) -> Dict[str, Any]:
        """Get agent information."""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "version": self.version,
            "intelligence_level": self.intelligence_level.value,
            "description": self.description,
            "status": self.status.value,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "uptime": self._get_uptime(),
        }

    def _get_uptime(self) -> Optional[float]:
        """Get agent uptime in seconds."""
        if self.started_at:
            return (datetime.utcnow() - self.started_at).total_seconds()
        return None

    def _start_background_tasks(self) -> None:
        """Start background tasks."""
        # Start heartbeat task
        task = asyncio.create_task(self._heartbeat_loop())
        self._tasks.add(task)
        task.add_done_callback(self._tasks.discard)
        
        # Start agent-specific background tasks
        additional_tasks = self._get_background_tasks()
        for task_func in additional_tasks:
            task = asyncio.create_task(task_func())
            self._tasks.add(task)
            task.add_done_callback(self._tasks.discard)

    async def _heartbeat_loop(self) -> None:
        """Background heartbeat loop."""
        while not self._shutdown_event.is_set():
            try:
                await self.get_health()
                await asyncio.sleep(30)  # Heartbeat every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Heartbeat failed", error=str(e))
                await asyncio.sleep(5)  # Retry after 5 seconds

    # Abstract methods that must be implemented by subclasses

    @abstractmethod
    async def _initialize_agent(self) -> None:
        """Initialize agent-specific components."""
        pass

    @abstractmethod
    async def _start_agent(self) -> None:
        """Start agent-specific functionality."""
        pass

    @abstractmethod
    async def _stop_agent(self) -> None:
        """Stop agent-specific functionality."""
        pass

    @abstractmethod
    async def _get_agent_health(self) -> Dict[str, Any]:
        """Get agent-specific health information."""
        pass

    @abstractmethod
    async def _process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process agent-specific messages."""
        pass

    @abstractmethod
    async def _send_message(self, message: AgentMessage) -> None:
        """Send message via communication broker."""
        pass

    def _get_background_tasks(self) -> list:
        """Get list of background task functions to start."""
        return []

    @asynccontextmanager
    async def lifespan(self):
        """Async context manager for agent lifecycle."""
        try:
            await self.initialize()
            await self.start()
            yield self
        finally:
            await self.stop()