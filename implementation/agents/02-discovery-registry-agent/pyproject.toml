[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "discovery-registry-agent"
version = "1.0.0"
description = "AI-powered service discovery and registry management agent"
authors = ["AI Platform Team <<EMAIL>>"]
license = "MIT"
readme = "README.md"
packages = [{include = "dra", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
httpx = "^0.25.2"
aioredis = "^2.0.1"
asyncio-mqtt = "^0.16.1"
python-etcd3 = "^0.12.0"
kubernetes = "^28.1.0"
consul = "^1.1.0"
prometheus-client = "^0.19.0"
structlog = "^23.2.0"
tenacity = "^8.2.3"
numpy = "^1.25.2"
scikit-learn = "^1.3.2"
sentence-transformers = "^2.2.2"
openai = "^1.3.8"
anthropic = "^0.7.7"
google-generativeai = "^0.3.2"
tensorflow = "^2.15.0"
torch = "^2.1.1"
transformers = "^4.36.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
aioresponses = "^0.7.4"
pytest-benchmark = "^4.0.0"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["dra"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/dra",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=85"
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src/dra"]
omit = ["tests/*", "src/dra/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]