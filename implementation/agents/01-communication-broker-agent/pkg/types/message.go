package types

import (
	"time"
)

// MessageType defines the type of message
type MessageType string

const (
	MessageTypeRequest      MessageType = "request"
	MessageTypeResponse     MessageType = "response"
	MessageTypeNotification MessageType = "notification"
	MessageTypeHeartbeat    MessageType = "heartbeat"
	MessageTypeCommand      MessageType = "command"
	MessageTypeEvent        MessageType = "event"
	MessageTypeBroadcast    MessageType = "broadcast"
)

// MessagePriority defines the priority level of a message
type MessagePriority int

const (
	MessagePriorityLow    MessagePriority = 1
	MessagePriorityNormal MessagePriority = 5
	MessagePriorityHigh   MessagePriority = 10
	MessagePriorityUrgent MessagePriority = 15
	MessagePriorityCritical MessagePriority = 20
)

// Protocol defines the communication protocol
type Protocol string

const (
	ProtocolHTTP      Protocol = "http"
	ProtocolGRPC      Protocol = "grpc"
	ProtocolWebSocket Protocol = "websocket"
	ProtocolA2A       Protocol = "a2a" // Agent-to-Agent protocol
	ProtocolKafka     Protocol = "kafka"
)

// MessageStatus defines the status of a message
type MessageStatus string

const (
	MessageStatusPending    MessageStatus = "pending"
	MessageStatusRouting    MessageStatus = "routing"
	MessageStatusDelivered  MessageStatus = "delivered"
	MessageStatusProcessing MessageStatus = "processing"
	MessageStatusCompleted  MessageStatus = "completed"
	MessageStatusFailed     MessageStatus = "failed"
	MessageStatusRetrying   MessageStatus = "retrying"
	MessageStatusExpired    MessageStatus = "expired"
)

// Message represents a message in the communication system
type Message struct {
	// Message identification
	ID          string      `json:"id"`
	CorrelationID string    `json:"correlation_id,omitempty"`
	Type        MessageType `json:"type"`
	
	// Routing information
	FromAgentID string   `json:"from_agent_id"`
	ToAgentID   string   `json:"to_agent_id"`
	From        string   `json:"from"` // Alias for FromAgentID
	To          string   `json:"to"`   // Alias for ToAgentID
	Route       *Route   `json:"route,omitempty"`
	Protocol    Protocol `json:"protocol"`
	
	// Message metadata
	Priority    MessagePriority        `json:"priority"`
	Status      MessageStatus          `json:"status"`
	Timestamp   time.Time              `json:"timestamp"`
	TTL         time.Duration          `json:"ttl,omitempty"`
	Headers     map[string]string      `json:"headers,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	
	// Message content
	Subject string      `json:"subject,omitempty"`
	Body    interface{} `json:"body"`
	Payload interface{} `json:"payload"` // Alias for Body
	
	// Routing and delivery tracking
	Hops         []string  `json:"hops,omitempty"`
	DeliveryTime time.Time `json:"delivery_time,omitempty"`
	RetryCount   int       `json:"retry_count"`
	MaxRetries   int       `json:"max_retries"`
	
	// AI routing decision
	RoutingDecision *RoutingDecision `json:"routing_decision,omitempty"`
}

// MessageResponse represents a response to a message
type MessageResponse struct {
	MessageID    string                 `json:"message_id"`
	Status       MessageStatus          `json:"status"`
	Success      bool                   `json:"success"`
	Result       interface{}            `json:"result,omitempty"`
	Error        string                 `json:"error,omitempty"`
	ProcessingTime time.Duration        `json:"processing_time"`
	Timestamp    time.Time              `json:"timestamp"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// Route represents a communication route between agents
type Route struct {
	ID            string                 `json:"id"`
	FromAgentID   string                 `json:"from_agent_id"`
	ToAgentID     string                 `json:"to_agent_id"`
	Destination   string                 `json:"destination"`
	Protocol      Protocol               `json:"protocol"`
	Endpoint      string                 `json:"endpoint"`
	Path          []string               `json:"path,omitempty"`
	Cost          float64                `json:"cost"`
	Latency       time.Duration          `json:"latency"`
	Reliability   float64                `json:"reliability"`
	Bandwidth     int64                  `json:"bandwidth"`
	LoadFactor    float64                `json:"load_factor"`
	Health        RouteHealth            `json:"health"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	LastUsed      time.Time              `json:"last_used"`
	UsageCount    int64                  `json:"usage_count"`
	Priority      int                    `json:"priority"`
	LoadBalancer  LoadBalancerConfig     `json:"load_balancer"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// RouteHealth represents the health status of a route
type RouteHealth string

const (
	RouteHealthHealthy   RouteHealth = "healthy"
	RouteHealthDegraded  RouteHealth = "degraded"
	RouteHealthUnhealthy RouteHealth = "unhealthy"
	RouteHealthUnknown   RouteHealth = "unknown"
)

// RoutingDecision represents an AI-powered routing decision
type RoutingDecision struct {
	ID              string                 `json:"id"`
	MessageID       string                 `json:"message_id"`
	SelectedRoute   *Route                 `json:"selected_route"`
	AlternateRoutes []*Route               `json:"alternate_routes,omitempty"`
	Reasoning       string                 `json:"reasoning"`
	Confidence      float64                `json:"confidence"`
	Factors         map[string]float64     `json:"factors"`
	AIProvider      string                 `json:"ai_provider"`
	AIModel         string                 `json:"ai_model"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	Timestamp       time.Time              `json:"timestamp"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// MessageFilter represents criteria for filtering messages
type MessageFilter struct {
	AgentIDs    []string        `json:"agent_ids,omitempty"`
	Types       []MessageType   `json:"types,omitempty"`
	Priorities  []MessagePriority `json:"priorities,omitempty"`
	Protocols   []Protocol      `json:"protocols,omitempty"`
	Statuses    []MessageStatus `json:"statuses,omitempty"`
	StartTime   *time.Time      `json:"start_time,omitempty"`
	EndTime     *time.Time      `json:"end_time,omitempty"`
	Subject     string          `json:"subject,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
}

// MessageStats represents statistics about message processing
type MessageStats struct {
	TotalMessages     int64                          `json:"total_messages"`
	MessagesByType    map[MessageType]int64          `json:"messages_by_type"`
	MessagesByPriority map[MessagePriority]int64     `json:"messages_by_priority"`
	MessagesByProtocol map[Protocol]int64            `json:"messages_by_protocol"`
	MessagesByStatus  map[MessageStatus]int64        `json:"messages_by_status"`
	AverageLatency    time.Duration                  `json:"average_latency"`
	ThroughputPerSecond float64                      `json:"throughput_per_second"`
	ErrorRate         float64                        `json:"error_rate"`
	RetryRate         float64                        `json:"retry_rate"`
	Timestamp         time.Time                      `json:"timestamp"`
}

// RouteStats represents statistics about route usage
type RouteStats struct {
	RouteID         string        `json:"route_id"`
	MessagesRouted  int64         `json:"messages_routed"`
	AverageLatency  time.Duration `json:"average_latency"`
	SuccessRate     float64       `json:"success_rate"`
	ErrorRate       float64       `json:"error_rate"`
	LoadFactor      float64       `json:"load_factor"`
	LastUsed        time.Time     `json:"last_used"`
	Timestamp       time.Time     `json:"timestamp"`
}

// NetworkStats represents overall network statistics
type NetworkStats struct {
	TotalRoutes        int64                    `json:"total_routes"`
	HealthyRoutes      int64                    `json:"healthy_routes"`
	DegradedRoutes     int64                    `json:"degraded_routes"`
	UnhealthyRoutes    int64                    `json:"unhealthy_routes"`
	TotalMessages      int64                    `json:"total_messages"`
	AvgLatency         time.Duration            `json:"avg_latency"`
	AverageLatency     time.Duration            `json:"average_latency"`
	ErrorRate          float64                  `json:"error_rate"`
	ThroughputPerSec   float64                  `json:"throughput_per_sec"`
	ActiveConnections  int64                    `json:"active_connections"`
	NetworkUtilization float64                  `json:"network_utilization"`
	CongestionLevel    float64                  `json:"congestion_level"`
	RouteStats         map[string]*RouteStats   `json:"route_stats"`
	Timestamp          time.Time                `json:"timestamp"`
}

// MessageBatch represents a batch of messages for bulk processing
type MessageBatch struct {
	ID        string     `json:"id"`
	Messages  []*Message `json:"messages"`
	BatchSize int        `json:"batch_size"`
	Timestamp time.Time  `json:"timestamp"`
	Priority  MessagePriority `json:"priority"`
	TTL       time.Duration   `json:"ttl,omitempty"`
}

// ProtocolTranslation represents a protocol translation configuration
type ProtocolTranslation struct {
	ID           string                 `json:"id"`
	FromProtocol Protocol               `json:"from_protocol"`
	ToProtocol   Protocol               `json:"to_protocol"`
	Transformer  string                 `json:"transformer"`
	Configuration map[string]interface{} `json:"configuration"`
	Enabled      bool                   `json:"enabled"`
	Priority     int                    `json:"priority"`
}

// LoadBalancingStrategy represents a load balancing strategy
type LoadBalancingStrategy string

const (
	LoadBalancingRoundRobin     LoadBalancingStrategy = "round_robin"
	LoadBalancingWeightedRandom LoadBalancingStrategy = "weighted_random"
	LoadBalancingLeastConnections LoadBalancingStrategy = "least_connections"
	LoadBalancingLatencyBased   LoadBalancingStrategy = "latency_based"
	LoadBalancingAIOptimized    LoadBalancingStrategy = "ai_optimized"
)

// LoadBalancingConfig represents load balancing configuration
type LoadBalancingConfig struct {
	Strategy    LoadBalancingStrategy  `json:"strategy"`
	Algorithm   string                 `json:"algorithm"`
	Weights     map[string]float64     `json:"weights,omitempty"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
	Enabled     bool                   `json:"enabled"`
	HealthCheck HealthCheckConfig      `json:"health_check"`
}

// QueueConfig represents message queue configuration
type QueueConfig struct {
	Name         string        `json:"name"`
	MaxSize      int64         `json:"max_size"`
	TTL          time.Duration `json:"ttl"`
	Priority     bool          `json:"priority"`
	Persistent   bool          `json:"persistent"`
	Partitioned  bool          `json:"partitioned"`
	Partitions   int           `json:"partitions"`
	ReplicationFactor int      `json:"replication_factor"`
}

// RouteInfo contains routing information for message delivery (alias for Route)
type RouteInfo = Route

// NetworkStatistics contains network performance statistics (alias for NetworkStats)
type NetworkStatistics = NetworkStats

// LoadBalancerConfig contains load balancer configuration (alias for LoadBalancingConfig)
type LoadBalancerConfig = LoadBalancingConfig

// HealthCheckConfig contains health check configuration
type HealthCheckConfig struct {
	Enabled  bool          `json:"enabled"`
	Interval time.Duration `json:"interval"`
	Timeout  time.Duration `json:"timeout"`
}

// CapabilityEndpoint defines an endpoint for agent capabilities
type CapabilityEndpoint struct {
	Method      string `json:"method"`
	Path        string `json:"path"`
	Description string `json:"description"`
}