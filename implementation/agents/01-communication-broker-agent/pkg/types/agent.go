package types

import (
	"context"
	"time"
)

// AgentIntelligenceLevel defines the intelligence level of an agent
type AgentIntelligenceLevel string

const (
	AgentIntelligenceLow       AgentIntelligenceLevel = "low"
	AgentIntelligenceMedium    AgentIntelligenceLevel = "medium"
	AgentIntelligenceHigh      AgentIntelligenceLevel = "high"
	AgentIntelligenceMediumHigh AgentIntelligenceLevel = "medium-high"
	AgentIntelligenceVeryHigh  AgentIntelligenceLevel = "very-high"
	AgentIntelligenceSupreme   AgentIntelligenceLevel = "supreme"
)

// AgentStatus defines the current status of an agent
type AgentStatus string

const (
	AgentStatusInitializing AgentStatus = "initializing"
	AgentStatusStarting     AgentStatus = "starting"
	AgentStatusRunning      AgentStatus = "running"
	AgentStatusStopping     AgentStatus = "stopping"
	AgentStatusStopped      AgentStatus = "stopped"
	AgentStatusError        AgentStatus = "error"
	AgentStatusMaintenance  AgentStatus = "maintenance"
)

// PlatformAgent defines the core interface that all platform agents must implement
type PlatformAgent interface {
	// Core agent lifecycle methods
	Initialize(ctx context.Context) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Shutdown(ctx context.Context) error
	
	// Agent information methods
	GetID() string
	GetName() string
	GetDescription() string
	GetIntelligenceLevel() AgentIntelligenceLevel
	GetStatus() AgentStatus
	GetVersion() string
	
	// Health and monitoring methods
	HealthCheck(ctx context.Context) (*HealthStatus, error)
	GetMetrics(ctx context.Context) (*AgentMetrics, error)
	
	// Communication methods
	ProcessMessage(ctx context.Context, msg *Message) (*MessageResponse, error)
	SendMessage(ctx context.Context, targetAgentID string, msg *Message) error
	
	// AI integration methods
	MakeIntelligentDecision(ctx context.Context, context *DecisionContext, options []*DecisionOption) (*AgentDecision, error)
	
	// Collaboration methods
	CollaborateWithAgent(ctx context.Context, targetAgentID string, request *CollaborationRequest) (*CollaborationResponse, error)
	
	// Self-management methods
	SelfHeal(ctx context.Context, issue *Issue) error
	SelfOptimize(ctx context.Context) (*OptimizationResult, error)
}

// AgentInfo contains basic information about an agent
type AgentInfo struct {
	ID                string                 `json:"id"`
	Name              string                 `json:"name"`
	Description       string                 `json:"description"`
	Version           string                 `json:"version"`
	IntelligenceLevel AgentIntelligenceLevel `json:"intelligence_level"`
	Status            AgentStatus            `json:"status"`
	StartTime         time.Time              `json:"start_time"`
	LastHeartbeat     time.Time              `json:"last_heartbeat"`
	Capabilities      []string               `json:"capabilities"`
	Endpoints         map[string]string      `json:"endpoints"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// HealthStatus represents the health status of an agent
type HealthStatus struct {
	AgentID      string                 `json:"agent_id"`
	Status       string                 `json:"status"`
	Healthy      bool                   `json:"healthy"`
	Timestamp    time.Time              `json:"timestamp"`
	Uptime       time.Duration          `json:"uptime"`
	Version      string                 `json:"version"`
	Dependencies map[string]bool        `json:"dependencies"`
	Checks       map[string]interface{} `json:"checks"`
	Details      map[string]interface{} `json:"details"`
	Message      string                 `json:"message,omitempty"`
}

// AgentMetrics represents performance metrics of an agent
type AgentMetrics struct {
	AgentID              string                 `json:"agent_id"`
	Timestamp            time.Time              `json:"timestamp"`
	MessagesProcessed    int64                  `json:"messages_processed"`
	MessagesPerSecond    float64                `json:"messages_per_second"`
	AverageResponseTime  time.Duration          `json:"average_response_time"`
	ResponseTime         time.Duration          `json:"response_time"`
	ErrorRate            float64                `json:"error_rate"`
	CPUUsage             float64                `json:"cpu_usage"`
	MemoryUsage          int64                  `json:"memory_usage"`
	ActiveConnections    int64                  `json:"active_connections"`
	QueueDepth           int64                  `json:"queue_depth"`
	AIDecisions          int64                  `json:"ai_decisions"`
	AIDecisionAccuracy   float64                `json:"ai_decision_accuracy"`
	AIResponseTime       time.Duration          `json:"ai_response_time"`
	CustomMetrics        map[string]interface{} `json:"custom_metrics"`
}

// DecisionContext provides context for AI decision-making
type DecisionContext struct {
	AgentID     string                 `json:"agent_id"`
	RequestID   string                 `json:"request_id"`
	Timestamp   time.Time              `json:"timestamp"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Context     map[string]interface{} `json:"context"`
	Constraints map[string]interface{} `json:"constraints"`
	Priority    int                    `json:"priority"`
	Deadline    *time.Time             `json:"deadline,omitempty"`
}

// DecisionOption represents an option for AI decision-making
type DecisionOption struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Cost        float64                `json:"cost"`
	Benefit     float64                `json:"benefit"`
	Risk        float64                `json:"risk"`
	Confidence  float64                `json:"confidence"`
}

// AgentDecision represents a decision made by an agent
type AgentDecision struct {
	ID              string                 `json:"id"`
	AgentID         string                 `json:"agent_id"`
	Context         *DecisionContext       `json:"context"`
	Options         []*DecisionOption      `json:"options"`
	SelectedOption  *DecisionOption        `json:"selected_option"`
	Reasoning       string                 `json:"reasoning"`
	Confidence      float64                `json:"confidence"`
	Timestamp       time.Time              `json:"timestamp"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	AIProvider      string                 `json:"ai_provider"`
	AIModel         string                 `json:"ai_model"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// CollaborationRequest represents a request for collaboration between agents
type CollaborationRequest struct {
	ID            string                 `json:"id"`
	FromAgentID   string                 `json:"from_agent_id"`
	ToAgentID     string                 `json:"to_agent_id"`
	Type          string                 `json:"type"`
	Description   string                 `json:"description"`
	Parameters    map[string]interface{} `json:"parameters"`
	Priority      int                    `json:"priority"`
	Deadline      *time.Time             `json:"deadline,omitempty"`
	Timestamp     time.Time              `json:"timestamp"`
}

// CollaborationResponse represents a response to a collaboration request
type CollaborationResponse struct {
	ID            string                 `json:"id"`
	RequestID     string                 `json:"request_id"`
	FromAgentID   string                 `json:"from_agent_id"`
	ToAgentID     string                 `json:"to_agent_id"`
	Success       bool                   `json:"success"`
	Result        map[string]interface{} `json:"result"`
	Error         string                 `json:"error,omitempty"`
	ProcessingTime time.Duration         `json:"processing_time"`
	Timestamp     time.Time              `json:"timestamp"`
}

// Issue represents an issue that needs to be resolved
type Issue struct {
	ID          string                 `json:"id"`
	AgentID     string                 `json:"agent_id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Description string                 `json:"description"`
	Context     map[string]interface{} `json:"context"`
	Timestamp   time.Time              `json:"timestamp"`
	Resolved    bool                   `json:"resolved"`
	Resolution  string                 `json:"resolution,omitempty"`
}

// OptimizationResult represents the result of a self-optimization process
type OptimizationResult struct {
	ID              string                 `json:"id"`
	AgentID         string                 `json:"agent_id"`
	Type            string                 `json:"type"`
	Description     string                 `json:"description"`
	Improvements    []string               `json:"improvements"`
	PerformanceGain float64                `json:"performance_gain"`
	ResourceSaving  float64                `json:"resource_saving"`
	Timestamp       time.Time              `json:"timestamp"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// AgentCapability represents a capability that an agent provides
type AgentCapability struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Version     string                 `json:"version"`
	AIEnhanced  bool                   `json:"ai_enhanced"`
	Parameters  map[string]interface{} `json:"parameters"`
	Metrics     map[string]float64     `json:"metrics"`
	Endpoints   []CapabilityEndpoint   `json:"endpoints"`
}

// Add type aliases for compatibility
type IntelligenceLevel = AgentIntelligenceLevel

const (
	IntelligenceLevelBasic     = AgentIntelligenceLow
	IntelligenceLevelAdvanced  = AgentIntelligenceHigh
	IntelligenceLevelSupreme   = AgentIntelligenceSupreme
)