package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/agent"
	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/internal/server"
	"github.com/sirupsen/logrus"
)

const (
	// ApplicationName defines the name of this agent
	ApplicationName = "communication-broker-agent"
	// Version defines the current version
	Version = "1.0.0"
	// AgentID is the unique identifier for this agent instance
	AgentID = "CBA-001"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetLevel(logrus.InfoLevel)
	
	logger.WithFields(logrus.Fields{
		"application": ApplicationName,
		"version":     Version,
		"agent_id":    AgentID,
	}).Info("Starting Communication Broker Agent")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Fatal("Failed to load configuration")
	}

	// Set log level from config
	if level, err := logrus.ParseLevel(cfg.LogLevel); err == nil {
		logger.SetLevel(level)
	}

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize the Communication Broker Agent
	cba := agent.NewCommunicationBrokerAgent(cfg)

	// Start the agent
	if err := cba.Start(); err != nil {
		logger.WithError(err).Fatal("Failed to start Communication Broker Agent")
	}

	// Create and start the HTTP server
	httpServer := server.NewHTTPServer(cfg, cba)
	
	// Start HTTP server in a goroutine
	go func() {
		logger.WithField("port", cfg.Server.HTTPPort).Info("Starting HTTP server")
		if err := httpServer.Start(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("HTTP server failed")
		}
	}()

	// Create and start the gRPC server
	grpcServer, err := server.NewGRPCServer(cfg, cba)
	if err != nil {
		logger.WithError(err).Fatal("Failed to create gRPC server")
	}
	
	// Start gRPC server in a goroutine
	go func() {
		logger.WithField("port", cfg.Server.GRPCPort).Info("Starting gRPC server")
		if err := grpcServer.Start(); err != nil {
			logger.WithError(err).Fatal("gRPC server failed")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Block until we receive a signal
	sig := <-sigChan
	logger.WithField("signal", sig).Info("Received shutdown signal")

	// Create a deadline for shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Shutdown servers gracefully
	logger.Info("Shutting down servers...")

	// Shutdown HTTP server
	if err := httpServer.Stop(shutdownCtx); err != nil {
		logger.WithError(err).Error("HTTP server shutdown failed")
	}

	// Shutdown gRPC server
	grpcServer.Stop()

	// Shutdown the agent
	if err := cba.Stop(); err != nil {
		logger.WithError(err).Error("Agent shutdown failed")
	}

	// Cancel the main context
	cancel()

	logger.Info("Communication Broker Agent stopped")
}

// printBanner prints the application banner
func printBanner() {
	banner := fmt.Sprintf(`
╔════════════════════════════════════════════════════════════════╗
║                    TwoDot.ai Platform                          ║
║               Communication Broker Agent (CBA)                 ║
║                                                                ║
║  Agent ID: %s                                       ║
║  Version:  %s                                             ║
║  Package:  ai.twodot.com/platform/agents/communication-broker ║
╚════════════════════════════════════════════════════════════════╝
`, AgentID, Version)
	
	fmt.Print(banner)
}