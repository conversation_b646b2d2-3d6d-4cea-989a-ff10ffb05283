# Communication Broker Agent - Build System
.PHONY: build test clean run docker-build docker-run help

# Variables
BINARY_NAME=cba
DOCKER_IMAGE=ai-platform/communication-broker-agent
VERSION=1.0.0
GO_VERSION=1.21

# Default target
all: test build

# Build the application
build:
	@echo "Building $(BINARY_NAME)..."
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) ./cmd/server
	@echo "Build complete: bin/$(BINARY_NAME)"

# Run tests
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Tests complete. Coverage report: coverage.html"

# Run linting
lint:
	@echo "Running linter..."
	golangci-lint run
	@echo "Linting complete"

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -rf bin/
	rm -f coverage.out coverage.html
	docker rmi $(DOCKER_IMAGE):$(VERSION) 2>/dev/null || true
	@echo "Clean complete"

# Run the application locally
run:
	@echo "Running $(BINARY_NAME)..."
	go run ./cmd/server

# Run with specific configuration
run-dev:
	@echo "Running $(BINARY_NAME) in development mode..."
	CBA_SERVER_ENVIRONMENT=development \
	CBA_SERVER_HTTP_PORT=8080 \
	CBA_SERVER_GRPC_PORT=50051 \
	CBA_SERVER_WEBSOCKET_PORT=8081 \
	CBA_AI_ENABLED=true \
	go run ./cmd/server

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE):$(VERSION) .
	docker tag $(DOCKER_IMAGE):$(VERSION) $(DOCKER_IMAGE):latest
	@echo "Docker image built: $(DOCKER_IMAGE):$(VERSION)"

# Run Docker container
docker-run:
	@echo "Running Docker container..."
	docker run -d \
		--name cba-container \
		-p 8080:8080 \
		-p 50051:50051 \
		-p 8081:8081 \
		-e CBA_SERVER_ENVIRONMENT=development \
		-e CBA_AI_ENABLED=true \
		$(DOCKER_IMAGE):$(VERSION)
	@echo "Container started: cba-container"

# Stop Docker container
docker-stop:
	@echo "Stopping Docker container..."
	docker stop cba-container || true
	docker rm cba-container || true
	@echo "Container stopped"

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod tidy
	go mod download
	@echo "Dependencies installed"

# Generate code (if needed)
generate:
	@echo "Generating code..."
	go generate ./...
	@echo "Code generation complete"

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...
	@echo "Code formatting complete"

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...
	@echo "Code vetting complete"

# Security scan
security:
	@echo "Running security scan..."
	gosec ./...
	@echo "Security scan complete"

# Full CI pipeline
ci: deps fmt vet lint security test build
	@echo "CI pipeline complete"

# Development setup
setup-dev:
	@echo "Setting up development environment..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@echo "Development environment setup complete"

# Help
help:
	@echo "Available targets:"
	@echo "  build       - Build the application"
	@echo "  test        - Run tests with coverage"
	@echo "  lint        - Run linter"
	@echo "  clean       - Clean build artifacts"
	@echo "  run         - Run application locally"
	@echo "  run-dev     - Run with development configuration"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run  - Run Docker container"
	@echo "  docker-stop - Stop Docker container"
	@echo "  deps        - Install dependencies"
	@echo "  generate    - Generate code"
	@echo "  fmt         - Format code"
	@echo "  vet         - Vet code"
	@echo "  security    - Run security scan"
	@echo "  ci          - Full CI pipeline"
	@echo "  setup-dev   - Setup development environment"
	@echo "  help        - Show this help"