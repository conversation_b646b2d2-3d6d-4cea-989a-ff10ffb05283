name: Communication Broker Agent CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.21]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: make deps

    - name: Format check
      run: |
        make fmt
        git diff --exit-code

    - name: Vet
      run: make vet

    - name: Lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest

    - name: Security scan
      run: make security

    - name: Run tests
      run: make test

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella

  build:
    runs-on: ubuntu-latest
    needs: test

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21

    - name: Build application
      run: make build

    - name: Build Docker image
      run: make docker-build

    - name: Test Docker image
      run: |
        docker run --rm -d --name test-cba -p 8080:8080 ai-platform/communication-broker-agent:latest
        sleep 10
        curl -f http://localhost:8080/health || exit 1
        docker stop test-cba

  deploy:
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy placeholder
      run: |
        echo "Deployment would happen here"
        echo "Tagged as: v$(date +%Y%m%d-%H%M%S)"