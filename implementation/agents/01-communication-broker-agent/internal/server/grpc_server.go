package server

import (
	"context"
	"fmt"
	"net"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	"ai.twodot.com/platform/agents/communication-broker/internal/agent"
	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// GRPCServer handles gRPC requests for the CBA
type GRPCServer struct {
	server     *grpc.Server
	listener   net.Listener
	cba        *agent.CommunicationBrokerAgent
	config     *config.Config
	healthSrv  *health.Server
}

// AgentService implements the gRPC agent service
type AgentService struct {
	cba *agent.CommunicationBrokerAgent
}

// NewGRPCServer creates a new gRPC server instance
func NewGRPCServer(cfg *config.Config, cba *agent.CommunicationBrokerAgent) (*GRPCServer, error) {
	// Create listener
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Server.GRPCPort))
	if err != nil {
		return nil, fmt.Errorf("failed to listen on gRPC port %d: %w", cfg.Server.GRPCPort, err)
	}
	
	// Create gRPC server with options
	opts := []grpc.ServerOption{
		grpc.ConnectionTimeout(time.Duration(cfg.Server.ReadTimeout) * time.Second),
		grpc.MaxRecvMsgSize(4 * 1024 * 1024), // 4MB
		grpc.MaxSendMsgSize(4 * 1024 * 1024), // 4MB
	}
	
	server := grpc.NewServer(opts...)
	
	// Create health server
	healthSrv := health.NewServer()
	grpc_health_v1.RegisterHealthServer(server, healthSrv)
	
	// Register agent service
	agentService := &AgentService{cba: cba}
	// Note: In production, this would register with generated protobuf service
	// RegisterAgentServiceServer(server, agentService)
	
	// Enable reflection for development
	if cfg.Server.Environment != "production" {
		reflection.Register(server)
	}
	
	grpcServer := &GRPCServer{
		server:    server,
		listener:  lis,
		cba:       cba,
		config:    cfg,
		healthSrv: healthSrv,
	}
	
	return grpcServer, nil
}

// Start starts the gRPC server
func (s *GRPCServer) Start() error {
	// Set health status to serving
	s.healthSrv.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)
	
	return s.server.Serve(s.listener)
}

// Stop stops the gRPC server
func (s *GRPCServer) Stop() {
	// Set health status to not serving
	s.healthSrv.SetServingStatus("", grpc_health_v1.HealthCheckResponse_NOT_SERVING)
	
	// Graceful stop
	s.server.GracefulStop()
}

// GetInfo returns agent information
func (as *AgentService) GetInfo(ctx context.Context, req interface{}) (interface{}, error) {
	// Placeholder implementation
	// In production, this would use generated protobuf types
	info := map[string]interface{}{
		"id":      as.cba.GetID(),
		"name":    as.cba.GetName(),
		"version": as.cba.GetVersion(),
		"status":  as.cba.GetStatus(),
	}
	
	return info, nil
}

// GetCapabilities returns agent capabilities
func (as *AgentService) GetCapabilities(ctx context.Context, req interface{}) (interface{}, error) {
	capabilities := as.cba.GetCapabilities()
	return map[string]interface{}{"capabilities": capabilities}, nil
}

// GetHealth returns agent health status
func (as *AgentService) GetHealth(ctx context.Context, req interface{}) (interface{}, error) {
	health := as.cba.GetHealth()
	return health, nil
}

// ProcessMessage processes a message through the agent
func (as *AgentService) ProcessMessage(ctx context.Context, msg interface{}) (interface{}, error) {
	// Placeholder implementation
	// In production, this would convert from protobuf message to internal types
	
	// For now, create a dummy message for demonstration
	dummyMsg := &types.Message{
		ID:        fmt.Sprintf("grpc-msg-%d", time.Now().UnixNano()),
		Type:      types.MessageTypeRequest,
		From:      "grpc-client",
		To:        "target-agent",
		Payload:   map[string]interface{}{"data": "grpc message"},
		Timestamp: time.Now(),
		Headers: map[string]string{
			"protocol": "grpc",
		},
	}
	
	response, err := as.cba.ProcessMessage(dummyMsg)
	if err != nil {
		return nil, err
	}
	
	return response, nil
}

// RouteMessage routes a message through the broker
func (as *AgentService) RouteMessage(ctx context.Context, msg interface{}) (interface{}, error) {
	// Similar to ProcessMessage but specifically for routing
	return as.ProcessMessage(ctx, msg)
}

// AddRoute adds a new route to the routing table
func (as *AgentService) AddRoute(ctx context.Context, route interface{}) (interface{}, error) {
	// Placeholder implementation
	// In production, this would convert from protobuf route to internal types
	
	dummyRoute := &types.RouteInfo{
		ID:          fmt.Sprintf("grpc-route-%d", time.Now().UnixNano()),
		Destination: "grpc-destination",
		Protocol:    "grpc",
		Endpoint:    "localhost:50051",
		Priority:    1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	as.cba.AddRoute(dummyRoute)
	
	return map[string]interface{}{
		"message": "Route added successfully",
		"route":   dummyRoute,
	}, nil
}

// GetRoutes returns the current routing table
func (as *AgentService) GetRoutes(ctx context.Context, req interface{}) (interface{}, error) {
	routes := as.cba.GetRoutes()
	return map[string]interface{}{"routes": routes}, nil
}

// RemoveRoute removes a route from the routing table
func (as *AgentService) RemoveRoute(ctx context.Context, req interface{}) (interface{}, error) {
	// Placeholder implementation
	// In production, this would extract destination from protobuf request
	
	destination := "placeholder-destination"
	as.cba.RemoveRoute(destination)
	
	return map[string]interface{}{
		"message": "Route removed successfully",
	}, nil
}

// GetNetworkStats returns network statistics
func (as *AgentService) GetNetworkStats(ctx context.Context, req interface{}) (interface{}, error) {
	stats := as.cba.GetNetworkStats()
	return stats, nil
}

// TranslateMessage translates a message between protocols
func (as *AgentService) TranslateMessage(ctx context.Context, req interface{}) (interface{}, error) {
	// Placeholder implementation for protocol translation
	// In production, this would perform actual protocol translation
	
	translatedMsg := &types.Message{
		ID:        fmt.Sprintf("translated-msg-%d", time.Now().UnixNano()),
		Type:      types.MessageTypeRequest,
		From:      "grpc-translator",
		To:        "target-agent",
		Payload:   map[string]interface{}{"translated_data": "grpc to http translation"},
		Timestamp: time.Now(),
		Headers: map[string]string{
			"original_protocol": "grpc",
			"target_protocol":   "http",
			"translation_time":  time.Now().Format(time.RFC3339),
		},
	}
	
	return map[string]interface{}{
		"translated_message": translatedMsg,
	}, nil
}

// GetMetrics returns agent metrics
func (as *AgentService) GetMetrics(ctx context.Context, req interface{}) (interface{}, error) {
	metrics := as.cba.GetMetrics()
	return metrics, nil
}

// Note: In a production implementation, this file would also include:
// 1. Generated protobuf service interfaces and implementations
// 2. Proper type conversions between protobuf and internal types
// 3. Comprehensive error handling with appropriate gRPC status codes
// 4. Authentication and authorization middleware
// 5. Request/response logging and metrics collection
// 6. Circuit breakers and rate limiting
// 7. Connection pooling and load balancing support