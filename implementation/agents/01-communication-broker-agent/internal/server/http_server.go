package server

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/mux"

	"ai.twodot.com/platform/agents/communication-broker/internal/agent"
	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// HTTPServer handles HTTP requests for the CBA
type HTTPServer struct {
	server *http.Server
	router *gin.Engine
	cba    *agent.CommunicationBrokerAgent
	config *config.Config
}

// NewHTTPServer creates a new HTTP server instance
func NewHTTPServer(cfg *config.Config, cba *agent.CommunicationBrokerAgent) *HTTPServer {
	// Set Gin mode based on configuration
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	
	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	})
	
	httpServer := &HTTPServer{
		router: router,
		cba:    cba,
		config: cfg,
	}
	
	// Setup routes
	httpServer.setupRoutes()
	
	// Create HTTP server
	httpServer.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.HTTPPort),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}
	
	return httpServer
}

// setupRoutes configures all HTTP routes
func (s *HTTPServer) setupRoutes() {
	// Health endpoints
	s.router.GET("/health", s.handleHealth)
	s.router.GET("/ready", s.handleReady)
	
	// Agent management endpoints
	api := s.router.Group("/api/v1")
	{
		// Agent info
		api.GET("/agent/info", s.handleAgentInfo)
		api.GET("/agent/capabilities", s.handleAgentCapabilities)
		api.GET("/agent/status", s.handleAgentStatus)
		api.GET("/agent/metrics", s.handleAgentMetrics)
		
		// Message routing
		api.POST("/route", s.handleRouteMessage)
		api.GET("/routes", s.handleGetRoutes)
		api.POST("/routes", s.handleAddRoute)
		api.DELETE("/routes/:destination", s.handleRemoveRoute)
		
		// Protocol translation
		api.POST("/translate", s.handleTranslateMessage)
		api.GET("/protocols", s.handleGetProtocols)
		
		// Network monitoring
		api.GET("/network/stats", s.handleNetworkStats)
		api.GET("/network/health", s.handleNetworkHealth)
	}
}

// Start starts the HTTP server
func (s *HTTPServer) Start() error {
	return s.server.ListenAndServe()
}

// Stop stops the HTTP server
func (s *HTTPServer) Stop(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}

// Health check endpoint
func (s *HTTPServer) handleHealth(c *gin.Context) {
	health := s.cba.GetHealth()
	
	statusCode := http.StatusOK
	if health.Status == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	} else if health.Status == "degraded" {
		statusCode = http.StatusPartialContent
	}
	
	c.JSON(statusCode, health)
}

// Readiness check endpoint
func (s *HTTPServer) handleReady(c *gin.Context) {
	if s.cba.GetStatus() == types.AgentStatusRunning {
		c.JSON(http.StatusOK, gin.H{"status": "ready"})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"status": "not ready"})
	}
}

// Agent info endpoint
func (s *HTTPServer) handleAgentInfo(c *gin.Context) {
	info := gin.H{
		"id":      s.cba.GetID(),
		"name":    s.cba.GetName(),
		"version": s.cba.GetVersion(),
		"status":  s.cba.GetStatus(),
	}
	c.JSON(http.StatusOK, info)
}

// Agent capabilities endpoint
func (s *HTTPServer) handleAgentCapabilities(c *gin.Context) {
	capabilities := s.cba.GetCapabilities()
	c.JSON(http.StatusOK, gin.H{"capabilities": capabilities})
}

// Agent status endpoint
func (s *HTTPServer) handleAgentStatus(c *gin.Context) {
	status := s.cba.GetStatus()
	c.JSON(http.StatusOK, gin.H{"status": status})
}

// Agent metrics endpoint
func (s *HTTPServer) handleAgentMetrics(c *gin.Context) {
	metrics := s.cba.GetMetrics()
	c.JSON(http.StatusOK, metrics)
}

// Route message endpoint
func (s *HTTPServer) handleRouteMessage(c *gin.Context) {
	var msg types.Message
	if err := c.ShouldBindJSON(&msg); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message format", "details": err.Error()})
		return
	}
	
	// Set timestamp if not provided
	if msg.Timestamp.IsZero() {
		msg.Timestamp = time.Now()
	}
	
	// Generate ID if not provided
	if msg.ID == "" {
		msg.ID = fmt.Sprintf("msg-%d", time.Now().UnixNano())
	}
	
	response, err := s.cba.ProcessMessage(&msg)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to route message", "details": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, response)
}

// Get routes endpoint
func (s *HTTPServer) handleGetRoutes(c *gin.Context) {
	routes := s.cba.GetRoutes()
	c.JSON(http.StatusOK, gin.H{"routes": routes})
}

// Add route endpoint
func (s *HTTPServer) handleAddRoute(c *gin.Context) {
	var route types.RouteInfo
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid route format", "details": err.Error()})
		return
	}
	
	// Set timestamps
	route.CreatedAt = time.Now()
	route.UpdatedAt = time.Now()
	
	// Generate ID if not provided
	if route.ID == "" {
		route.ID = fmt.Sprintf("route-%d", time.Now().UnixNano())
	}
	
	s.cba.AddRoute(&route)
	c.JSON(http.StatusCreated, gin.H{"message": "Route added successfully", "route": route})
}

// Remove route endpoint
func (s *HTTPServer) handleRemoveRoute(c *gin.Context) {
	destination := c.Param("destination")
	if destination == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Destination parameter is required"})
		return
	}
	
	s.cba.RemoveRoute(destination)
	c.JSON(http.StatusOK, gin.H{"message": "Route removed successfully"})
}

// Translate message endpoint
func (s *HTTPServer) handleTranslateMessage(c *gin.Context) {
	var request struct {
		Message      types.Message `json:"message"`
		FromProtocol string        `json:"from_protocol"`
		ToProtocol   string        `json:"to_protocol"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}
	
	// Placeholder for protocol translation
	// In production, this would perform actual protocol translation
	translatedMessage := request.Message
	translatedMessage.Headers["translated_from"] = request.FromProtocol
	translatedMessage.Headers["translated_to"] = request.ToProtocol
	translatedMessage.Headers["translation_timestamp"] = time.Now().Format(time.RFC3339)
	
	c.JSON(http.StatusOK, gin.H{"translated_message": translatedMessage})
}

// Get supported protocols endpoint
func (s *HTTPServer) handleGetProtocols(c *gin.Context) {
	protocols := []string{"http", "grpc", "websocket", "a2a"}
	c.JSON(http.StatusOK, gin.H{"protocols": protocols})
}

// Network statistics endpoint
func (s *HTTPServer) handleNetworkStats(c *gin.Context) {
	stats := s.cba.GetNetworkStats()
	c.JSON(http.StatusOK, stats)
}

// Network health endpoint
func (s *HTTPServer) handleNetworkHealth(c *gin.Context) {
	stats := s.cba.GetNetworkStats()
	
	health := gin.H{
		"status":           "healthy",
		"total_messages":   stats.TotalMessages,
		"error_rate":       stats.ErrorRate,
		"avg_latency_ms":   stats.AvgLatency.Milliseconds(),
		"active_connections": stats.ActiveConnections,
		"timestamp":        stats.Timestamp,
	}
	
	// Determine health status based on metrics
	if stats.ErrorRate > 0.1 { // 10% error rate threshold
		health["status"] = "degraded"
	}
	if stats.ErrorRate > 0.3 { // 30% error rate threshold
		health["status"] = "unhealthy"
	}
	
	statusCode := http.StatusOK
	if health["status"] == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	} else if health["status"] == "degraded" {
		statusCode = http.StatusPartialContent
	}
	
	c.JSON(statusCode, health)
}