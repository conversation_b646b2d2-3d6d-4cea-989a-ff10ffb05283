package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// BaseAgent implements the core PlatformAgent interface
type BaseAgent struct {
	id          string
	name        string
	version     string
	capabilities []types.AgentCapability
	status      types.AgentStatus
	config      *config.Config
	ctx         context.Context
	cancel      context.CancelFunc
	mu          sync.RWMutex
	
	// Lifecycle hooks
	onStart   func() error
	onStop    func() error
	onHealth  func() types.HealthStatus
	onMessage func(msg *types.Message) (*types.MessageResponse, error)
}

// NewBaseAgent creates a new base agent instance
func NewBaseAgent(cfg *config.Config, id, name, version string) *BaseAgent {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &BaseAgent{
		id:      id,
		name:    name,
		version: version,
		config:  cfg,
		ctx:     ctx,
		cancel:  cancel,
		status:  types.AgentStatusStopped,
		capabilities: []types.AgentCapability{
			{
				Name:        "base_lifecycle",
				Version:     "1.0.0",
				Description: "Basic agent lifecycle management",
				Endpoints: []types.CapabilityEndpoint{
					{Method: "GET", Path: "/health", Description: "Health check"},
					{Method: "POST", Path: "/start", Description: "Start agent"},
					{Method: "POST", Path: "/stop", Description: "Stop agent"},
				},
			},
		},
	}
}

// GetID returns the agent ID
func (a *BaseAgent) GetID() string {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.id
}

// GetName returns the agent name
func (a *BaseAgent) GetName() string {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.name
}

// GetVersion returns the agent version
func (a *BaseAgent) GetVersion() string {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.version
}

// GetCapabilities returns the agent capabilities
func (a *BaseAgent) GetCapabilities() []types.AgentCapability {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.capabilities
}

// GetStatus returns the current agent status
func (a *BaseAgent) GetStatus() types.AgentStatus {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.status
}

// Start starts the agent
func (a *BaseAgent) Start(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	if a.status == types.AgentStatusRunning {
		return fmt.Errorf("agent %s is already running", a.id)
	}
	
	a.status = types.AgentStatusStarting
	
	// Call custom start hook if available
	if a.onStart != nil {
		if err := a.onStart(); err != nil {
			a.status = types.AgentStatusError
			return fmt.Errorf("failed to start agent %s: %w", a.id, err)
		}
	}
	
	a.status = types.AgentStatusRunning
	return nil
}

// Stop stops the agent
func (a *BaseAgent) Stop() error {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	if a.status == types.AgentStatusStopped {
		return fmt.Errorf("agent %s is already stopped", a.id)
	}
	
	a.status = types.AgentStatusStopping
	
	// Call custom stop hook if available
	if a.onStop != nil {
		if err := a.onStop(); err != nil {
			a.status = types.AgentStatusError
			return fmt.Errorf("failed to stop agent %s: %w", a.id, err)
		}
	}
	
	// Cancel context to stop all operations
	a.cancel()
	
	a.status = types.AgentStatusStopped
	return nil
}

// GetHealth returns the agent health status
func (a *BaseAgent) GetHealth() types.HealthStatus {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	health := types.HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"agent_id":     a.id,
			"agent_status": a.status,
			"uptime":       time.Since(time.Now()).String(), // Will be properly calculated in production
		},
	}
	
	// Call custom health check if available
	if a.onHealth != nil {
		customHealth := a.onHealth()
		// Merge custom health details
		for k, v := range customHealth.Details {
			health.Details[k] = v
		}
		// Use custom status if more specific
		if customHealth.Status != "" {
			health.Status = customHealth.Status
		}
	}
	
	return health
}

// ProcessMessage processes an incoming message
func (a *BaseAgent) ProcessMessage(msg *types.Message) (*types.MessageResponse, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	if a.status != types.AgentStatusRunning {
		return nil, fmt.Errorf("agent %s is not running (status: %s)", a.id, a.status)
	}
	
	// Call custom message handler if available
	if a.onMessage != nil {
		return a.onMessage(msg)
	}
	
	// Default echo response
	response := &types.MessageResponse{
		MessageID:      msg.ID,
		Status:         types.MessageStatusCompleted,
		Success:        true,
		Result:         map[string]interface{}{"echo": msg.Body},
		ProcessingTime: time.Since(time.Now()),
		Timestamp:      time.Now(),
		Metadata: map[string]interface{}{
			"processed_by": a.id,
			"agent_name":   a.name,
		},
	}
	
	return response, nil
}

// GetMetrics returns agent metrics
func (a *BaseAgent) GetMetrics() types.AgentMetrics {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	return types.AgentMetrics{
		AgentID:           a.id,
		MessagesProcessed: 0, // Will be tracked in production
		ResponseTime:      0, // Will be calculated in production
		ErrorRate:         0, // Will be calculated in production
		CPUUsage:          0, // Will be monitored in production
		MemoryUsage:       0, // Will be monitored in production
		Timestamp:         time.Now(),
	}
}

// SetLifecycleHooks sets custom lifecycle hooks
func (a *BaseAgent) SetLifecycleHooks(
	onStart func() error,
	onStop func() error,
	onHealth func() types.HealthStatus,
	onMessage func(msg *types.Message) (*types.MessageResponse, error),
) {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	a.onStart = onStart
	a.onStop = onStop
	a.onHealth = onHealth
	a.onMessage = onMessage
}

// GetContext returns the agent context
func (a *BaseAgent) GetContext() context.Context {
	return a.ctx
}

// AddCapability adds a new capability to the agent
func (a *BaseAgent) AddCapability(capability types.AgentCapability) {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	a.capabilities = append(a.capabilities, capability)
}

// SetStatus sets the agent status (internal use)
func (a *BaseAgent) SetStatus(status types.AgentStatus) {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	a.status = status
}

// Initialize initializes the agent
func (a *BaseAgent) Initialize(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	if a.status != types.AgentStatusStopped {
		return fmt.Errorf("agent %s is not in stopped state", a.id)
	}
	
	a.status = types.AgentStatusInitializing
	
	// Perform initialization tasks
	if a.onStart != nil {
		if err := a.onStart(); err != nil {
			a.status = types.AgentStatusError
			return fmt.Errorf("failed to initialize agent %s: %w", a.id, err)
		}
	}
	
	a.status = types.AgentStatusStopped
	return nil
}

// Shutdown shuts down the agent
func (a *BaseAgent) Shutdown(ctx context.Context) error {
	return a.Stop()
}

// GetDescription returns the agent description
func (a *BaseAgent) GetDescription() string {
	return fmt.Sprintf("%s v%s - Base agent implementation", a.name, a.version)
}

// GetIntelligenceLevel returns the agent intelligence level
func (a *BaseAgent) GetIntelligenceLevel() types.IntelligenceLevel {
	return types.IntelligenceLevelBasic
}