package agent

import (
	"fmt"
	"sync"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// CommunicationBrokerAgent implements the main CBA functionality
type CommunicationBrokerAgent struct {
	*BaseAgent
	messageQueue    chan *types.Message
	routingTable    map[string]*types.RouteInfo
	networkStats    *types.NetworkStatistics
	aiEnabled       bool
	maxQueueSize    int
	processingStats struct {
		messagesProcessed int64
		totalLatency      time.Duration
		errorCount        int64
		mu                sync.RWMutex
	}
}

// NewCommunicationBrokerAgent creates a new CBA instance
func NewCommunicationBrokerAgent(cfg *config.Config) *CommunicationBrokerAgent {
	base := NewBaseAgent(cfg, "CBA-001", "Communication Broker Agent", "1.0.0")
	
	cba := &CommunicationBrokerAgent{
		BaseAgent:    base,
		messageQueue: make(chan *types.Message, cfg.Infrastructure.MaxConcurrentConnections),
		routingTable: make(map[string]*types.RouteInfo),
		maxQueueSize: cfg.Infrastructure.MaxConcurrentConnections,
		aiEnabled:    cfg.AI.Enabled,
		networkStats: &types.NetworkStatistics{
			TotalMessages:    0,
			AvgLatency:      0,
			ErrorRate:       0,
			ThroughputPerSec: 0,
			ActiveConnections: 0,
			Timestamp:       time.Now(),
		},
	}
	
	// Add CBA-specific capabilities
	cba.AddCapability(types.AgentCapability{
		Name:        "intelligent_routing",
		Version:     "1.0.0",
		Description: "AI-powered message routing and optimization",
		Endpoints: []types.CapabilityEndpoint{
			{Method: "POST", Path: "/route", Description: "Route a message"},
			{Method: "GET", Path: "/routes", Description: "Get routing table"},
			{Method: "POST", Path: "/routes", Description: "Add route"},
			{Method: "DELETE", Path: "/routes/{id}", Description: "Remove route"},
		},
	})
	
	cba.AddCapability(types.AgentCapability{
		Name:        "protocol_translation",
		Version:     "1.0.0",
		Description: "Multi-protocol message translation",
		Endpoints: []types.CapabilityEndpoint{
			{Method: "POST", Path: "/translate", Description: "Translate message protocol"},
			{Method: "GET", Path: "/protocols", Description: "Get supported protocols"},
		},
	})
	
	cba.AddCapability(types.AgentCapability{
		Name:        "network_monitoring",
		Version:     "1.0.0",
		Description: "Real-time network monitoring and analysis",
		Endpoints: []types.CapabilityEndpoint{
			{Method: "GET", Path: "/network/stats", Description: "Get network statistics"},
			{Method: "GET", Path: "/network/health", Description: "Get network health"},
		},
	})
	
	// Set lifecycle hooks
	cba.SetLifecycleHooks(
		cba.startCBA,
		cba.stopCBA,
		cba.healthCheck,
		cba.routeMessage,
	)
	
	return cba
}

// startCBA starts the CBA-specific functionality
func (cba *CommunicationBrokerAgent) startCBA() error {
	// Start message processing goroutine
	go cba.processMessages()
	
	// Start network monitoring
	go cba.monitorNetwork()
	
	// Initialize routing table with default routes
	cba.initializeDefaultRoutes()
	
	return nil
}

// stopCBA stops the CBA-specific functionality
func (cba *CommunicationBrokerAgent) stopCBA() error {
	// Close message queue
	close(cba.messageQueue)
	
	return nil
}

// healthCheck performs CBA-specific health checks
func (cba *CommunicationBrokerAgent) healthCheck() types.HealthStatus {
	cba.processingStats.mu.RLock()
	queueSize := len(cba.messageQueue)
	messagesProcessed := cba.processingStats.messagesProcessed
	errorCount := cba.processingStats.errorCount
	cba.processingStats.mu.RUnlock()
	
	status := "healthy"
	if queueSize > cba.maxQueueSize*8/10 { // 80% threshold
		status = "degraded"
	}
	if queueSize >= cba.maxQueueSize {
		status = "unhealthy"
	}
	
	return types.HealthStatus{
		Status:    status,
		Timestamp: time.Now(),
		Details: map[string]interface{}{
			"queue_size":         queueSize,
			"max_queue_size":     cba.maxQueueSize,
			"messages_processed": messagesProcessed,
			"error_count":        errorCount,
			"ai_enabled":         cba.aiEnabled,
			"active_routes":      len(cba.routingTable),
		},
	}
}

// routeMessage handles incoming message routing
func (cba *CommunicationBrokerAgent) routeMessage(msg *types.Message) (*types.MessageResponse, error) {
	startTime := time.Now()
	
	// Add to processing queue
	select {
	case cba.messageQueue <- msg:
		// Message queued successfully
	default:
		// Queue is full
		cba.updateStats(0, time.Since(startTime), true)
		return nil, fmt.Errorf("message queue is full")
	}
	
	// For now, return immediate acknowledgment
	// In production, this would be handled asynchronously
	response := &types.MessageResponse{
		MessageID:      msg.ID,
		Status:         types.MessageStatusRouting,
		Success:        true,
		Result:         map[string]interface{}{"status": "queued", "message_id": msg.ID, "queue_position": len(cba.messageQueue)},
		ProcessingTime: time.Since(startTime),
		Timestamp:      time.Now(),
		Metadata: map[string]interface{}{
			"acknowledgment": "true",
			"agent_id":       cba.GetID(),
		},
	}
	
	return response, nil
}

// processMessages processes messages from the queue
func (cba *CommunicationBrokerAgent) processMessages() {
	for msg := range cba.messageQueue {
		startTime := time.Now()
		err := cba.processMessage(msg)
		cba.updateStats(1, time.Since(startTime), err != nil)
	}
}

// processMessage processes a single message
func (cba *CommunicationBrokerAgent) processMessage(msg *types.Message) error {
	// Find route for the message
	route, err := cba.findRoute(msg)
	if err != nil {
		return fmt.Errorf("failed to find route for message %s: %w", msg.ID, err)
	}
	
	// Apply AI routing optimization if enabled
	if cba.aiEnabled {
		route = cba.optimizeRoute(msg, route)
	}
	
	// Forward message (placeholder - would integrate with actual transport)
	return cba.forwardMessage(msg, route)
}

// findRoute finds the best route for a message
func (cba *CommunicationBrokerAgent) findRoute(msg *types.Message) (*types.RouteInfo, error) {
	// Look for specific route for the destination
	if route, exists := cba.routingTable[msg.ToAgentID]; exists {
		return route, nil
	}
	
	// Look for wildcard or default routes
	if route, exists := cba.routingTable["*"]; exists {
		return route, nil
	}
	
	return nil, fmt.Errorf("no route found for destination %s", msg.ToAgentID)
}

// optimizeRoute applies AI optimization to route selection
func (cba *CommunicationBrokerAgent) optimizeRoute(msg *types.Message, route *types.RouteInfo) *types.RouteInfo {
	// Placeholder for AI optimization logic
	// In production, this would integrate with Gemini/OpenAI APIs
	// to make intelligent routing decisions based on:
	// - Network conditions
	// - Message priority
	// - Historical performance
	// - Load balancing requirements
	
	return route
}

// forwardMessage forwards a message to its destination
func (cba *CommunicationBrokerAgent) forwardMessage(msg *types.Message, route *types.RouteInfo) error {
	// Placeholder for actual message forwarding
	// In production, this would:
	// - Translate protocols if needed
	// - Handle retries and circuit breaking
	// - Update routing statistics
	// - Log routing decisions
	
	return nil
}

// monitorNetwork monitors network performance
func (cba *CommunicationBrokerAgent) monitorNetwork() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-cba.GetContext().Done():
			return
		case <-ticker.C:
			cba.updateNetworkStats()
		}
	}
}

// updateNetworkStats updates network statistics
func (cba *CommunicationBrokerAgent) updateNetworkStats() {
	cba.processingStats.mu.RLock()
	messagesProcessed := cba.processingStats.messagesProcessed
	totalLatency := cba.processingStats.totalLatency
	errorCount := cba.processingStats.errorCount
	cba.processingStats.mu.RUnlock()
	
	var avgLatency time.Duration
	if messagesProcessed > 0 {
		avgLatency = totalLatency / time.Duration(messagesProcessed)
	}
	
	var errorRate float64
	if messagesProcessed > 0 {
		errorRate = float64(errorCount) / float64(messagesProcessed)
	}
	
	cba.networkStats = &types.NetworkStatistics{
		TotalMessages:     messagesProcessed,
		AvgLatency:       avgLatency,
		ErrorRate:        errorRate,
		ThroughputPerSec: 0, // Would be calculated based on time windows
		ActiveConnections: int64(len(cba.routingTable)),
		Timestamp:        time.Now(),
	}
}

// updateStats updates processing statistics
func (cba *CommunicationBrokerAgent) updateStats(messages int64, latency time.Duration, hasError bool) {
	cba.processingStats.mu.Lock()
	defer cba.processingStats.mu.Unlock()
	
	cba.processingStats.messagesProcessed += messages
	cba.processingStats.totalLatency += latency
	if hasError {
		cba.processingStats.errorCount++
	}
}

// initializeDefaultRoutes sets up default routing table
func (cba *CommunicationBrokerAgent) initializeDefaultRoutes() {
	// Add default wildcard route
	cba.routingTable["*"] = &types.RouteInfo{
		ID:          "default-route",
		Destination: "*",
		Protocol:    "http",
		Endpoint:    "http://localhost:8080",
		Priority:    1,
		LoadBalancer: types.LoadBalancerConfig{
			Algorithm: "round_robin",
			HealthCheck: types.HealthCheckConfig{
				Enabled:  true,
				Interval: 30 * time.Second,
				Timeout:  5 * time.Second,
			},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// AddRoute adds a new route to the routing table
func (cba *CommunicationBrokerAgent) AddRoute(route *types.RouteInfo) {
	cba.routingTable[route.Destination] = route
}

// RemoveRoute removes a route from the routing table
func (cba *CommunicationBrokerAgent) RemoveRoute(destination string) {
	delete(cba.routingTable, destination)
}

// GetRoutes returns the current routing table
func (cba *CommunicationBrokerAgent) GetRoutes() map[string]*types.RouteInfo {
	return cba.routingTable
}

// GetNetworkStats returns current network statistics
func (cba *CommunicationBrokerAgent) GetNetworkStats() *types.NetworkStatistics {
	return cba.networkStats
}