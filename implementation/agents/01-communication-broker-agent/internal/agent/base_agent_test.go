package agent

import (
	"context"
	"testing"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewBaseAgent(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	assert.NotNil(t, agent)
	assert.Equal(t, "test-agent", agent.GetID())
	assert.Equal(t, "Test Agent", agent.GetName())
	assert.Equal(t, "1.0.0", agent.GetVersion())
	assert.Equal(t, types.AgentStatusStopped, agent.GetStatus())
	assert.NotEmpty(t, agent.GetCapabilities())
}

func TestBaseAgentLifecycle(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	// Test starting the agent
	ctx := context.Background()
	err := agent.Start(ctx)
	require.NoError(t, err)
	assert.Equal(t, types.AgentStatusRunning, agent.GetStatus())
	
	// Test starting an already running agent
	err = agent.Start()
	assert.Error(t, err)
	
	// Test stopping the agent
	err = agent.Stop()
	require.NoError(t, err)
	assert.Equal(t, types.AgentStatusStopped, agent.GetStatus())
	
	// Test stopping an already stopped agent
	err = agent.Stop()
	assert.Error(t, err)
}

func TestBaseAgentHealth(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	health := agent.GetHealth()
	assert.Equal(t, "healthy", health.Status)
	assert.NotZero(t, health.Timestamp)
	assert.Contains(t, health.Details, "agent_id")
	assert.Equal(t, "test-agent", health.Details["agent_id"])
}

func TestBaseAgentProcessMessage(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	// Start the agent
	ctx := context.Background()
	err := agent.Start(ctx)
	require.NoError(t, err)
	
	// Create a test message
	msg := &types.Message{
		ID:        "test-msg-1",
		Type:      types.MessageTypeRequest,
		From:      "sender",
		To:        "test-agent",
		Payload:   map[string]interface{}{"test": "data"},
		Timestamp: time.Now(),
		Headers:   map[string]string{"test": "header"},
	}
	
	// Process the message
	response, err := agent.ProcessMessage(msg)
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, types.MessageTypeResponse, response.Type)
	assert.Equal(t, "test-agent", response.From)
	assert.Equal(t, "sender", response.To)
	assert.Contains(t, response.Headers, "processed_by")
}

func TestBaseAgentProcessMessageWhenStopped(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	// Create a test message
	msg := &types.Message{
		ID:   "test-msg-1",
		Type: types.MessageTypeRequest,
		From: "sender",
		To:   "test-agent",
	}
	
	// Try to process message when agent is stopped
	response, err := agent.ProcessMessage(msg)
	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), "not running")
}

func TestBaseAgentMetrics(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	metrics := agent.GetMetrics()
	assert.Equal(t, "test-agent", metrics.AgentID)
	assert.NotZero(t, metrics.Timestamp)
}

func TestBaseAgentCapabilities(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	// Test default capabilities
	capabilities := agent.GetCapabilities()
	assert.Len(t, capabilities, 1)
	assert.Equal(t, "base_lifecycle", capabilities[0].Name)
	
	// Test adding a new capability
	newCapability := types.AgentCapability{
		Name:        "test_capability",
		Version:     "1.0.0",
		Description: "Test capability",
	}
	
	agent.AddCapability(newCapability)
	capabilities = agent.GetCapabilities()
	assert.Len(t, capabilities, 2)
	assert.Equal(t, "test_capability", capabilities[1].Name)
}

func TestBaseAgentLifecycleHooks(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Environment: "test",
		},
	}
	
	agent := NewBaseAgent(cfg, "test-agent", "Test Agent", "1.0.0")
	
	var startCalled, stopCalled, healthCalled, messageCalled bool
	
	// Set lifecycle hooks
	agent.SetLifecycleHooks(
		func() error {
			startCalled = true
			return nil
		},
		func() error {
			stopCalled = true
			return nil
		},
		func() types.HealthStatus {
			healthCalled = true
			return types.HealthStatus{Status: "custom_healthy"}
		},
		func(msg *types.Message) (*types.Message, error) {
			messageCalled = true
			return &types.Message{ID: "custom_response"}, nil
		},
	)
	
	// Test start hook
	ctx := context.Background()
	err := agent.Start(ctx)
	require.NoError(t, err)
	assert.True(t, startCalled)
	
	// Test health hook
	health := agent.GetHealth()
	assert.True(t, healthCalled)
	assert.Equal(t, "custom_healthy", health.Status)
	
	// Test message hook
	msg := &types.Message{ID: "test"}
	response, err := agent.ProcessMessage(msg)
	require.NoError(t, err)
	assert.True(t, messageCalled)
	assert.Equal(t, "custom_response", response.ID)
	
	// Test stop hook
	err = agent.Stop()
	require.NoError(t, err)
	assert.True(t, stopCalled)
}