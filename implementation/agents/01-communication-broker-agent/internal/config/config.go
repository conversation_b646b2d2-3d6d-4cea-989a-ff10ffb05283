package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config holds all configuration for the Communication Broker Agent
type Config struct {
	// Agent configuration
	Agent AgentConfig `mapstructure:"agent"`
	
	// Server configurations
	HTTP      HTTPConfig      `mapstructure:"http"`
	GRPC      GRPCConfig      `mapstructure:"grpc"`
	WebSocket WebSocketConfig `mapstructure:"websocket"`
	
	// AI configuration
	AI AIConfig `mapstructure:"ai"`
	
	// Infrastructure configuration
	Infrastructure InfrastructureConfig `mapstructure:"infrastructure"`
	Server         ServerConfig         `mapstructure:"server"`
	Redis          RedisConfig          `mapstructure:"redis"`
	Kafka    KafkaConfig    `mapstructure:"kafka"`
	Etcd     EtcdConfig     `mapstructure:"etcd"`
	Postgres PostgresConfig `mapstructure:"postgres"`
	
	// Monitoring configuration
	Metrics MetricsConfig `mapstructure:"metrics"`
	Health  HealthConfig  `mapstructure:"health"`
	
	// Logging configuration
	LogLevel string `mapstructure:"log_level"`
}

// AgentConfig holds agent-specific configuration
type AgentConfig struct {
	ID                string        `mapstructure:"id"`
	Name              string        `mapstructure:"name"`
	Description       string        `mapstructure:"description"`
	IntelligenceLevel string        `mapstructure:"intelligence_level"`
	HeartbeatInterval time.Duration `mapstructure:"heartbeat_interval"`
	HealthCheckURL    string        `mapstructure:"health_check_url"`
}

// HTTPConfig holds HTTP server configuration
type HTTPConfig struct {
	Port            int           `mapstructure:"port"`
	ReadTimeout     time.Duration `mapstructure:"read_timeout"`
	WriteTimeout    time.Duration `mapstructure:"write_timeout"`
	IdleTimeout     time.Duration `mapstructure:"idle_timeout"`
	ShutdownTimeout time.Duration `mapstructure:"shutdown_timeout"`
}

// GRPCConfig holds gRPC server configuration
type GRPCConfig struct {
	Port                int           `mapstructure:"port"`
	MaxConnectionAge    time.Duration `mapstructure:"max_connection_age"`
	MaxConnectionIdle   time.Duration `mapstructure:"max_connection_idle"`
	ConnectionTimeout   time.Duration `mapstructure:"connection_timeout"`
	KeepAliveTime       time.Duration `mapstructure:"keepalive_time"`
	KeepAliveTimeout    time.Duration `mapstructure:"keepalive_timeout"`
}

// WebSocketConfig holds WebSocket server configuration
type WebSocketConfig struct {
	Port               int           `mapstructure:"port"`
	ReadBufferSize     int           `mapstructure:"read_buffer_size"`
	WriteBufferSize    int           `mapstructure:"write_buffer_size"`
	HandshakeTimeout   time.Duration `mapstructure:"handshake_timeout"`
	PingPeriod         time.Duration `mapstructure:"ping_period"`
	PongWait           time.Duration `mapstructure:"pong_wait"`
	WriteWait          time.Duration `mapstructure:"write_wait"`
}

// AIConfig holds AI integration configuration
type AIConfig struct {
	// AI enabled flag
	Enabled bool `mapstructure:"enabled"`
	
	// Primary AI model configuration
	Primary AIModelConfig `mapstructure:"primary"`
	
	// Fallback AI model configuration
	Fallback AIModelConfig `mapstructure:"fallback"`
	
	// AI providers configuration
	Providers AIProvidersConfig `mapstructure:"providers"`
	
	// AI processing settings
	MaxRetries      int           `mapstructure:"max_retries"`
	RequestTimeout  time.Duration `mapstructure:"request_timeout"`
	RateLimitRPS    int           `mapstructure:"rate_limit_rps"`
	EnableFallback  bool          `mapstructure:"enable_fallback"`
}

// AIProvidersConfig holds configuration for AI providers
type AIProvidersConfig struct {
	GoogleGemini GoogleGeminiConfig `mapstructure:"google_gemini"`
	Gemini       GoogleGeminiConfig `mapstructure:"gemini"`
	OpenAI       OpenAIConfig       `mapstructure:"openai"`
	Claude       AnthropicConfig    `mapstructure:"claude"`
	Anthropic    AnthropicConfig    `mapstructure:"anthropic"`
}

// AIModelConfig holds individual AI model configuration
type AIModelConfig struct {
	Provider    string            `mapstructure:"provider"`
	Model       string            `mapstructure:"model"`
	APIKey      string            `mapstructure:"api_key"`
	APIEndpoint string            `mapstructure:"api_endpoint"`
	Temperature float32           `mapstructure:"temperature"`
	MaxTokens   int               `mapstructure:"max_tokens"`
	Parameters  map[string]string `mapstructure:"parameters"`
}

// AnthropicConfig holds Anthropic Claude configuration
type AnthropicConfig struct {
	APIKey      string        `mapstructure:"api_key"`
	APIEndpoint string        `mapstructure:"api_endpoint"`
	Endpoint    string        `mapstructure:"endpoint"`
	Model       string        `mapstructure:"model"`
	Temperature float32       `mapstructure:"temperature"`
	MaxTokens   int           `mapstructure:"max_tokens"`
	Enabled     bool          `mapstructure:"enabled"`
	Timeout     time.Duration `mapstructure:"timeout"`
}

// GoogleGeminiConfig holds Google Gemini configuration
type GoogleGeminiConfig struct {
	APIKey      string        `mapstructure:"api_key"`
	APIEndpoint string        `mapstructure:"api_endpoint"`
	Endpoint    string        `mapstructure:"endpoint"`
	Model       string        `mapstructure:"model"`
	Temperature float32       `mapstructure:"temperature"`
	MaxTokens   int           `mapstructure:"max_tokens"`
	Enabled     bool          `mapstructure:"enabled"`
	Timeout     time.Duration `mapstructure:"timeout"`
}

// OpenAIConfig holds OpenAI configuration
type OpenAIConfig struct {
	APIKey      string        `mapstructure:"api_key"`
	APIEndpoint string        `mapstructure:"api_endpoint"`
	Endpoint    string        `mapstructure:"endpoint"`
	Model       string        `mapstructure:"model"`
	Temperature float32       `mapstructure:"temperature"`
	MaxTokens   int           `mapstructure:"max_tokens"`
	Enabled     bool          `mapstructure:"enabled"`
	Timeout     time.Duration `mapstructure:"timeout"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Addresses        []string      `mapstructure:"addresses"`
	Username         string        `mapstructure:"username"`
	Password         string        `mapstructure:"password"`
	Database         int           `mapstructure:"database"`
	MaxRetries       int           `mapstructure:"max_retries"`
	DialTimeout      time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout      time.Duration `mapstructure:"read_timeout"`
	WriteTimeout     time.Duration `mapstructure:"write_timeout"`
	PoolSize         int           `mapstructure:"pool_size"`
	PoolTimeout      time.Duration `mapstructure:"pool_timeout"`
	IdleTimeout      time.Duration `mapstructure:"idle_timeout"`
	MaxIdleConns     int           `mapstructure:"max_idle_conns"`
}

// KafkaConfig holds Kafka configuration
type KafkaConfig struct {
	Brokers         []string      `mapstructure:"brokers"`
	ConsumerGroup   string        `mapstructure:"consumer_group"`
	ProducerTimeout time.Duration `mapstructure:"producer_timeout"`
	ConsumerTimeout time.Duration `mapstructure:"consumer_timeout"`
	BatchSize       int           `mapstructure:"batch_size"`
	BatchTimeout    time.Duration `mapstructure:"batch_timeout"`
	RetryMax        int           `mapstructure:"retry_max"`
	Topics          TopicsConfig  `mapstructure:"topics"`
}

// TopicsConfig holds Kafka topic configuration
type TopicsConfig struct {
	Messages    string `mapstructure:"messages"`
	Routing     string `mapstructure:"routing"`
	Health      string `mapstructure:"health"`
	Metrics     string `mapstructure:"metrics"`
	Deadletter  string `mapstructure:"deadletter"`
}

// EtcdConfig holds etcd configuration
type EtcdConfig struct {
	Endpoints   []string      `mapstructure:"endpoints"`
	Username    string        `mapstructure:"username"`
	Password    string        `mapstructure:"password"`
	DialTimeout time.Duration `mapstructure:"dial_timeout"`
	Namespace   string        `mapstructure:"namespace"`
}

// PostgresConfig holds PostgreSQL configuration
type PostgresConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	Username        string        `mapstructure:"username"`
	Password        string        `mapstructure:"password"`
	Database        string        `mapstructure:"database"`
	SSLMode         string        `mapstructure:"ssl_mode"`
	MaxConnections  int           `mapstructure:"max_connections"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
}

// MetricsConfig holds metrics configuration
type MetricsConfig struct {
	Enabled      bool   `mapstructure:"enabled"`
	Port         int    `mapstructure:"port"`
	Path         string `mapstructure:"path"`
	Namespace    string `mapstructure:"namespace"`
	Subsystem    string `mapstructure:"subsystem"`
}

// HealthConfig holds health check configuration
type HealthConfig struct {
	Enabled  bool          `mapstructure:"enabled"`
	Port     int           `mapstructure:"port"`
	Path     string        `mapstructure:"path"`
	Interval time.Duration `mapstructure:"interval"`
	Timeout  time.Duration `mapstructure:"timeout"`
}

// InfrastructureConfig holds infrastructure-related configuration
type InfrastructureConfig struct {
	MaxConcurrentConnections int           `mapstructure:"max_concurrent_connections"`
	ConnectionTimeout        time.Duration `mapstructure:"connection_timeout"`
	MaxRetries              int           `mapstructure:"max_retries"`
	CircuitBreakerThreshold int           `mapstructure:"circuit_breaker_threshold"`
	LoadBalancingEnabled    bool          `mapstructure:"load_balancing_enabled"`
}

// ServerConfig holds general server configuration
type ServerConfig struct {
	HTTPPort      int           `mapstructure:"http_port"`
	GRPCPort      int           `mapstructure:"grpc_port"`
	WebSocketPort int           `mapstructure:"websocket_port"`
	MetricsPort   int           `mapstructure:"metrics_port"`
	HealthPort    int           `mapstructure:"health_port"`
	ReadTimeout   time.Duration `mapstructure:"read_timeout"`
	WriteTimeout  time.Duration `mapstructure:"write_timeout"`
	IdleTimeout   time.Duration `mapstructure:"idle_timeout"`
	Environment   string        `mapstructure:"environment"`
}

// Load loads configuration from various sources
func Load() (*Config, error) {
	v := viper.New()
	
	// Set configuration file name and paths
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath(".")
	v.AddConfigPath("./configs")
	v.AddConfigPath("/etc/twodot/cba")
	v.AddConfigPath("$HOME/.twodot/cba")
	
	// Set default values
	setDefaults(v)
	
	// Enable environment variable support
	v.AutomaticEnv()
	v.SetEnvPrefix("CBA")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	
	// Try to read configuration file
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// Config file not found is not an error - we can use defaults
	}
	
	// Unmarshal configuration
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	
	// Validate configuration
	if err := validate(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}
	
	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults(v *viper.Viper) {
	// Agent defaults
	v.SetDefault("agent.id", "CBA-001")
	v.SetDefault("agent.name", "Communication Broker Agent")
	v.SetDefault("agent.description", "AI-powered message routing and communication management")
	v.SetDefault("agent.intelligence_level", "medium-high")
	v.SetDefault("agent.heartbeat_interval", "30s")
	v.SetDefault("agent.health_check_url", "/health")
	
	// HTTP server defaults
	v.SetDefault("http.port", 8080)
	v.SetDefault("http.read_timeout", "30s")
	v.SetDefault("http.write_timeout", "30s")
	v.SetDefault("http.idle_timeout", "120s")
	v.SetDefault("http.shutdown_timeout", "30s")
	
	// gRPC server defaults
	v.SetDefault("grpc.port", 9090)
	v.SetDefault("grpc.max_connection_age", "30m")
	v.SetDefault("grpc.max_connection_idle", "5m")
	v.SetDefault("grpc.connection_timeout", "10s")
	v.SetDefault("grpc.keepalive_time", "30s")
	v.SetDefault("grpc.keepalive_timeout", "5s")
	
	// WebSocket server defaults
	v.SetDefault("websocket.port", 8081)
	v.SetDefault("websocket.read_buffer_size", 1024)
	v.SetDefault("websocket.write_buffer_size", 1024)
	v.SetDefault("websocket.handshake_timeout", "10s")
	v.SetDefault("websocket.ping_period", "54s")
	v.SetDefault("websocket.pong_wait", "60s")
	v.SetDefault("websocket.write_wait", "10s")
	
	// AI defaults
	v.SetDefault("ai.primary.provider", "google")
	v.SetDefault("ai.primary.model", "gemini-pro")
	v.SetDefault("ai.primary.temperature", 0.3)
	v.SetDefault("ai.primary.max_tokens", 1000)
	v.SetDefault("ai.fallback.provider", "openai")
	v.SetDefault("ai.fallback.model", "gpt-4-turbo")
	v.SetDefault("ai.fallback.temperature", 0.3)
	v.SetDefault("ai.fallback.max_tokens", 1000)
	v.SetDefault("ai.max_retries", 3)
	v.SetDefault("ai.request_timeout", "30s")
	v.SetDefault("ai.rate_limit_rps", 10)
	v.SetDefault("ai.enable_fallback", true)
	
	// Redis defaults
	v.SetDefault("redis.addresses", []string{"localhost:6379"})
	v.SetDefault("redis.database", 0)
	v.SetDefault("redis.max_retries", 3)
	v.SetDefault("redis.dial_timeout", "5s")
	v.SetDefault("redis.read_timeout", "3s")
	v.SetDefault("redis.write_timeout", "3s")
	v.SetDefault("redis.pool_size", 10)
	v.SetDefault("redis.pool_timeout", "4s")
	v.SetDefault("redis.idle_timeout", "5m")
	v.SetDefault("redis.max_idle_conns", 5)
	
	// Kafka defaults
	v.SetDefault("kafka.brokers", []string{"localhost:9092"})
	v.SetDefault("kafka.consumer_group", "cba-consumer-group")
	v.SetDefault("kafka.producer_timeout", "10s")
	v.SetDefault("kafka.consumer_timeout", "10s")
	v.SetDefault("kafka.batch_size", 100)
	v.SetDefault("kafka.batch_timeout", "100ms")
	v.SetDefault("kafka.retry_max", 3)
	v.SetDefault("kafka.topics.messages", "agent-messages")
	v.SetDefault("kafka.topics.routing", "routing-decisions")
	v.SetDefault("kafka.topics.health", "agent-health")
	v.SetDefault("kafka.topics.metrics", "agent-metrics")
	v.SetDefault("kafka.topics.deadletter", "deadletter-queue")
	
	// etcd defaults
	v.SetDefault("etcd.endpoints", []string{"localhost:2379"})
	v.SetDefault("etcd.dial_timeout", "5s")
	v.SetDefault("etcd.namespace", "twodot/cba/")
	
	// PostgreSQL defaults
	v.SetDefault("postgres.host", "localhost")
	v.SetDefault("postgres.port", 5432)
	v.SetDefault("postgres.username", "cba_user")
	v.SetDefault("postgres.database", "cba_metrics")
	v.SetDefault("postgres.ssl_mode", "disable")
	v.SetDefault("postgres.max_connections", 25)
	v.SetDefault("postgres.max_idle_conns", 5)
	v.SetDefault("postgres.conn_max_lifetime", "5m")
	
	// Metrics defaults
	v.SetDefault("metrics.enabled", true)
	v.SetDefault("metrics.port", 8082)
	v.SetDefault("metrics.path", "/metrics")
	v.SetDefault("metrics.namespace", "twodot")
	v.SetDefault("metrics.subsystem", "cba")
	
	// Health defaults
	v.SetDefault("health.enabled", true)
	v.SetDefault("health.port", 8083)
	v.SetDefault("health.path", "/health")
	v.SetDefault("health.interval", "30s")
	v.SetDefault("health.timeout", "10s")
	
	// Infrastructure defaults
	v.SetDefault("infrastructure.max_concurrent_connections", 1000)
	v.SetDefault("infrastructure.connection_timeout", "30s")
	v.SetDefault("infrastructure.max_retries", 3)
	v.SetDefault("infrastructure.circuit_breaker_threshold", 5)
	v.SetDefault("infrastructure.load_balancing_enabled", true)
	
	// Server defaults
	v.SetDefault("server.http_port", 8080)
	v.SetDefault("server.grpc_port", 9090)
	v.SetDefault("server.websocket_port", 8081)
	v.SetDefault("server.metrics_port", 8082)
	v.SetDefault("server.health_port", 8083)
	v.SetDefault("server.read_timeout", "30s")
	v.SetDefault("server.write_timeout", "30s")
	v.SetDefault("server.idle_timeout", "120s")
	v.SetDefault("server.environment", "development")
	
	// AI enabled flag
	v.SetDefault("ai.enabled", true)
	
	// Logging defaults
	v.SetDefault("log_level", "info")
}

// validate validates the configuration
func validate(config *Config) error {
	// Validate agent configuration
	if config.Agent.ID == "" {
		return fmt.Errorf("agent.id cannot be empty")
	}
	
	if config.Agent.Name == "" {
		return fmt.Errorf("agent.name cannot be empty")
	}
	
	// Validate port numbers
	if config.HTTP.Port <= 0 || config.HTTP.Port > 65535 {
		return fmt.Errorf("invalid HTTP port: %d", config.HTTP.Port)
	}
	
	if config.GRPC.Port <= 0 || config.GRPC.Port > 65535 {
		return fmt.Errorf("invalid gRPC port: %d", config.GRPC.Port)
	}
	
	if config.WebSocket.Port <= 0 || config.WebSocket.Port > 65535 {
		return fmt.Errorf("invalid WebSocket port: %d", config.WebSocket.Port)
	}
	
	// Validate AI configuration
	if config.AI.Primary.Provider == "" {
		return fmt.Errorf("ai.primary.provider cannot be empty")
	}
	
	if config.AI.Primary.Model == "" {
		return fmt.Errorf("ai.primary.model cannot be empty")
	}
	
	// Validate Redis configuration
	if len(config.Redis.Addresses) == 0 {
		return fmt.Errorf("redis.addresses cannot be empty")
	}
	
	// Validate Kafka configuration
	if len(config.Kafka.Brokers) == 0 {
		return fmt.Errorf("kafka.brokers cannot be empty")
	}
	
	// Validate etcd configuration
	if len(config.Etcd.Endpoints) == 0 {
		return fmt.Errorf("etcd.endpoints cannot be empty")
	}
	
	return nil
}