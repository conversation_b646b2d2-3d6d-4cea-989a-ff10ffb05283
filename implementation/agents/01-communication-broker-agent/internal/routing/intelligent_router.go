package routing

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// IntelligentRouter implements ML-powered routing decisions
type IntelligentRouter struct {
	config        *config.Config
	routes        map[string]*types.RouteInfo
	routeMetrics  map[string]*RouteMetrics
	mlModel       *MLRoutingModel
	optimizer     *RouteOptimizer
	loadBalancer  *IntelligentLoadBalancer
	mu            sync.RWMutex
	learningRate  float64
	decisionCache map[string]*CachedDecision
}

// RouteMetrics contains comprehensive metrics for each route
type RouteMetrics struct {
	RouteID             string        `json:"route_id"`
	TotalMessages       int64         `json:"total_messages"`
	SuccessfulMessages  int64         `json:"successful_messages"`
	FailedMessages      int64         `json:"failed_messages"`
	AvgLatency         time.Duration `json:"avg_latency"`
	MinLatency         time.Duration `json:"min_latency"`
	MaxLatency         time.Duration `json:"max_latency"`
	ThroughputPerSec   float64       `json:"throughput_per_sec"`
	ErrorRate          float64       `json:"error_rate"`
	HealthScore        float64       `json:"health_score"`
	LoadScore          float64       `json:"load_score"`
	QualityScore       float64       `json:"quality_score"`
	LastUsed           time.Time     `json:"last_used"`
	CreatedAt          time.Time     `json:"created_at"`
	UpdatedAt          time.Time     `json:"updated_at"`
}

// MLRoutingModel implements machine learning for routing decisions
type MLRoutingModel struct {
	weights       map[string]float64 `json:"weights"`
	features      []string           `json:"features"`
	trainingData  []TrainingExample  `json:"training_data"`
	accuracy      float64            `json:"accuracy"`
	lastTrained   time.Time          `json:"last_trained"`
	predictions   int64              `json:"predictions"`
	correctPreds  int64              `json:"correct_predictions"`
}

// TrainingExample represents a training example for the ML model
type TrainingExample struct {
	Features map[string]float64 `json:"features"`
	Label    string             `json:"label"`
	Outcome  float64            `json:"outcome"`
	Timestamp time.Time         `json:"timestamp"`
}

// CachedDecision represents a cached routing decision
type CachedDecision struct {
	Route     string    `json:"route"`
	Score     float64   `json:"score"`
	Timestamp time.Time `json:"timestamp"`
	TTL       time.Duration `json:"ttl"`
}

// NewIntelligentRouter creates a new intelligent router
func NewIntelligentRouter(cfg *config.Config) *IntelligentRouter {
	router := &IntelligentRouter{
		config:        cfg,
		routes:        make(map[string]*types.RouteInfo),
		routeMetrics:  make(map[string]*RouteMetrics),
		mlModel:       NewMLRoutingModel(),
		optimizer:     NewRouteOptimizer(),
		loadBalancer:  NewIntelligentLoadBalancer(),
		learningRate:  0.01,
		decisionCache: make(map[string]*CachedDecision),
	}
	
	return router
}

// RouteIntelligently routes messages using ML-powered decision making
func (ir *IntelligentRouter) RouteIntelligently(ctx context.Context, msg *types.Message) (*types.RouteInfo, error) {
	// Check cache first
	cacheKey := ir.generateCacheKey(msg)
	if cached := ir.getCachedDecision(cacheKey); cached != nil {
		if route, exists := ir.routes[cached.Route]; exists {
			return route, nil
		}
	}
	
	// Extract features from message and context
	features := ir.extractFeatures(msg)
	
	// Get route candidates
	candidates := ir.getRouteCandidates(msg)
	if len(candidates) == 0 {
		return nil, fmt.Errorf("no route candidates available for destination: %s", msg.To)
	}
	
	// Use ML model to score each candidate
	bestRoute, bestScore := ir.selectBestRoute(features, candidates)
	
	// Cache the decision
	ir.cacheDecision(cacheKey, bestRoute, bestScore)
	
	// Update route metrics
	ir.updateRouteUsage(bestRoute)
	
	// Learn from this decision (async)
	go ir.recordDecisionForLearning(features, bestRoute, msg)
	
	route, exists := ir.routes[bestRoute]
	if !exists {
		return nil, fmt.Errorf("selected route %s not found", bestRoute)
	}
	
	return route, nil
}

// AddRoute adds a new route with metrics tracking
func (ir *IntelligentRouter) AddRoute(route *types.RouteInfo) error {
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	ir.routes[route.Destination] = route
	
	// Initialize metrics for the route
	ir.routeMetrics[route.Destination] = &RouteMetrics{
		RouteID:      route.ID,
		HealthScore:  100.0,
		LoadScore:    0.0,
		QualityScore: 100.0,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	// Retrain model with new route
	go ir.retrainModel()
	
	return nil
}

// UpdateRouteMetrics updates metrics for a route based on performance
func (ir *IntelligentRouter) UpdateRouteMetrics(destination string, latency time.Duration, success bool) {
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	metrics, exists := ir.routeMetrics[destination]
	if !exists {
		return
	}
	
	// Update message counts
	metrics.TotalMessages++
	if success {
		metrics.SuccessfulMessages++
	} else {
		metrics.FailedMessages++
	}
	
	// Update latency metrics
	if metrics.TotalMessages == 1 {
		metrics.AvgLatency = latency
		metrics.MinLatency = latency
		metrics.MaxLatency = latency
	} else {
		// Running average
		totalLatency := metrics.AvgLatency * time.Duration(metrics.TotalMessages-1)
		metrics.AvgLatency = (totalLatency + latency) / time.Duration(metrics.TotalMessages)
		
		if latency < metrics.MinLatency {
			metrics.MinLatency = latency
		}
		if latency > metrics.MaxLatency {
			metrics.MaxLatency = latency
		}
	}
	
	// Update error rate
	metrics.ErrorRate = float64(metrics.FailedMessages) / float64(metrics.TotalMessages)
	
	// Update health score based on recent performance
	metrics.HealthScore = ir.calculateHealthScore(metrics)
	
	// Update quality score
	metrics.QualityScore = ir.calculateQualityScore(metrics)
	
	metrics.UpdatedAt = time.Now()
	
	// Add to training data
	go ir.addTrainingExample(destination, metrics, success)
}

// extractFeatures extracts features from message for ML model
func (ir *IntelligentRouter) extractFeatures(msg *types.Message) map[string]float64 {
	features := make(map[string]float64)
	
	// Message type features
	switch msg.Type {
	case types.MessageTypeRequest:
		features["msg_type_request"] = 1.0
	case types.MessageTypeResponse:
		features["msg_type_response"] = 1.0
	case types.MessageTypeEvent:
		features["msg_type_event"] = 1.0
	}
	
	// Priority features
	if priority := msg.Headers["priority"]; priority != "" {
		switch priority {
		case "high":
			features["priority_high"] = 1.0
		case "medium":
			features["priority_medium"] = 1.0
		case "low":
			features["priority_low"] = 1.0
		}
	}
	
	// Protocol features
	if protocol := msg.Headers["protocol"]; protocol != "" {
		features["protocol_"+protocol] = 1.0
	}
	
	// Payload size feature
	if payloadSize := len(fmt.Sprintf("%v", msg.Payload)); payloadSize > 0 {
		features["payload_size"] = float64(payloadSize) / 1024.0 // KB
	}
	
	// Time-based features
	hour := float64(msg.Timestamp.Hour())
	features["hour_sin"] = math.Sin(2 * math.Pi * hour / 24)
	features["hour_cos"] = math.Cos(2 * math.Pi * hour / 24)
	
	// Day of week feature
	dayOfWeek := float64(msg.Timestamp.Weekday())
	features["day_sin"] = math.Sin(2 * math.Pi * dayOfWeek / 7)
	features["day_cos"] = math.Cos(2 * math.Pi * dayOfWeek / 7)
	
	return features
}

// getRouteCandidates gets available route candidates for a message
func (ir *IntelligentRouter) getRouteCandidates(msg *types.Message) []string {
	ir.mu.RLock()
	defer ir.mu.RUnlock()
	
	candidates := make([]string, 0)
	
	// Direct route to destination
	if _, exists := ir.routes[msg.To]; exists {
		candidates = append(candidates, msg.To)
	}
	
	// Wildcard routes
	if _, exists := ir.routes["*"]; exists {
		candidates = append(candidates, "*")
	}
	
	// Protocol-specific routes
	if protocol := msg.Headers["protocol"]; protocol != "" {
		protocolRoute := "protocol:" + protocol
		if _, exists := ir.routes[protocolRoute]; exists {
			candidates = append(candidates, protocolRoute)
		}
	}
	
	return candidates
}

// selectBestRoute uses ML model to select the best route
func (ir *IntelligentRouter) selectBestRoute(features map[string]float64, candidates []string) (string, float64) {
	bestRoute := ""
	bestScore := -1.0
	
	for _, candidate := range candidates {
		score := ir.scoreRoute(features, candidate)
		if score > bestScore {
			bestScore = score
			bestRoute = candidate
		}
	}
	
	// Fallback to first candidate if no good score
	if bestRoute == "" && len(candidates) > 0 {
		bestRoute = candidates[0]
		bestScore = 0.5
	}
	
	return bestRoute, bestScore
}

// scoreRoute scores a route using the ML model
func (ir *IntelligentRouter) scoreRoute(features map[string]float64, routeID string) float64 {
	ir.mu.RLock()
	defer ir.mu.RUnlock()
	
	// Get route metrics
	metrics, exists := ir.routeMetrics[routeID]
	if !exists {
		return 0.0
	}
	
	// Combine static features with route-specific features
	routeFeatures := make(map[string]float64)
	for k, v := range features {
		routeFeatures[k] = v
	}
	
	// Add route-specific features
	routeFeatures["health_score"] = metrics.HealthScore / 100.0
	routeFeatures["error_rate"] = metrics.ErrorRate
	routeFeatures["avg_latency"] = float64(metrics.AvgLatency.Milliseconds()) / 1000.0
	routeFeatures["load_score"] = metrics.LoadScore / 100.0
	routeFeatures["quality_score"] = metrics.QualityScore / 100.0
	
	// Calculate score using ML model
	score := ir.mlModel.predict(routeFeatures)
	
	return score
}

// calculateHealthScore calculates health score for a route
func (ir *IntelligentRouter) calculateHealthScore(metrics *RouteMetrics) float64 {
	if metrics.TotalMessages == 0 {
		return 100.0
	}
	
	baseScore := 100.0
	
	// Penalize high error rates
	baseScore -= metrics.ErrorRate * 50.0
	
	// Penalize high latency
	if metrics.AvgLatency > 500*time.Millisecond {
		latencyPenalty := float64(metrics.AvgLatency.Milliseconds()) / 500.0 * 20.0
		baseScore -= latencyPenalty
	}
	
	// Ensure score is between 0 and 100
	if baseScore < 0 {
		baseScore = 0
	}
	
	return baseScore
}

// calculateQualityScore calculates quality score for a route
func (ir *IntelligentRouter) calculateQualityScore(metrics *RouteMetrics) float64 {
	if metrics.TotalMessages == 0 {
		return 100.0
	}
	
	// Weighted combination of factors
	healthWeight := 0.4
	latencyWeight := 0.3
	throughputWeight := 0.2
	reliabilityWeight := 0.1
	
	healthFactor := metrics.HealthScore / 100.0
	
	latencyFactor := 1.0
	if metrics.AvgLatency > 0 {
		targetLatency := 100.0 // 100ms target
		latencyFactor = math.Max(0, 1.0-(float64(metrics.AvgLatency.Milliseconds())/targetLatency))
	}
	
	throughputFactor := math.Min(1.0, metrics.ThroughputPerSec/1000.0) // Target 1000 msg/sec
	
	reliabilityFactor := 1.0 - metrics.ErrorRate
	
	score := (healthWeight*healthFactor +
		latencyWeight*latencyFactor +
		throughputWeight*throughputFactor +
		reliabilityWeight*reliabilityFactor) * 100.0
	
	return score
}

// Cache management methods
func (ir *IntelligentRouter) generateCacheKey(msg *types.Message) string {
	return fmt.Sprintf("%s:%s:%s", msg.To, msg.Type, msg.Headers["priority"])
}

func (ir *IntelligentRouter) getCachedDecision(key string) *CachedDecision {
	ir.mu.RLock()
	defer ir.mu.RUnlock()
	
	cached, exists := ir.decisionCache[key]
	if !exists {
		return nil
	}
	
	if time.Since(cached.Timestamp) > cached.TTL {
		delete(ir.decisionCache, key)
		return nil
	}
	
	return cached
}

func (ir *IntelligentRouter) cacheDecision(key, route string, score float64) {
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	ir.decisionCache[key] = &CachedDecision{
		Route:     route,
		Score:     score,
		Timestamp: time.Now(),
		TTL:       5 * time.Minute,
	}
}

func (ir *IntelligentRouter) updateRouteUsage(routeID string) {
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	if metrics, exists := ir.routeMetrics[routeID]; exists {
		metrics.LastUsed = time.Now()
	}
}

// ML model methods
func (ir *IntelligentRouter) recordDecisionForLearning(features map[string]float64, selectedRoute string, msg *types.Message) {
	// This would record the decision for later training
	// Implementation would track the outcome of the routing decision
}

func (ir *IntelligentRouter) addTrainingExample(destination string, metrics *RouteMetrics, success bool) {
	// Add training example to ML model
	outcome := 0.0
	if success {
		outcome = 1.0
	}
	
	example := TrainingExample{
		Features: map[string]float64{
			"health_score":  metrics.HealthScore,
			"error_rate":    metrics.ErrorRate,
			"avg_latency":   float64(metrics.AvgLatency.Milliseconds()),
			"quality_score": metrics.QualityScore,
		},
		Label:     destination,
		Outcome:   outcome,
		Timestamp: time.Now(),
	}
	
	ir.mlModel.addTrainingExample(example)
}

func (ir *IntelligentRouter) retrainModel() {
	// Retrain the ML model with accumulated data
	ir.mlModel.retrain()
}

// GetRouteMetrics returns metrics for all routes
func (ir *IntelligentRouter) GetRouteMetrics() map[string]*RouteMetrics {
	ir.mu.RLock()
	defer ir.mu.RUnlock()
	
	// Return copy of metrics
	metricsCopy := make(map[string]*RouteMetrics)
	for k, v := range ir.routeMetrics {
		metricsCopy[k] = v
	}
	
	return metricsCopy
}

// GetMLModelStats returns ML model statistics
func (ir *IntelligentRouter) GetMLModelStats() map[string]interface{} {
	return map[string]interface{}{
		"accuracy":         ir.mlModel.accuracy,
		"predictions":      ir.mlModel.predictions,
		"correct_predictions": ir.mlModel.correctPreds,
		"training_examples": len(ir.mlModel.trainingData),
		"last_trained":     ir.mlModel.lastTrained,
		"features":         ir.mlModel.features,
	}
}