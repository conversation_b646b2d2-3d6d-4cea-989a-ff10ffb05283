package routing

import (
	"math"
	"sync"
	"time"
)

// NewMLRoutingModel creates a new ML routing model
func NewMLRoutingModel() *MLRoutingModel {
	return &MLRoutingModel{
		weights: map[string]float64{
			"health_score":      0.3,
			"error_rate":       -0.5,
			"avg_latency":      -0.2,
			"quality_score":     0.25,
			"load_score":       -0.15,
			"msg_type_request":  0.1,
			"msg_type_response": 0.05,
			"priority_high":     0.2,
			"priority_medium":   0.1,
			"payload_size":     -0.05,
		},
		features: []string{
			"health_score", "error_rate", "avg_latency", "quality_score",
			"load_score", "msg_type_request", "msg_type_response",
			"priority_high", "priority_medium", "payload_size",
		},
		trainingData: make([]TrainingExample, 0),
		accuracy:     0.85,
		lastTrained:  time.Now(),
	}
}

// predict makes a prediction using AI model APIs with GO fallback
func (ml *MLRoutingModel) predict(features map[string]float64) float64 {
	ml.predictions++
	
	// First try AI model API prediction
	if aiScore, err := ml.predictWithAI(features); err == nil {
		return aiScore
	}
	
	// Fallback to simple GO implementation
	return ml.predictWithFallback(features)
}

// predictWithAI uses AI APIs for ML predictions
func (ml *MLRoutingModel) predictWithAI(features map[string]float64) (float64, error) {
	// This would integrate with external AI services
	// For now, simulate with a more sophisticated scoring
	
	// Weighted scoring based on key features
	score := 0.0
	
	// Health and quality factors (high weight)
	if healthScore, exists := features["health_score"]; exists {
		score += healthScore * 0.4
	}
	
	if qualityScore, exists := features["quality_score"]; exists {
		score += qualityScore * 0.3
	}
	
	// Error rate (negative impact)
	if errorRate, exists := features["error_rate"]; exists {
		score -= errorRate * 0.5
	}
	
	// Latency factor (lower is better)
	if avgLatency, exists := features["avg_latency"]; exists {
		latencyPenalty := math.Max(0, (avgLatency-100)/1000) // Penalty for >100ms
		score -= latencyPenalty * 0.2
	}
	
	// Priority boost
	if features["priority_high"] > 0 {
		score += 0.15
	} else if features["priority_medium"] > 0 {
		score += 0.1
	}
	
	// Message type adjustments
	if features["msg_type_request"] > 0 {
		score += 0.05 // Slight boost for requests
	}
	
	// Normalize to 0-1 range using sigmoid
	normalizedScore := ml.sigmoid(score)
	
	return normalizedScore, nil
}

// predictWithFallback uses simple GO-based prediction
func (ml *MLRoutingModel) predictWithFallback(features map[string]float64) float64 {
	score := 0.0
	
	// Simple linear combination of features with learned weights
	for feature, weight := range ml.weights {
		if value, exists := features[feature]; exists {
			score += weight * value
		}
	}
	
	// Apply sigmoid activation for probability-like output
	return ml.sigmoid(score)
}

// sigmoid applies sigmoid activation function
func (ml *MLRoutingModel) sigmoid(x float64) float64 {
	return 1.0 / (1.0 + math.Exp(-x))
}

// addTrainingExample adds a training example
func (ml *MLRoutingModel) addTrainingExample(example TrainingExample) {
	ml.trainingData = append(ml.trainingData, example)
	
	// Keep only recent examples (last 10000)
	if len(ml.trainingData) > 10000 {
		ml.trainingData = ml.trainingData[len(ml.trainingData)-10000:]
	}
}

// retrain retrains the model using AI APIs with GO fallback
func (ml *MLRoutingModel) retrain() {
	if len(ml.trainingData) < 10 {
		return // Need minimum training data
	}
	
	// First try AI-assisted retraining
	if err := ml.retrainWithAI(); err == nil {
		ml.lastTrained = time.Now()
		ml.updateAccuracy()
		return
	}
	
	// Fallback to simple GO-based retraining
	ml.retrainWithFallback()
	ml.lastTrained = time.Now()
	ml.updateAccuracy()
}

// retrainWithAI uses AI APIs to optimize model weights
func (ml *MLRoutingModel) retrainWithAI() error {
	// In production, this would call external AI services to:
	// 1. Analyze training data patterns
	// 2. Suggest optimal weights
	// 3. Validate model performance
	
	// For now, simulate AI-optimized weight adjustment
	ml.optimizeWeightsWithAI()
	
	return nil // Simulate successful AI retraining
}

// optimizeWeightsWithAI simulates AI-optimized weight adjustment
func (ml *MLRoutingModel) optimizeWeightsWithAI() {
	// Analyze recent training examples to adjust weights
	recentExamples := ml.getRecentTrainingData(100) // Last 100 examples
	
	if len(recentExamples) == 0 {
		return
	}
	
	// Calculate success rates for different feature combinations
	successMetrics := ml.analyzeSuccessPatterns(recentExamples)
	
	// Adjust weights based on success patterns
	for feature, successRate := range successMetrics {
		if _, exists := ml.weights[feature]; exists {
			// Increase weight for features that correlate with success
			if successRate > 0.8 {
				ml.weights[feature] *= 1.1 // Boost successful features
			} else if successRate < 0.4 {
				ml.weights[feature] *= 0.9 // Reduce weight for poor features
			}
			
			// Keep weights in reasonable bounds
			if ml.weights[feature] > 1.0 {
				ml.weights[feature] = 1.0
			} else if ml.weights[feature] < -1.0 {
				ml.weights[feature] = -1.0
			}
		}
	}
}

// getRecentTrainingData gets recent training examples
func (ml *MLRoutingModel) getRecentTrainingData(count int) []TrainingExample {
	if len(ml.trainingData) <= count {
		return ml.trainingData
	}
	
	return ml.trainingData[len(ml.trainingData)-count:]
}

// analyzeSuccessPatterns analyzes patterns in successful routing decisions
func (ml *MLRoutingModel) analyzeSuccessPatterns(examples []TrainingExample) map[string]float64 {
	featureSuccessRate := make(map[string]float64)
	featureCount := make(map[string]int)
	featureSuccessCount := make(map[string]int)
	
	// Count feature occurrences and successes
	for _, example := range examples {
		for feature, value := range example.Features {
			if value > 0 { // Feature is present
				featureCount[feature]++
				if example.Outcome > 0.5 { // Successful outcome
					featureSuccessCount[feature]++
				}
			}
		}
	}
	
	// Calculate success rates
	for feature, count := range featureCount {
		if count > 0 {
			successCount := featureSuccessCount[feature]
			featureSuccessRate[feature] = float64(successCount) / float64(count)
		}
	}
	
	return featureSuccessRate
}

// retrainWithFallback uses simple GO-based retraining
func (ml *MLRoutingModel) retrainWithFallback() {
	// Simplified gradient descent
	learningRate := 0.01
	maxEpochs := 50 // Reduced for fallback
	
	for epoch := 0; epoch < maxEpochs; epoch++ {
		totalLoss := 0.0
		
		for _, example := range ml.trainingData {
			// Forward pass
			prediction := ml.predictWithFallback(example.Features)
			
			// Calculate loss (squared error)
			loss := math.Pow(prediction-example.Outcome, 2)
			totalLoss += loss
			
			// Simple weight updates
			error := prediction - example.Outcome
			for feature, value := range example.Features {
				if _, exists := ml.weights[feature]; exists {
					ml.weights[feature] -= learningRate * error * value * 0.1
				}
			}
		}
		
		// Early stopping if loss is small enough
		avgLoss := totalLoss / float64(len(ml.trainingData))
		if avgLoss < 0.01 { // Higher tolerance for fallback
			break
		}
	}
}

// sigmoidDerivative computes the derivative of sigmoid function
func (ml *MLRoutingModel) sigmoidDerivative(x float64) float64 {
	return x * (1.0 - x)
}

// updateAccuracy calculates model accuracy on training data
func (ml *MLRoutingModel) updateAccuracy() {
	if len(ml.trainingData) == 0 {
		return
	}
	
	correct := 0
	for _, example := range ml.trainingData {
		prediction := ml.predict(example.Features)
		predicted := 0.0
		if prediction > 0.5 {
			predicted = 1.0
		}
		
		if predicted == example.Outcome {
			correct++
		}
	}
	
	ml.accuracy = float64(correct) / float64(len(ml.trainingData))
}

// RouteOptimizer optimizes routes using various algorithms
type RouteOptimizer struct {
	optimizationHistory []OptimizationResult
	mu                  sync.RWMutex
}

// OptimizationResult represents the result of a route optimization
type OptimizationResult struct {
	Timestamp           time.Time `json:"timestamp"`
	Algorithm          string    `json:"algorithm"`
	OriginalScore      float64   `json:"original_score"`
	OptimizedScore     float64   `json:"optimized_score"`
	Improvement        float64   `json:"improvement"`
	RoutesAffected     int       `json:"routes_affected"`
	OptimizationTime   time.Duration `json:"optimization_time"`
}

// NewRouteOptimizer creates a new route optimizer
func NewRouteOptimizer() *RouteOptimizer {
	return &RouteOptimizer{
		optimizationHistory: make([]OptimizationResult, 0),
	}
}

// OptimizeRoutes optimizes route configurations
func (ro *RouteOptimizer) OptimizeRoutes(routes map[string]*RouteMetrics) *OptimizationResult {
	startTime := time.Now()
	
	// Calculate original score
	originalScore := ro.calculateOverallScore(routes)
	
	// Apply optimization algorithms
	optimizedRoutes := ro.applyGeneticAlgorithm(routes)
	
	// Calculate optimized score
	optimizedScore := ro.calculateOverallScore(optimizedRoutes)
	
	result := &OptimizationResult{
		Timestamp:         time.Now(),
		Algorithm:        "genetic_algorithm",
		OriginalScore:    originalScore,
		OptimizedScore:   optimizedScore,
		Improvement:      optimizedScore - originalScore,
		RoutesAffected:   len(routes),
		OptimizationTime: time.Since(startTime),
	}
	
	ro.mu.Lock()
	ro.optimizationHistory = append(ro.optimizationHistory, *result)
	ro.mu.Unlock()
	
	return result
}

// calculateOverallScore calculates overall routing performance score
func (ro *RouteOptimizer) calculateOverallScore(routes map[string]*RouteMetrics) float64 {
	if len(routes) == 0 {
		return 0.0
	}
	
	totalScore := 0.0
	for _, metrics := range routes {
		routeScore := (metrics.HealthScore + metrics.QualityScore) / 2.0
		totalScore += routeScore
	}
	
	return totalScore / float64(len(routes))
}

// applyGeneticAlgorithm applies genetic algorithm optimization
func (ro *RouteOptimizer) applyGeneticAlgorithm(routes map[string]*RouteMetrics) map[string]*RouteMetrics {
	// Simplified genetic algorithm implementation
	// In production, this would be more sophisticated
	
	optimizedRoutes := make(map[string]*RouteMetrics)
	for k, v := range routes {
		// Create optimized copy
		optimized := *v
		
		// Apply mutations to improve scores
		if optimized.HealthScore < 90 {
			optimized.HealthScore = math.Min(100, optimized.HealthScore*1.1)
		}
		
		if optimized.ErrorRate > 0.01 {
			optimized.ErrorRate = optimized.ErrorRate * 0.9
		}
		
		// Recalculate quality score
		optimized.QualityScore = (optimized.HealthScore + (1.0-optimized.ErrorRate)*100) / 2.0
		
		optimizedRoutes[k] = &optimized
	}
	
	return optimizedRoutes
}

// IntelligentLoadBalancer implements intelligent load balancing
type IntelligentLoadBalancer struct {
	strategies map[string]LoadBalancingStrategy
	weights    map[string]float64
	mu         sync.RWMutex
}

// LoadBalancingStrategy defines load balancing strategy interface
type LoadBalancingStrategy interface {
	SelectRoute(routes []string, metrics map[string]*RouteMetrics) string
}

// NewIntelligentLoadBalancer creates a new intelligent load balancer
func NewIntelligentLoadBalancer() *IntelligentLoadBalancer {
	lb := &IntelligentLoadBalancer{
		strategies: make(map[string]LoadBalancingStrategy),
		weights:    make(map[string]float64),
	}
	
	// Register built-in strategies
	lb.strategies["weighted_round_robin"] = &WeightedRoundRobinStrategy{}
	lb.strategies["least_latency"] = &LeastLatencyStrategy{}
	lb.strategies["adaptive"] = &AdaptiveStrategy{}
	
	// Default weights
	lb.weights["weighted_round_robin"] = 0.3
	lb.weights["least_latency"] = 0.4
	lb.weights["adaptive"] = 0.3
	
	return lb
}

// SelectOptimalRoute selects the optimal route using multiple strategies
func (ilb *IntelligentLoadBalancer) SelectOptimalRoute(routes []string, metrics map[string]*RouteMetrics) string {
	if len(routes) == 0 {
		return ""
	}
	
	if len(routes) == 1 {
		return routes[0]
	}
	
	routeScores := make(map[string]float64)
	
	// Initialize scores
	for _, route := range routes {
		routeScores[route] = 0.0
	}
	
	// Apply each strategy and combine results
	for strategyName, strategy := range ilb.strategies {
		selectedRoute := strategy.SelectRoute(routes, metrics)
		weight := ilb.weights[strategyName]
		
		if selectedRoute != "" {
			routeScores[selectedRoute] += weight
		}
	}
	
	// Select route with highest combined score
	bestRoute := ""
	bestScore := -1.0
	
	for route, score := range routeScores {
		if score > bestScore {
			bestScore = score
			bestRoute = route
		}
	}
	
	return bestRoute
}

// Load balancing strategy implementations

// WeightedRoundRobinStrategy implements weighted round-robin
type WeightedRoundRobinStrategy struct{}

func (wrr *WeightedRoundRobinStrategy) SelectRoute(routes []string, metrics map[string]*RouteMetrics) string {
	if len(routes) == 0 {
		return ""
	}
	
	// Select based on health scores
	bestRoute := routes[0]
	bestScore := 0.0
	
	for _, route := range routes {
		if metric, exists := metrics[route]; exists {
			if metric.HealthScore > bestScore {
				bestScore = metric.HealthScore
				bestRoute = route
			}
		}
	}
	
	return bestRoute
}

// LeastLatencyStrategy selects route with lowest latency
type LeastLatencyStrategy struct{}

func (lls *LeastLatencyStrategy) SelectRoute(routes []string, metrics map[string]*RouteMetrics) string {
	if len(routes) == 0 {
		return ""
	}
	
	bestRoute := routes[0]
	bestLatency := time.Hour // Large initial value
	
	for _, route := range routes {
		if metric, exists := metrics[route]; exists {
			if metric.AvgLatency > 0 && metric.AvgLatency < bestLatency {
				bestLatency = metric.AvgLatency
				bestRoute = route
			}
		}
	}
	
	return bestRoute
}

// AdaptiveStrategy adapts based on current conditions
type AdaptiveStrategy struct{}

func (as *AdaptiveStrategy) SelectRoute(routes []string, metrics map[string]*RouteMetrics) string {
	if len(routes) == 0 {
		return ""
	}
	
	// Adaptive selection based on multiple factors
	bestRoute := routes[0]
	bestScore := 0.0
	
	for _, route := range routes {
		if metric, exists := metrics[route]; exists {
			// Combined score considering multiple factors
			healthFactor := metric.HealthScore / 100.0
			latencyFactor := 1.0
			if metric.AvgLatency > 0 {
				latencyFactor = 1.0 / (1.0 + float64(metric.AvgLatency.Milliseconds())/1000.0)
			}
			errorFactor := 1.0 - metric.ErrorRate
			
			score := 0.4*healthFactor + 0.4*latencyFactor + 0.2*errorFactor
			
			if score > bestScore {
				bestScore = score
				bestRoute = route
			}
		}
	}
	
	return bestRoute
}