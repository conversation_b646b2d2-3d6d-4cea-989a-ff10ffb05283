package routing

import (
	"fmt"
	"sync"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// Router defines the interface for message routing
type Router interface {
	// Route finds the best route for a message
	Route(msg *types.Message) (*types.RouteInfo, error)
	
	// AddRoute adds a new route to the routing table
	AddRoute(route *types.RouteInfo) error
	
	// RemoveRoute removes a route from the routing table
	RemoveRoute(destination string) error
	
	// GetRoutes returns all current routes
	GetRoutes() map[string]*types.RouteInfo
	
	// UpdateRouteHealth updates the health status of a route
	UpdateRouteHealth(destination string, healthy bool) error
}

// BasicRouter implements a simple routing algorithm
type BasicRouter struct {
	routes map[string]*types.RouteInfo
	mu     sync.RWMutex
}

// NewBasicRouter creates a new basic router instance
func NewBasicRouter() *BasicRouter {
	return &BasicRouter{
		routes: make(map[string]*types.RouteInfo),
	}
}

// Route finds the best route for a message using basic algorithm
func (r *BasicRouter) Route(msg *types.Message) (*types.RouteInfo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// First, try to find an exact match for the destination
	if route, exists := r.routes[msg.ToAgentID]; exists && route.Health == types.RouteHealthHealthy {
		return route, nil
	}
	
	// If no exact match, try wildcard routes
	if route, exists := r.routes["*"]; exists && route.Health == types.RouteHealthHealthy {
		return route, nil
	}
	
	// If no wildcard route, return any route for the destination (even if unhealthy)
	if route, exists := r.routes[msg.ToAgentID]; exists {
		return route, nil
	}
	
	return nil, fmt.Errorf("no route found for destination: %s", msg.ToAgentID)
}

// AddRoute adds a new route to the routing table
func (r *BasicRouter) AddRoute(route *types.RouteInfo) error {
	if route == nil {
		return fmt.Errorf("route cannot be nil")
	}
	
	if route.Destination == "" {
		return fmt.Errorf("route destination cannot be empty")
	}
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Set default values if not provided
	if route.ID == "" {
		route.ID = fmt.Sprintf("route-%s-%d", route.Destination, time.Now().UnixNano())
	}
	
	if route.LastUsed.IsZero() {
		route.LastUsed = time.Now()
	}
	
	route.Health = types.RouteHealthHealthy // Default to healthy
	
	r.routes[route.Destination] = route
	return nil
}

// RemoveRoute removes a route from the routing table
func (r *BasicRouter) RemoveRoute(destination string) error {
	if destination == "" {
		return fmt.Errorf("destination cannot be empty")
	}
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.routes[destination]; !exists {
		return fmt.Errorf("route not found for destination: %s", destination)
	}
	
	delete(r.routes, destination)
	return nil
}

// GetRoutes returns all current routes
func (r *BasicRouter) GetRoutes() map[string]*types.RouteInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// Return a copy to prevent external modifications
	routesCopy := make(map[string]*types.RouteInfo)
	for k, v := range r.routes {
		routesCopy[k] = v
	}
	
	return routesCopy
}

// UpdateRouteHealth updates the health status of a route
func (r *BasicRouter) UpdateRouteHealth(destination string, healthy bool) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	route, exists := r.routes[destination]
	if !exists {
		return fmt.Errorf("route not found for destination: %s", destination)
	}
	
	if healthy {
		route.Health = types.RouteHealthHealthy
	} else {
		route.Health = types.RouteHealthUnhealthy
	}
	route.LastUsed = time.Now()
	
	return nil
}

// GetHealthyRoutes returns only healthy routes
func (r *BasicRouter) GetHealthyRoutes() map[string]*types.RouteInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	healthyRoutes := make(map[string]*types.RouteInfo)
	for k, v := range r.routes {
		if v.Health == types.RouteHealthHealthy {
			healthyRoutes[k] = v
		}
	}
	
	return healthyRoutes
}

// GetRouteStats returns statistics about the routing table
func (r *BasicRouter) GetRouteStats() RouteStats {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	stats := RouteStats{
		TotalRoutes:   len(r.routes),
		HealthyRoutes: 0,
		Protocols:     make(map[string]int),
		Timestamp:     time.Now(),
	}
	
	for _, route := range r.routes {
		if route.Health == types.RouteHealthHealthy {
			stats.HealthyRoutes++
		}
		stats.Protocols[string(route.Protocol)]++
	}
	
	return stats
}

// RouteStats contains statistics about the routing table
type RouteStats struct {
	TotalRoutes   int            `json:"total_routes"`
	HealthyRoutes int            `json:"healthy_routes"`
	Protocols     map[string]int `json:"protocols"`
	Timestamp     time.Time      `json:"timestamp"`
}

// WeightedRouter implements weighted round-robin routing
type WeightedRouter struct {
	*BasicRouter
	weights map[string]int
	current map[string]int
}

// NewWeightedRouter creates a new weighted router instance
func NewWeightedRouter() *WeightedRouter {
	return &WeightedRouter{
		BasicRouter: NewBasicRouter(),
		weights:     make(map[string]int),
		current:     make(map[string]int),
	}
}

// Route finds the best route using weighted round-robin algorithm
func (r *WeightedRouter) Route(msg *types.Message) (*types.RouteInfo, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// For simplicity, fall back to basic routing for now
	// In production, this would implement proper weighted round-robin
	return r.BasicRouter.Route(msg)
}

// SetRouteWeight sets the weight for a specific route
func (r *WeightedRouter) SetRouteWeight(destination string, weight int) error {
	if weight < 1 {
		return fmt.Errorf("weight must be at least 1")
	}
	
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.routes[destination]; !exists {
		return fmt.Errorf("route not found for destination: %s", destination)
	}
	
	r.weights[destination] = weight
	r.current[destination] = 0
	
	return nil
}