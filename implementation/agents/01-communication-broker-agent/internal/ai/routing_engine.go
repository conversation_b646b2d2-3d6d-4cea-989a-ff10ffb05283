package ai

import (
	"context"
	"fmt"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// RoutingEngine provides AI-powered routing decisions
type RoutingEngine struct {
	config    *config.Config
	enabled   bool
	gemini    *GeminiClient
	openai    *OpenAIClient
	claude    *ClaudeClient
	fallback  RoutingAlgorithm
}

// RoutingDecision represents an AI routing decision
type RoutingDecision struct {
	RecommendedRoute string                 `json:"recommended_route"`
	Confidence      float64                `json:"confidence"`
	Reasoning       string                 `json:"reasoning"`
	Alternatives    []string               `json:"alternatives"`
	Metrics         map[string]interface{} `json:"metrics"`
	Timestamp       time.Time              `json:"timestamp"`
}

// RoutingContext provides context for AI routing decisions
type RoutingContext struct {
	Message         *types.Message              `json:"message"`
	AvailableRoutes map[string]*types.RouteInfo `json:"available_routes"`
	NetworkStats    *types.NetworkStatistics    `json:"network_stats"`
	HistoricalData  []RoutingDecision           `json:"historical_data"`
}

// RoutingAlgorithm defines fallback routing algorithms
type RoutingAlgorithm string

const (
	AlgorithmRoundRobin    RoutingAlgorithm = "round_robin"
	AlgorithmWeighted      RoutingAlgorithm = "weighted"
	AlgorithmLeastLatency  RoutingAlgorithm = "least_latency"
	AlgorithmHealthBased   RoutingAlgorithm = "health_based"
)

// NewRoutingEngine creates a new AI routing engine
func NewRoutingEngine(cfg *config.Config) *RoutingEngine {
	engine := &RoutingEngine{
		config:   cfg,
		enabled:  cfg.AI.Enabled,
		fallback: AlgorithmRoundRobin,
	}
	
	// Initialize AI clients based on configuration
	if cfg.AI.Enabled {
		if cfg.AI.Providers.GoogleGemini.Enabled {
			engine.gemini = NewGeminiClient(cfg.AI.Providers.GoogleGemini)
		}
		if cfg.AI.Providers.OpenAI.Enabled {
			engine.openai = NewOpenAIClient(cfg.AI.Providers.OpenAI)
		}
		if cfg.AI.Providers.Anthropic.Enabled {
			engine.claude = NewClaudeClient(cfg.AI.Providers.Anthropic)
		}
	}
	
	return engine
}

// DecideRoute makes an AI-powered routing decision with GO fallback
func (re *RoutingEngine) DecideRoute(ctx context.Context, routingCtx *RoutingContext) (*RoutingDecision, error) {
	if !re.enabled {
		return re.fallbackDecision(routingCtx)
	}
	
	// Try AI providers in order of preference with timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	
	decision, err := re.tryAIProviders(ctxWithTimeout, routingCtx)
	if err != nil {
		// Fall back to algorithmic routing if AI fails or times out
		return re.fallbackDecision(routingCtx)
	}
	
	// Validate AI decision and apply safety checks
	if validated := re.validateAIDecision(decision, routingCtx); validated != nil {
		return validated, nil
	}
	
	// If validation fails, use fallback
	return re.fallbackDecision(routingCtx)
}

// tryAIProviders attempts to get routing decision from AI providers
func (re *RoutingEngine) tryAIProviders(ctx context.Context, routingCtx *RoutingContext) (*RoutingDecision, error) {
	var lastErr error
	
	// Try Gemini first
	if re.gemini != nil {
		decision, err := re.gemini.GetRoutingDecision(ctx, routingCtx)
		if err == nil {
			return decision, nil
		}
		lastErr = err
	}
	
	// Try OpenAI as fallback
	if re.openai != nil {
		decision, err := re.openai.GetRoutingDecision(ctx, routingCtx)
		if err == nil {
			return decision, nil
		}
		lastErr = err
	}
	
	// Try Claude as last resort
	if re.claude != nil {
		decision, err := re.claude.GetRoutingDecision(ctx, routingCtx)
		if err == nil {
			return decision, nil
		}
		lastErr = err
	}
	
	return nil, lastErr
}

// fallbackDecision provides a non-AI routing decision
func (re *RoutingEngine) fallbackDecision(routingCtx *RoutingContext) (*RoutingDecision, error) {
	if len(routingCtx.AvailableRoutes) == 0 {
		return nil, fmt.Errorf("no routes available")
	}
	
	var selectedRoute string
	reasoning := "AI routing unavailable, using fallback algorithm"
	
	switch re.fallback {
	case AlgorithmRoundRobin:
		selectedRoute = re.roundRobinSelection(routingCtx.AvailableRoutes)
		reasoning += " (round-robin)"
		
	case AlgorithmWeighted:
		selectedRoute = re.weightedSelection(routingCtx.AvailableRoutes)
		reasoning += " (weighted)"
		
	case AlgorithmLeastLatency:
		selectedRoute = re.leastLatencySelection(routingCtx.AvailableRoutes)
		reasoning += " (least latency)"
		
	case AlgorithmHealthBased:
		selectedRoute = re.healthBasedSelection(routingCtx.AvailableRoutes)
		reasoning += " (health-based)"
		
	default:
		selectedRoute = re.roundRobinSelection(routingCtx.AvailableRoutes)
		reasoning += " (default round-robin)"
	}
	
	return &RoutingDecision{
		RecommendedRoute: selectedRoute,
		Confidence:      0.7, // Medium confidence for algorithmic decisions
		Reasoning:       reasoning,
		Alternatives:    re.getAlternativeRoutes(routingCtx.AvailableRoutes, selectedRoute),
		Metrics: map[string]interface{}{
			"algorithm":      string(re.fallback),
			"ai_enabled":     false,
			"fallback_used":  true,
			"routes_count":   len(routingCtx.AvailableRoutes),
		},
		Timestamp: time.Now(),
	}, nil
}

// roundRobinSelection implements round-robin route selection
func (re *RoutingEngine) roundRobinSelection(routes map[string]*types.RouteInfo) string {
	if len(routes) == 0 {
		return ""
	}
	
	// For simplicity, return the first route
	// In production, this would maintain state for true round-robin
	for destination := range routes {
		return destination
	}
	
	return ""
}

// weightedSelection implements weighted route selection
func (re *RoutingEngine) weightedSelection(routes map[string]*types.RouteInfo) string {
	if len(routes) == 0 {
		return ""
	}
	
	// Find route with highest priority (lowest priority number)
	var bestRoute string
	var bestPriority int = 1000
	
	for destination, route := range routes {
		if route.Health == types.RouteHealthHealthy && route.Priority < bestPriority {
			bestRoute = destination
			bestPriority = route.Priority
		}
	}
	
	if bestRoute != "" {
		return bestRoute
	}
	
	// If no healthy routes, return any route
	for destination := range routes {
		return destination
	}
	
	return ""
}

// leastLatencySelection selects route with lowest latency
func (re *RoutingEngine) leastLatencySelection(routes map[string]*types.RouteInfo) string {
	if len(routes) == 0 {
		return ""
	}
	
	// For now, use the same logic as weighted selection
	// In production, this would use actual latency measurements
	return re.weightedSelection(routes)
}

// healthBasedSelection selects only healthy routes
func (re *RoutingEngine) healthBasedSelection(routes map[string]*types.RouteInfo) string {
	if len(routes) == 0 {
		return ""
	}
	
	// First try to find a healthy route
	for destination, route := range routes {
		if route.Health == types.RouteHealthHealthy {
			return destination
		}
	}
	
	// If no healthy routes, use any route
	for destination := range routes {
		return destination
	}
	
	return ""
}

// getAlternativeRoutes returns alternative route destinations
func (re *RoutingEngine) getAlternativeRoutes(routes map[string]*types.RouteInfo, selected string) []string {
	alternatives := make([]string, 0, len(routes)-1)
	
	for destination := range routes {
		if destination != selected {
			alternatives = append(alternatives, destination)
		}
	}
	
	return alternatives
}

// OptimizeRouting provides suggestions for route optimization
func (re *RoutingEngine) OptimizeRouting(ctx context.Context, historicalData []RoutingDecision) ([]string, error) {
	if !re.enabled {
		return []string{"Enable AI for advanced routing optimization"}, nil
	}
	
	// Placeholder for AI-powered optimization suggestions
	// In production, this would analyze historical data and provide recommendations
	
	suggestions := []string{
		"Consider adding more routes for high-traffic destinations",
		"Monitor route health status for better decision-making",
		"Implement load balancing across multiple routes",
	}
	
	return suggestions, nil
}

// AnalyzeRoutingPerformance analyzes routing performance using AI
func (re *RoutingEngine) AnalyzeRoutingPerformance(ctx context.Context, metrics map[string]interface{}) (*PerformanceAnalysis, error) {
	analysis := &PerformanceAnalysis{
		OverallScore:    0.8,
		Recommendations: []string{},
		Insights:        []string{},
		Timestamp:       time.Now(),
	}
	
	if !re.enabled {
		analysis.Recommendations = append(analysis.Recommendations,
			"Enable AI for detailed performance analysis")
		return analysis, nil
	}
	
	// Placeholder for AI-powered analysis
	// In production, this would use AI to analyze performance metrics
	
	analysis.Recommendations = []string{
		"Optimize route selection for better latency",
		"Consider implementing caching for frequently used routes",
		"Monitor error rates and implement circuit breakers",
	}
	
	analysis.Insights = []string{
		"Route distribution is well-balanced",
		"Error rates are within acceptable limits",
		"Latency trends show room for improvement",
	}
	
	return analysis, nil
}

// PerformanceAnalysis contains AI-generated performance analysis
type PerformanceAnalysis struct {
	OverallScore    float64   `json:"overall_score"`
	Recommendations []string  `json:"recommendations"`
	Insights        []string  `json:"insights"`
	Timestamp       time.Time `json:"timestamp"`
}

// SetFallbackAlgorithm sets the fallback routing algorithm
func (re *RoutingEngine) SetFallbackAlgorithm(algorithm RoutingAlgorithm) {
	re.fallback = algorithm
}

// IsEnabled returns whether AI routing is enabled
func (re *RoutingEngine) IsEnabled() bool {
	return re.enabled
}

// validateAIDecision validates and sanitizes AI routing decisions
func (re *RoutingEngine) validateAIDecision(decision *RoutingDecision, routingCtx *RoutingContext) *RoutingDecision {
	if decision == nil {
		return nil
	}
	
	// Check if recommended route exists
	if _, exists := routingCtx.AvailableRoutes[decision.RecommendedRoute]; !exists {
		// Try alternatives
		for _, alt := range decision.Alternatives {
			if _, exists := routingCtx.AvailableRoutes[alt]; exists {
				decision.RecommendedRoute = alt
				decision.Reasoning = fmt.Sprintf("Original route unavailable, using alternative: %s", alt)
				decision.Confidence *= 0.8 // Reduce confidence
				break
			}
		}
		
		// If no alternatives work, return nil for fallback
		if _, exists := routingCtx.AvailableRoutes[decision.RecommendedRoute]; !exists {
			return nil
		}
	}
	
	// Confidence bounds checking
	if decision.Confidence < 0.0 {
		decision.Confidence = 0.0
	} else if decision.Confidence > 1.0 {
		decision.Confidence = 1.0
	}
	
	// Minimum confidence threshold for AI decisions
	if decision.Confidence < 0.3 {
		return nil // Too low confidence, use fallback
	}
	
	// Check route health if available
	if route, exists := routingCtx.AvailableRoutes[decision.RecommendedRoute]; exists {
		if route.Health != types.RouteHealthHealthy {
			// Reduce confidence for unhealthy routes
			decision.Confidence *= 0.5
			decision.Reasoning += " (Warning: Selected route is unhealthy)"
			
			if decision.Confidence < 0.3 {
				return nil // Too risky, use fallback
			}
		}
	}
	
	// Add validation metadata
	decision.Metrics["validated"] = true
	decision.Metrics["validation_timestamp"] = time.Now().Format(time.RFC3339)
	
	return decision
}

// enhanceWithRealTimeData enhances routing context with real-time data
func (re *RoutingEngine) enhanceWithRealTimeData(routingCtx *RoutingContext) *RoutingContext {
	// Add real-time enhancements that GO can easily provide
	enhanced := &RoutingContext{
		Message:         routingCtx.Message,
		AvailableRoutes: routingCtx.AvailableRoutes,
		NetworkStats:    routingCtx.NetworkStats,
		HistoricalData:  routingCtx.HistoricalData,
	}
	
	// Add current system metrics
	if enhanced.NetworkStats == nil {
		enhanced.NetworkStats = &types.NetworkStatistics{
			TotalMessages:     0,
			AvgLatency:       50 * time.Millisecond, // Default
			ErrorRate:        0.01,                  // 1% default
			ThroughputPerSec: 1000,                  // Default throughput
			ActiveConnections: int64(len(enhanced.AvailableRoutes)),
			Timestamp:        time.Now(),
		}
	}
	
	return enhanced
}

// OptimizeRoutingWithAI provides AI-powered routing optimization suggestions
func (re *RoutingEngine) OptimizeRoutingWithAI(ctx context.Context, historicalData []RoutingDecision) ([]string, error) {
	if !re.enabled {
		return re.OptimizeRouting(ctx, historicalData)
	}
	
	// Try AI optimization with fallback
	suggestions := []string{}
	
	// AI-based analysis (simulated for now)
	if len(historicalData) > 0 {
		// Analyze patterns in routing decisions
		patterns := re.analyzeRoutingPatterns(historicalData)
		suggestions = append(suggestions, patterns...)
	}
	
	// Add GO-based analysis
	fallbackSuggestions, _ := re.OptimizeRouting(ctx, historicalData)
	suggestions = append(suggestions, fallbackSuggestions...)
	
	// Remove duplicates and prioritize
	return re.prioritizeAndDeduplicateSuggestions(suggestions), nil
}

// analyzeRoutingPatterns analyzes patterns in historical routing data
func (re *RoutingEngine) analyzeRoutingPatterns(historicalData []RoutingDecision) []string {
	patterns := []string{}
	
	if len(historicalData) < 10 {
		return patterns
	}
	
	// Analyze confidence trends
	avgConfidence := 0.0
	for _, decision := range historicalData {
		avgConfidence += decision.Confidence
	}
	avgConfidence /= float64(len(historicalData))
	
	if avgConfidence < 0.7 {
		patterns = append(patterns, "Low average routing confidence detected - consider route health improvements")
	}
	
	// Analyze route distribution
	routeUsage := make(map[string]int)
	for _, decision := range historicalData {
		routeUsage[decision.RecommendedRoute]++
	}
	
	// Check for route imbalance
	if len(routeUsage) > 1 {
		maxUsage := 0
		totalDecisions := len(historicalData)
		
		for _, count := range routeUsage {
			if count > maxUsage {
				maxUsage = count
			}
		}
		
		if float64(maxUsage)/float64(totalDecisions) > 0.8 {
			patterns = append(patterns, "Route usage is heavily skewed - consider load balancing improvements")
		}
	}
	
	// Analyze recent vs historical performance
	recentDecisions := historicalData
	if len(historicalData) > 50 {
		recentDecisions = historicalData[len(historicalData)-50:] // Last 50 decisions
	}
	
	recentAvgConfidence := 0.0
	for _, decision := range recentDecisions {
		recentAvgConfidence += decision.Confidence
	}
	recentAvgConfidence /= float64(len(recentDecisions))
	
	if recentAvgConfidence < avgConfidence-0.1 {
		patterns = append(patterns, "Recent routing confidence has decreased - investigate network conditions")
	}
	
	return patterns
}

// prioritizeAndDeduplicateSuggestions removes duplicates and prioritizes suggestions
func (re *RoutingEngine) prioritizeAndDeduplicateSuggestions(suggestions []string) []string {
	seen := make(map[string]bool)
	prioritized := []string{}
	
	// High priority keywords
	highPriority := []string{"critical", "urgent", "failure", "error"}
	mediumPriority := []string{"performance", "optimization", "improvement"}
	
	// Add high priority suggestions first
	for _, suggestion := range suggestions {
		if seen[suggestion] {
			continue
		}
		
		for _, keyword := range highPriority {
			if contains(suggestion, keyword) {
				prioritized = append(prioritized, suggestion)
				seen[suggestion] = true
				break
			}
		}
	}
	
	// Add medium priority suggestions
	for _, suggestion := range suggestions {
		if seen[suggestion] {
			continue
		}
		
		for _, keyword := range mediumPriority {
			if contains(suggestion, keyword) {
				prioritized = append(prioritized, suggestion)
				seen[suggestion] = true
				break
			}
		}
	}
	
	// Add remaining suggestions
	for _, suggestion := range suggestions {
		if !seen[suggestion] {
			prioritized = append(prioritized, suggestion)
			seen[suggestion] = true
		}
	}
	
	return prioritized
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      findInString(s, substr))))
}

// findInString simple substring search
func findInString(s, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(s) < len(substr) {
		return false
	}
	
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// GetConfig returns the current configuration
func (re *RoutingEngine) GetConfig() *config.Config {
	return re.config
}