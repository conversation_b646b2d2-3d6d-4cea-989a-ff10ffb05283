package ai

import (
	"context"
	"fmt"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// OpenAIClient handles communication with OpenAI APIs
type OpenAIClient struct {
	config   config.OpenAIConfig
	apiKey   string
	model    string
	endpoint string
	timeout  time.Duration
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(cfg config.OpenAIConfig) *OpenAIClient {
	return &OpenAIClient{
		config:   cfg,
		apiKey:   cfg.APIKey,
		model:    cfg.Model,
		endpoint: cfg.Endpoint,
		timeout:  time.Duration(cfg.Timeout) * time.Second,
	}
}

// GetRoutingDecision gets a routing decision from OpenAI
func (oc *OpenAIClient) GetRoutingDecision(ctx context.Context, routingCtx *RoutingContext) (*RoutingDecision, error) {
	if !oc.config.Enabled {
		return nil, fmt.Errorf("OpenAI client is disabled")
	}
	
	// Create context with timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, oc.timeout)
	defer cancel()
	
	// Prepare the prompt for OpenAI
	prompt := oc.buildRoutingPrompt(routingCtx)
	
	// Simulate API call to OpenAI
	// In production, this would make actual HTTP requests to OpenAI APIs
	decision := oc.simulateOpenAIResponse(ctxWithTimeout, routingCtx)
	
	// Add metadata
	decision.Metrics["provider"] = "openai"
	decision.Metrics["model"] = oc.model
	decision.Metrics["prompt_length"] = len(prompt)
	
	return decision, nil
}

// buildRoutingPrompt creates a prompt for OpenAI
func (oc *OpenAIClient) buildRoutingPrompt(routingCtx *RoutingContext) string {
	prompt := `You are an expert routing AI for a distributed communication system. 
Analyze the following routing scenario and provide the optimal routing decision.

Consider these factors:
1. Route health and availability
2. Message priority and type
3. Network performance metrics
4. Load balancing requirements
5. Historical performance data

`
	
	prompt += fmt.Sprintf("Message: ID=%s, Type=%s, From=%s, To=%s\n",
		routingCtx.Message.ID,
		routingCtx.Message.Type,
		routingCtx.Message.From,
		routingCtx.Message.To,
	)
	
	prompt += "Available Routes:\n"
	for destination, route := range routingCtx.AvailableRoutes {
		status := "UNHEALTHY"
		if route.Health == types.RouteHealthHealthy {
			status = "HEALTHY"
		}
		prompt += fmt.Sprintf("- %s: %s (%s, Priority: %d, Status: %s)\n",
			destination, route.Protocol, route.Endpoint, route.Priority, status)
	}
	
	if routingCtx.NetworkStats != nil {
		prompt += fmt.Sprintf("\nNetwork Stats: Messages=%d, AvgLatency=%s, ErrorRate=%.2f%%, Connections=%d\n",
			routingCtx.NetworkStats.TotalMessages,
			routingCtx.NetworkStats.AvgLatency,
			routingCtx.NetworkStats.ErrorRate*100,
			routingCtx.NetworkStats.ActiveConnections,
		)
	}
	
	prompt += "\nProvide your decision in JSON format with: recommended_route, confidence, reasoning, alternatives"
	
	return prompt
}

// simulateOpenAIResponse simulates a response from OpenAI
func (oc *OpenAIClient) simulateOpenAIResponse(ctx context.Context, routingCtx *RoutingContext) *RoutingDecision {
	// This is a simulation - in production, this would parse actual AI responses
	
	var recommendedRoute string
	confidence := 0.9
	reasoning := "OpenAI analysis: Selected route based on optimal health, priority, and performance metrics"
	
	// Enhanced logic for OpenAI simulation
	var bestRoute string
	var bestScore float64 = 0
	
	for destination, route := range routingCtx.AvailableRoutes {
		score := 0.0
		
		// Health factor
		if route.Health == types.RouteHealthHealthy {
			score += 50.0
		}
		
		// Priority factor (lower priority number = higher score)
		score += (10.0 - float64(route.Priority))
		
		// Protocol preference (simulate OpenAI preferring certain protocols)
		switch route.Protocol {
		case "grpc":
			score += 10.0
		case "http":
			score += 8.0
		case "websocket":
			score += 6.0
		}
		
		if score > bestScore {
			bestScore = score
			bestRoute = destination
		}
	}
	
	if bestRoute != "" {
		recommendedRoute = bestRoute
	} else {
		// Fallback to first available route
		for destination := range routingCtx.AvailableRoutes {
			recommendedRoute = destination
			confidence = 0.6
			reasoning = "OpenAI fallback: No optimal route found, selecting available route"
			break
		}
	}
	
	// Generate alternatives
	alternatives := make([]string, 0)
	for destination := range routingCtx.AvailableRoutes {
		if destination != recommendedRoute {
			alternatives = append(alternatives, destination)
		}
	}
	
	return &RoutingDecision{
		RecommendedRoute: recommendedRoute,
		Confidence:      confidence,
		Reasoning:       reasoning,
		Alternatives:    alternatives,
		Metrics: map[string]interface{}{
			"ai_provider":        "openai",
			"processing_time_ms": 200,
			"tokens_used":        67,
			"best_score":         bestScore,
		},
		Timestamp: time.Now(),
	}
}

// GenerateRoutingStrategy generates routing strategies using OpenAI
func (oc *OpenAIClient) GenerateRoutingStrategy(ctx context.Context, requirements map[string]interface{}) (map[string]interface{}, error) {
	if !oc.config.Enabled {
		return nil, fmt.Errorf("OpenAI client is disabled")
	}
	
	// Simulate strategy generation
	strategy := map[string]interface{}{
		"primary_algorithm":   "ai_optimized",
		"fallback_algorithm":  "weighted_round_robin",
		"health_check_freq":   "30s",
		"circuit_breaker":     true,
		"load_threshold":      0.8,
		"latency_target":      "100ms",
		"retry_strategy":      "exponential_backoff",
	}
	
	return strategy, nil
}

// PredictNetworkLoad predicts network load using OpenAI
func (oc *OpenAIClient) PredictNetworkLoad(ctx context.Context, historicalData []RoutingDecision) (map[string]float64, error) {
	if !oc.config.Enabled {
		return nil, fmt.Errorf("OpenAI client is disabled")
	}
	
	// Simulate load prediction
	predictions := map[string]float64{
		"next_hour":  0.65,
		"next_day":   0.72,
		"peak_hour":  0.95,
		"low_hour":   0.23,
	}
	
	return predictions, nil
}

// IsHealthy checks if the OpenAI client is healthy
func (oc *OpenAIClient) IsHealthy() bool {
	return oc.config.Enabled && oc.apiKey != ""
}

// GetStats returns client statistics
func (oc *OpenAIClient) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":      oc.config.Enabled,
		"model":        oc.model,
		"endpoint":     oc.endpoint,
		"timeout":      oc.timeout.String(),
		"healthy":      oc.IsHealthy(),
		"last_used":    time.Now().Format(time.RFC3339),
	}
}