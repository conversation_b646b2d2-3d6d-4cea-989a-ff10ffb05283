package protocol

import (
	"context"
	"fmt"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// WebSocketTranslator handles WebSocket protocol translations
type WebSocketTranslator struct {
	config  *config.Config
	healthy bool
	stats   *WebSocketTranslatorStats
}

// WebSocketTranslatorStats tracks WebSocket translator statistics
type WebSocketTranslatorStats struct {
	TranslationsCompleted int64         `json:"translations_completed"`
	ConnectionsHandled   int64         `json:"connections_handled"`
	MessagesStreamed     int64         `json:"messages_streamed"`
	AvgStreamDuration    time.Duration `json:"avg_stream_duration"`
	ErrorCount           int64         `json:"error_count"`
	LastTranslation      time.Time     `json:"last_translation"`
	RealTimeMessages     int64         `json:"real_time_messages"`
}

// NewWebSocketTranslator creates a new WebSocket translator
func NewWebSocketTranslator(cfg *config.Config) *WebSocketTranslator {
	return &WebSocketTranslator{
		config:  cfg,
		healthy: true,
		stats:   &WebSocketTranslatorStats{},
	}
}

// Translate translates messages to/from WebSocket protocol
func (wt *WebSocketTranslator) Translate(ctx context.Context, msg *types.Message, fromProtocol, toProtocol string) (*types.Message, error) {
	startTime := time.Now()
	defer func() {
		wt.stats.TranslationsCompleted++
		wt.stats.LastTranslation = time.Now()
		
		duration := time.Since(startTime)
		if wt.stats.TranslationsCompleted > 0 {
			totalTime := wt.stats.AvgStreamDuration * time.Duration(wt.stats.TranslationsCompleted-1)
			wt.stats.AvgStreamDuration = (totalTime + duration) / time.Duration(wt.stats.TranslationsCompleted)
		}
	}()
	
	switch {
	case toProtocol == "websocket":
		return wt.translateToWebSocket(ctx, msg, fromProtocol)
	case fromProtocol == "websocket":
		return wt.translateFromWebSocket(ctx, msg, toProtocol)
	default:
		wt.stats.ErrorCount++
		return nil, fmt.Errorf("WebSocket translator cannot handle %s to %s translation", fromProtocol, toProtocol)
	}
}

// translateToWebSocket converts any protocol message to WebSocket format
func (wt *WebSocketTranslator) translateToWebSocket(ctx context.Context, msg *types.Message, fromProtocol string) (*types.Message, error) {
	wt.stats.MessagesStreamed++
	
	// Create WebSocket-compatible message
	wsMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers
	for k, v := range msg.Headers {
		wsMsg.Headers[k] = v
	}
	
	// Add WebSocket-specific headers
	wsMsg.Headers["websocket-version"] = "13"
	wsMsg.Headers["websocket-type"] = wt.determineWebSocketMessageType(msg)
	wsMsg.Headers["websocket-origin"] = msg.From
	wsMsg.Headers["websocket-connection-id"] = wt.generateConnectionID()
	wsMsg.Headers["x-protocol-source"] = fromProtocol
	wsMsg.Headers["x-protocol-target"] = "websocket"
	wsMsg.Headers["x-real-time"] = "true"
	
	// Convert payload based on source protocol
	switch fromProtocol {
	case "http":
		wsMsg = wt.convertHTTPToWebSocketPayload(wsMsg)
	case "grpc":
		wsMsg = wt.convertGRPCToWebSocketPayload(wsMsg)
	case "a2a":
		wsMsg = wt.convertA2AToWebSocketPayload(wsMsg)
	default:
		// For unknown protocols, wrap in WebSocket frame
		wsMsg.Payload = map[string]interface{}{
			"frame_type":        "binary",
			"original_protocol": fromProtocol,
			"data":             msg.Payload,
			"stream_id":        wt.generateStreamID(),
			"real_time":        true,
			"timestamp":        time.Now().Unix(),
		}
	}
	
	// Mark as real-time message
	wt.stats.RealTimeMessages++
	
	return wsMsg, nil
}

// translateFromWebSocket converts WebSocket message to target protocol format
func (wt *WebSocketTranslator) translateFromWebSocket(ctx context.Context, msg *types.Message, toProtocol string) (*types.Message, error) {
	wt.stats.MessagesStreamed++
	
	// Create target protocol message
	targetMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers (excluding WebSocket-specific ones)
	for k, v := range msg.Headers {
		if !wt.isWebSocketSpecificHeader(k) {
			targetMsg.Headers[k] = v
		}
	}
	
	// Add target protocol headers
	targetMsg.Headers["x-protocol-source"] = "websocket"
	targetMsg.Headers["x-protocol-target"] = toProtocol
	targetMsg.Headers["x-websocket-type"] = msg.Headers["websocket-type"]
	targetMsg.Headers["x-websocket-connection-id"] = msg.Headers["websocket-connection-id"]
	targetMsg.Headers["x-real-time-source"] = "true"
	
	// Convert payload based on target protocol
	switch toProtocol {
	case "http":
		targetMsg = wt.convertWebSocketToHTTPPayload(targetMsg)
	case "grpc":
		targetMsg = wt.convertWebSocketToGRPCPayload(targetMsg)
	case "a2a":
		targetMsg = wt.convertWebSocketToA2APayload(targetMsg)
	default:
		// For unknown protocols, extract data from WebSocket frame
		if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
			if data, exists := payloadMap["data"]; exists {
				targetMsg.Payload = data
			}
		}
	}
	
	return targetMsg, nil
}

// convertHTTPToWebSocketPayload converts HTTP to WebSocket streaming format
func (wt *WebSocketTranslator) convertHTTPToWebSocketPayload(msg *types.Message) *types.Message {
	wsPayload := map[string]interface{}{
		"frame_type": "text",
		"opcode":     1, // Text frame
		"stream": map[string]interface{}{
			"http_method": msg.Headers["X-HTTP-Method"],
			"http_url":    msg.Headers["X-HTTP-URL"],
			"headers":     wt.extractHTTPHeaders(msg.Headers),
			"body":        msg.Payload,
		},
		"metadata": map[string]interface{}{
			"content_type":    msg.Headers["Content-Type"],
			"content_length":  msg.Headers["Content-Length"],
			"streaming":       true,
			"real_time":       true,
		},
		"stream_id":  wt.generateStreamID(),
		"timestamp":  time.Now().Unix(),
	}
	
	msg.Payload = wsPayload
	msg.Headers["websocket-type"] = "http_stream"
	
	return msg
}

// convertWebSocketToHTTPPayload converts WebSocket back to HTTP format
func (wt *WebSocketTranslator) convertWebSocketToHTTPPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if stream, exists := payloadMap["stream"]; exists {
			if streamMap, ok := stream.(map[string]interface{}); ok {
				// Extract HTTP data from WebSocket stream
				if body, exists := streamMap["body"]; exists {
					msg.Payload = body
				}
				
				// Restore HTTP headers
				if headers, exists := streamMap["headers"]; exists {
					if headerMap, ok := headers.(map[string]interface{}); ok {
						for k, v := range headerMap {
							msg.Headers[k] = fmt.Sprintf("%v", v)
						}
					}
				}
				
				// Set HTTP method and URL if available
				if method, exists := streamMap["http_method"]; exists {
					msg.Headers["X-HTTP-Method"] = fmt.Sprintf("%v", method)
				}
				if url, exists := streamMap["http_url"]; exists {
					msg.Headers["X-HTTP-URL"] = fmt.Sprintf("%v", url)
				}
			}
		}
		
		// Extract metadata
		if metadata, exists := payloadMap["metadata"]; exists {
			if metadataMap, ok := metadata.(map[string]interface{}); ok {
				if contentType, exists := metadataMap["content_type"]; exists {
					msg.Headers["Content-Type"] = fmt.Sprintf("%v", contentType)
				}
			}
		}
	}
	
	return msg
}

// convertGRPCToWebSocketPayload converts gRPC to WebSocket streaming format
func (wt *WebSocketTranslator) convertGRPCToWebSocketPayload(msg *types.Message) *types.Message {
	wsPayload := map[string]interface{}{
		"frame_type": "binary",
		"opcode":     2, // Binary frame for protobuf data
		"stream": map[string]interface{}{
			"grpc_service": msg.Headers["grpc-service"],
			"grpc_method":  msg.Headers["grpc-method"],
			"protobuf_data": msg.Payload,
			"streaming":    true,
		},
		"metadata": map[string]interface{}{
			"encoding":     msg.Headers["grpc-encoding"],
			"content_type": "application/grpc+proto",
			"real_time":    true,
		},
		"stream_id": wt.generateStreamID(),
		"timestamp": time.Now().Unix(),
	}
	
	msg.Payload = wsPayload
	msg.Headers["websocket-type"] = "grpc_stream"
	
	return msg
}

// convertWebSocketToGRPCPayload converts WebSocket back to gRPC format
func (wt *WebSocketTranslator) convertWebSocketToGRPCPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if stream, exists := payloadMap["stream"]; exists {
			if streamMap, ok := stream.(map[string]interface{}); ok {
				// Extract gRPC data from WebSocket stream
				if protobufData, exists := streamMap["protobuf_data"]; exists {
					msg.Payload = protobufData
				}
				
				// Restore gRPC headers
				if service, exists := streamMap["grpc_service"]; exists {
					msg.Headers["grpc-service"] = fmt.Sprintf("%v", service)
				}
				if method, exists := streamMap["grpc_method"]; exists {
					msg.Headers["grpc-method"] = fmt.Sprintf("%v", method)
				}
			}
		}
		
		// Extract metadata
		if metadata, exists := payloadMap["metadata"]; exists {
			if metadataMap, ok := metadata.(map[string]interface{}); ok {
				if encoding, exists := metadataMap["encoding"]; exists {
					msg.Headers["grpc-encoding"] = fmt.Sprintf("%v", encoding)
				}
			}
		}
	}
	
	return msg
}

// convertA2AToWebSocketPayload converts A2A to WebSocket streaming format
func (wt *WebSocketTranslator) convertA2AToWebSocketPayload(msg *types.Message) *types.Message {
	wsPayload := map[string]interface{}{
		"frame_type": "text",
		"opcode":     1,
		"stream": map[string]interface{}{
			"agent_protocol": "a2a",
			"source_agent":   msg.From,
			"target_agent":   msg.To,
			"capabilities":   msg.Headers["agent-capabilities"],
			"priority":      msg.Headers["routing-priority"],
			"data":          msg.Payload,
			"streaming":     true,
		},
		"metadata": map[string]interface{}{
			"a2a_version":     msg.Headers["a2a-version"],
			"agent_streaming": true,
			"real_time":       true,
		},
		"stream_id": wt.generateStreamID(),
		"timestamp": time.Now().Unix(),
	}
	
	msg.Payload = wsPayload
	msg.Headers["websocket-type"] = "agent_stream"
	
	return msg
}

// convertWebSocketToA2APayload converts WebSocket back to A2A format
func (wt *WebSocketTranslator) convertWebSocketToA2APayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if stream, exists := payloadMap["stream"]; exists {
			if streamMap, ok := stream.(map[string]interface{}); ok {
				// Extract A2A data from WebSocket stream
				if data, exists := streamMap["data"]; exists {
					msg.Payload = data
				}
				
				// Restore A2A headers
				if capabilities, exists := streamMap["capabilities"]; exists {
					msg.Headers["agent-capabilities"] = fmt.Sprintf("%v", capabilities)
				}
				if priority, exists := streamMap["priority"]; exists {
					msg.Headers["routing-priority"] = fmt.Sprintf("%v", priority)
				}
			}
		}
		
		// Extract metadata
		if metadata, exists := payloadMap["metadata"]; exists {
			if metadataMap, ok := metadata.(map[string]interface{}); ok {
				if version, exists := metadataMap["a2a_version"]; exists {
					msg.Headers["a2a-version"] = fmt.Sprintf("%v", version)
				}
			}
		}
	}
	
	return msg
}

// determineWebSocketMessageType determines the appropriate WebSocket message type
func (wt *WebSocketTranslator) determineWebSocketMessageType(msg *types.Message) string {
	switch msg.Type {
	case types.MessageTypeRequest:
		return "request_stream"
	case types.MessageTypeResponse:
		return "response_stream"
	case types.MessageTypeEvent:
		return "event_stream"
	default:
		return "data_stream"
	}
}

// generateConnectionID generates a unique WebSocket connection ID
func (wt *WebSocketTranslator) generateConnectionID() string {
	return fmt.Sprintf("ws-conn-%d", time.Now().UnixNano())
}

// generateStreamID generates a unique stream ID
func (wt *WebSocketTranslator) generateStreamID() string {
	return fmt.Sprintf("stream-%d", time.Now().UnixNano())
}

// extractHTTPHeaders extracts HTTP headers for WebSocket streaming
func (wt *WebSocketTranslator) extractHTTPHeaders(headers map[string]string) map[string]string {
	httpHeaders := make(map[string]string)
	
	for k, v := range headers {
		if k != "websocket-version" && k != "websocket-type" && 
		   k != "websocket-origin" && k != "websocket-connection-id" {
			httpHeaders[k] = v
		}
	}
	
	return httpHeaders
}

// isWebSocketSpecificHeader checks if a header is WebSocket-specific
func (wt *WebSocketTranslator) isWebSocketSpecificHeader(header string) bool {
	wsHeaders := []string{
		"websocket-version", "websocket-type", "websocket-origin",
		"websocket-connection-id", "x-protocol-source", "x-protocol-target",
		"x-real-time", "upgrade", "connection", "sec-websocket-key",
		"sec-websocket-accept", "sec-websocket-protocol",
	}
	
	for _, wsHeader := range wsHeaders {
		if header == wsHeader {
			return true
		}
	}
	
	return false
}

// SupportedProtocols returns protocols this translator can handle
func (wt *WebSocketTranslator) SupportedProtocols() []string {
	return []string{"websocket", "http", "grpc", "a2a"}
}

// IsHealthy checks if the translator is healthy
func (wt *WebSocketTranslator) IsHealthy() bool {
	return wt.healthy
}

// GetStats returns translator statistics
func (wt *WebSocketTranslator) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"translator_type":        "websocket",
		"translations_completed": wt.stats.TranslationsCompleted,
		"connections_handled":   wt.stats.ConnectionsHandled,
		"messages_streamed":     wt.stats.MessagesStreamed,
		"avg_stream_duration":   wt.stats.AvgStreamDuration.String(),
		"error_count":           wt.stats.ErrorCount,
		"real_time_messages":    wt.stats.RealTimeMessages,
		"last_translation":      wt.stats.LastTranslation.Format(time.RFC3339),
		"supported_protocols":   wt.SupportedProtocols(),
		"healthy":              wt.healthy,
	}
}