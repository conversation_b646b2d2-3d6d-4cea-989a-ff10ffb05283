package protocol

import (
	"context"
	"fmt"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// GRPCTranslator handles gRPC protocol translations
type GRPCTranslator struct {
	config  *config.Config
	healthy bool
	stats   *GRPCTranslatorStats
}

// GRPCTranslatorStats tracks gRPC translator statistics
type GRPCTranslatorStats struct {
	TranslationsCompleted int64         `json:"translations_completed"`
	GRPCCallsMade        int64         `json:"grpc_calls_made"`
	GRPCCallsReceived    int64         `json:"grpc_calls_received"`
	AvgCallDuration      time.Duration `json:"avg_call_duration"`
	ErrorCount           int64         `json:"error_count"`
	LastTranslation      time.Time     `json:"last_translation"`
	ProtobufConversions  int64         `json:"protobuf_conversions"`
}

// NewGRPCTranslator creates a new gRPC translator
func NewGRPCTranslator(cfg *config.Config) *GRPCTranslator {
	return &GRPCTranslator{
		config:  cfg,
		healthy: true,
		stats:   &GRPCTranslatorStats{},
	}
}

// Translate translates messages to/from gRPC protocol
func (gt *GRPCTranslator) Translate(ctx context.Context, msg *types.Message, fromProtocol, toProtocol string) (*types.Message, error) {
	startTime := time.Now()
	defer func() {
		gt.stats.TranslationsCompleted++
		gt.stats.LastTranslation = time.Now()
		
		duration := time.Since(startTime)
		if gt.stats.TranslationsCompleted > 0 {
			totalTime := gt.stats.AvgCallDuration * time.Duration(gt.stats.TranslationsCompleted-1)
			gt.stats.AvgCallDuration = (totalTime + duration) / time.Duration(gt.stats.TranslationsCompleted)
		}
	}()
	
	switch {
	case toProtocol == "grpc":
		return gt.translateToGRPC(ctx, msg, fromProtocol)
	case fromProtocol == "grpc":
		return gt.translateFromGRPC(ctx, msg, toProtocol)
	default:
		gt.stats.ErrorCount++
		return nil, fmt.Errorf("gRPC translator cannot handle %s to %s translation", fromProtocol, toProtocol)
	}
}

// translateToGRPC converts any protocol message to gRPC format
func (gt *GRPCTranslator) translateToGRPC(ctx context.Context, msg *types.Message, fromProtocol string) (*types.Message, error) {
	gt.stats.ProtobufConversions++
	
	// Create gRPC-compatible message
	grpcMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers
	for k, v := range msg.Headers {
		grpcMsg.Headers[k] = v
	}
	
	// Add gRPC-specific metadata
	grpcMsg.Headers["grpc-version"] = "1.0"
	grpcMsg.Headers["grpc-encoding"] = "gzip"
	grpcMsg.Headers["grpc-accept-encoding"] = "gzip"
	grpcMsg.Headers["content-type"] = "application/grpc+proto"
	grpcMsg.Headers["x-protocol-source"] = fromProtocol
	grpcMsg.Headers["x-protocol-target"] = "grpc"
	
	// Convert payload based on source protocol
	switch fromProtocol {
	case "http":
		grpcMsg = gt.convertHTTPToGRPCPayload(grpcMsg)
	case "websocket":
		grpcMsg = gt.convertWebSocketToGRPCPayload(grpcMsg)
	case "a2a":
		grpcMsg = gt.convertA2AToGRPCPayload(grpcMsg)
	default:
		// For unknown protocols, create a generic gRPC wrapper
		grpcMsg.Payload = map[string]interface{}{
			"service":         "CommunicationBroker",
			"method":          "ProcessMessage",
			"original_protocol": fromProtocol,
			"data":            msg.Payload,
			"timestamp":       time.Now().Unix(),
		}
	}
	
	// Set default gRPC service and method if not specified
	if grpcMsg.Headers["grpc-service"] == "" {
		grpcMsg.Headers["grpc-service"] = "ai.twodot.platform.CommunicationService"
	}
	if grpcMsg.Headers["grpc-method"] == "" {
		grpcMsg.Headers["grpc-method"] = "ProcessMessage"
	}
	
	return grpcMsg, nil
}

// translateFromGRPC converts gRPC message to target protocol format
func (gt *GRPCTranslator) translateFromGRPC(ctx context.Context, msg *types.Message, toProtocol string) (*types.Message, error) {
	gt.stats.ProtobufConversions++
	
	// Create target protocol message
	targetMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers (excluding gRPC-specific ones)
	for k, v := range msg.Headers {
		if !gt.isGRPCSpecificHeader(k) {
			targetMsg.Headers[k] = v
		}
	}
	
	// Add target protocol headers
	targetMsg.Headers["x-protocol-source"] = "grpc"
	targetMsg.Headers["x-protocol-target"] = toProtocol
	targetMsg.Headers["x-grpc-service"] = msg.Headers["grpc-service"]
	targetMsg.Headers["x-grpc-method"] = msg.Headers["grpc-method"]
	
	// Convert payload based on target protocol
	switch toProtocol {
	case "http":
		targetMsg = gt.convertGRPCToHTTPPayload(targetMsg)
	case "websocket":
		targetMsg = gt.convertGRPCToWebSocketPayload(targetMsg)
	case "a2a":
		targetMsg = gt.convertGRPCToA2APayload(targetMsg)
	default:
		// For unknown protocols, extract data from gRPC wrapper
		if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
			if data, exists := payloadMap["data"]; exists {
				targetMsg.Payload = data
			}
		}
	}
	
	return targetMsg, nil
}

// convertHTTPToGRPCPayload converts HTTP payload to gRPC protobuf-like structure
func (gt *GRPCTranslator) convertHTTPToGRPCPayload(msg *types.Message) *types.Message {
	// HTTP uses JSON, gRPC uses protobuf - simulate the conversion
	grpcPayload := map[string]interface{}{
		"service": "CommunicationService",
		"method":  "ProcessHTTPMessage",
		"request": map[string]interface{}{
			"message_id":   msg.ID,
			"message_type": string(msg.Type),
			"sender":       msg.From,
			"recipient":    msg.To,
			"data":        msg.Payload,
			"http_headers": gt.extractHTTPHeaders(msg.Headers),
		},
		"metadata": map[string]interface{}{
			"timestamp":    msg.Timestamp.Unix(),
			"content_type": msg.Headers["Content-Type"],
			"user_agent":   msg.Headers["User-Agent"],
		},
	}
	
	msg.Payload = grpcPayload
	msg.Headers["grpc-service"] = "ai.twodot.platform.CommunicationService"
	msg.Headers["grpc-method"] = "ProcessHTTPMessage"
	
	return msg
}

// convertGRPCToHTTPPayload converts gRPC payload back to HTTP-friendly format
func (gt *GRPCTranslator) convertGRPCToHTTPPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if request, exists := payloadMap["request"]; exists {
			if requestMap, ok := request.(map[string]interface{}); ok {
				// Extract the original data from gRPC structure
				if data, exists := requestMap["data"]; exists {
					msg.Payload = data
				}
				
				// Restore HTTP headers from metadata
				if metadata, exists := payloadMap["metadata"]; exists {
					if metadataMap, ok := metadata.(map[string]interface{}); ok {
						if contentType, exists := metadataMap["content_type"]; exists {
							msg.Headers["Content-Type"] = fmt.Sprintf("%v", contentType)
						}
						if userAgent, exists := metadataMap["user_agent"]; exists {
							msg.Headers["User-Agent"] = fmt.Sprintf("%v", userAgent)
						}
					}
				}
			}
		}
	}
	
	return msg
}

// convertWebSocketToGRPCPayload converts WebSocket to gRPC format
func (gt *GRPCTranslator) convertWebSocketToGRPCPayload(msg *types.Message) *types.Message {
	grpcPayload := map[string]interface{}{
		"service": "CommunicationService",
		"method":  "ProcessWebSocketMessage",
		"request": map[string]interface{}{
			"connection_id": msg.Headers["websocket-connection-id"],
			"message_type":  msg.Headers["websocket-type"],
			"data":         msg.Payload,
			"origin":       msg.Headers["websocket-origin"],
		},
		"metadata": map[string]interface{}{
			"timestamp":       msg.Timestamp.Unix(),
			"real_time":       true,
			"connection_type": "websocket",
		},
	}
	
	msg.Payload = grpcPayload
	msg.Headers["grpc-service"] = "ai.twodot.platform.CommunicationService"
	msg.Headers["grpc-method"] = "ProcessWebSocketMessage"
	
	return msg
}

// convertGRPCToWebSocketPayload converts gRPC back to WebSocket format
func (gt *GRPCTranslator) convertGRPCToWebSocketPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if request, exists := payloadMap["request"]; exists {
			if requestMap, ok := request.(map[string]interface{}); ok {
				if data, exists := requestMap["data"]; exists {
					msg.Payload = data
				}
				if connId, exists := requestMap["connection_id"]; exists {
					msg.Headers["websocket-connection-id"] = fmt.Sprintf("%v", connId)
				}
				if msgType, exists := requestMap["message_type"]; exists {
					msg.Headers["websocket-type"] = fmt.Sprintf("%v", msgType)
				}
				if origin, exists := requestMap["origin"]; exists {
					msg.Headers["websocket-origin"] = fmt.Sprintf("%v", origin)
				}
			}
		}
	}
	
	return msg
}

// convertA2AToGRPCPayload converts Agent-to-Agent to gRPC format
func (gt *GRPCTranslator) convertA2AToGRPCPayload(msg *types.Message) *types.Message {
	grpcPayload := map[string]interface{}{
		"service": "AgentCommunicationService",
		"method":  "ProcessAgentMessage",
		"request": map[string]interface{}{
			"source_agent":      msg.From,
			"target_agent":      msg.To,
			"message_priority":  msg.Headers["routing-priority"],
			"agent_capabilities": msg.Headers["agent-capabilities"],
			"data":             msg.Payload,
			"a2a_version":      msg.Headers["a2a-version"],
		},
		"metadata": map[string]interface{}{
			"timestamp":        msg.Timestamp.Unix(),
			"protocol_version": "2.0",
			"routing_type":     "agent_to_agent",
		},
	}
	
	msg.Payload = grpcPayload
	msg.Headers["grpc-service"] = "ai.twodot.platform.AgentCommunicationService"
	msg.Headers["grpc-method"] = "ProcessAgentMessage"
	
	return msg
}

// convertGRPCToA2APayload converts gRPC back to A2A format
func (gt *GRPCTranslator) convertGRPCToA2APayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if request, exists := payloadMap["request"]; exists {
			if requestMap, ok := request.(map[string]interface{}); ok {
				if data, exists := requestMap["data"]; exists {
					msg.Payload = data
				}
				if priority, exists := requestMap["message_priority"]; exists {
					msg.Headers["routing-priority"] = fmt.Sprintf("%v", priority)
				}
				if capabilities, exists := requestMap["agent_capabilities"]; exists {
					msg.Headers["agent-capabilities"] = fmt.Sprintf("%v", capabilities)
				}
				if version, exists := requestMap["a2a_version"]; exists {
					msg.Headers["a2a-version"] = fmt.Sprintf("%v", version)
				}
			}
		}
	}
	
	return msg
}

// extractHTTPHeaders extracts HTTP-specific headers
func (gt *GRPCTranslator) extractHTTPHeaders(headers map[string]string) map[string]string {
	httpHeaders := make(map[string]string)
	
	httpSpecific := []string{
		"Content-Type", "Content-Length", "Accept", "User-Agent",
		"Referer", "Host", "Authorization", "Cookie",
	}
	
	for _, header := range httpSpecific {
		if value, exists := headers[header]; exists {
			httpHeaders[header] = value
		}
	}
	
	return httpHeaders
}

// isGRPCSpecificHeader checks if a header is gRPC-specific
func (gt *GRPCTranslator) isGRPCSpecificHeader(header string) bool {
	grpcHeaders := []string{
		"grpc-version", "grpc-encoding", "grpc-accept-encoding",
		"grpc-service", "grpc-method", "grpc-status", "grpc-message",
		"content-type", "x-protocol-source", "x-protocol-target",
	}
	
	for _, grpcHeader := range grpcHeaders {
		if header == grpcHeader {
			return true
		}
	}
	
	return false
}

// SupportedProtocols returns protocols this translator can handle
func (gt *GRPCTranslator) SupportedProtocols() []string {
	return []string{"grpc", "http", "websocket", "a2a"}
}

// IsHealthy checks if the translator is healthy
func (gt *GRPCTranslator) IsHealthy() bool {
	return gt.healthy
}

// GetStats returns translator statistics
func (gt *GRPCTranslator) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"translator_type":        "grpc",
		"translations_completed": gt.stats.TranslationsCompleted,
		"grpc_calls_made":       gt.stats.GRPCCallsMade,
		"grpc_calls_received":   gt.stats.GRPCCallsReceived,
		"avg_call_duration":     gt.stats.AvgCallDuration.String(),
		"error_count":           gt.stats.ErrorCount,
		"protobuf_conversions":  gt.stats.ProtobufConversions,
		"last_translation":      gt.stats.LastTranslation.Format(time.RFC3339),
		"supported_protocols":   gt.SupportedProtocols(),
		"healthy":              gt.healthy,
	}
}

// SimulateGRPCCall simulates a gRPC call for testing
func (gt *GRPCTranslator) SimulateGRPCCall(ctx context.Context, service, method string, request interface{}) (interface{}, error) {
	gt.stats.GRPCCallsMade++
	
	// Simulate gRPC call processing
	response := map[string]interface{}{
		"service":    service,
		"method":     method,
		"status":     "OK",
		"timestamp":  time.Now().Unix(),
		"request_id": fmt.Sprintf("grpc-%d", time.Now().UnixNano()),
		"data":       request,
	}
	
	return response, nil
}