package protocol

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// HTTPTranslator handles HTTP protocol translations
type HTTPTranslator struct {
	config     *config.Config
	httpClient *http.Client
	healthy    bool
	stats      *HTTPTranslatorStats
}

// HTTPTranslatorStats tracks HTTP translator statistics
type HTTPTranslatorStats struct {
	TranslationsCompleted int64         `json:"translations_completed"`
	HTTPRequestsSent      int64         `json:"http_requests_sent"`
	HTTPResponsesReceived int64         `json:"http_responses_received"`
	AvgResponseTime       time.Duration `json:"avg_response_time"`
	ErrorCount           int64         `json:"error_count"`
	LastTranslation      time.Time     `json:"last_translation"`
}

// NewHTTPTranslator creates a new HTTP translator
func NewHTTPTranslator(cfg *config.Config) *HTTPTranslator {
	return &HTTPTranslator{
		config: cfg,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Server.ReadTimeout) * time.Second,
		},
		healthy: true,
		stats:   &HTTPTranslatorStats{},
	}
}

// Translate translates messages to/from HTTP protocol
func (ht *HTTPTranslator) Translate(ctx context.Context, msg *types.Message, fromProtocol, toProtocol string) (*types.Message, error) {
	startTime := time.Now()
	defer func() {
		ht.stats.TranslationsCompleted++
		ht.stats.LastTranslation = time.Now()
		
		// Update average response time
		duration := time.Since(startTime)
		if ht.stats.TranslationsCompleted > 0 {
			totalTime := ht.stats.AvgResponseTime * time.Duration(ht.stats.TranslationsCompleted-1)
			ht.stats.AvgResponseTime = (totalTime + duration) / time.Duration(ht.stats.TranslationsCompleted)
		}
	}()
	
	switch {
	case toProtocol == "http":
		return ht.translateToHTTP(ctx, msg, fromProtocol)
	case fromProtocol == "http":
		return ht.translateFromHTTP(ctx, msg, toProtocol)
	default:
		ht.stats.ErrorCount++
		return nil, fmt.Errorf("HTTP translator cannot handle %s to %s translation", fromProtocol, toProtocol)
	}
}

// translateToHTTP converts any protocol message to HTTP format
func (ht *HTTPTranslator) translateToHTTP(ctx context.Context, msg *types.Message, fromProtocol string) (*types.Message, error) {
	// Create HTTP-compatible message
	httpMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers
	for k, v := range msg.Headers {
		httpMsg.Headers[k] = v
	}
	
	// Add HTTP-specific headers
	httpMsg.Headers["Content-Type"] = "application/json"
	httpMsg.Headers["X-Protocol-Source"] = fromProtocol
	httpMsg.Headers["X-Protocol-Target"] = "http"
	httpMsg.Headers["X-Translation-Time"] = time.Now().Format(time.RFC3339)
	
	// Convert payload based on source protocol
	switch fromProtocol {
	case "grpc":
		httpMsg = ht.convertGRPCToHTTP(httpMsg)
	case "websocket":
		httpMsg = ht.convertWebSocketToHTTP(httpMsg)
	case "a2a":
		httpMsg = ht.convertA2AToHTTP(httpMsg)
	default:
		// For unknown protocols, wrap the payload
		httpMsg.Payload = map[string]interface{}{
			"original_protocol": fromProtocol,
			"original_payload":  msg.Payload,
			"wrapped_at":       time.Now().Format(time.RFC3339),
		}
	}
	
	return httpMsg, nil
}

// translateFromHTTP converts HTTP message to target protocol format
func (ht *HTTPTranslator) translateFromHTTP(ctx context.Context, msg *types.Message, toProtocol string) (*types.Message, error) {
	// Create target protocol message
	targetMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers (excluding HTTP-specific ones)
	for k, v := range msg.Headers {
		if !ht.isHTTPSpecificHeader(k) {
			targetMsg.Headers[k] = v
		}
	}
	
	// Add target protocol headers
	targetMsg.Headers["X-Protocol-Source"] = "http"
	targetMsg.Headers["X-Protocol-Target"] = toProtocol
	targetMsg.Headers["X-Translation-Time"] = time.Now().Format(time.RFC3339)
	
	// Convert payload based on target protocol
	switch toProtocol {
	case "grpc":
		targetMsg = ht.convertHTTPToGRPC(targetMsg)
	case "websocket":
		targetMsg = ht.convertHTTPToWebSocket(targetMsg)
	case "a2a":
		targetMsg = ht.convertHTTPToA2A(targetMsg)
	default:
		// For unknown protocols, unwrap if it was wrapped
		if wrappedPayload, ok := msg.Payload.(map[string]interface{}); ok {
			if originalPayload, exists := wrappedPayload["original_payload"]; exists {
				targetMsg.Payload = originalPayload
			}
		}
	}
	
	return targetMsg, nil
}

// convertGRPCToHTTP converts gRPC message format to HTTP
func (ht *HTTPTranslator) convertGRPCToHTTP(msg *types.Message) *types.Message {
	// gRPC uses protobuf, HTTP uses JSON
	if msg.Payload != nil {
		// Ensure payload is JSON-serializable
		jsonPayload := make(map[string]interface{})
		
		if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
			jsonPayload = payloadMap
		} else {
			jsonPayload["data"] = msg.Payload
		}
		
		jsonPayload["grpc_method"] = msg.Headers["grpc-method"]
		jsonPayload["grpc_service"] = msg.Headers["grpc-service"]
		
		msg.Payload = jsonPayload
	}
	
	// Convert gRPC headers to HTTP headers
	msg.Headers["X-GRPC-Method"] = msg.Headers["grpc-method"]
	msg.Headers["X-GRPC-Service"] = msg.Headers["grpc-service"]
	
	return msg
}

// convertHTTPToGRPC converts HTTP message format to gRPC
func (ht *HTTPTranslator) convertHTTPToGRPC(msg *types.Message) *types.Message {
	// Extract gRPC metadata from HTTP payload
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if grpcMethod, exists := payloadMap["grpc_method"]; exists {
			msg.Headers["grpc-method"] = fmt.Sprintf("%v", grpcMethod)
			delete(payloadMap, "grpc_method")
		}
		if grpcService, exists := payloadMap["grpc_service"]; exists {
			msg.Headers["grpc-service"] = fmt.Sprintf("%v", grpcService)
			delete(payloadMap, "grpc_service")
		}
	}
	
	// Convert HTTP headers to gRPC metadata
	if httpMethod := msg.Headers["X-GRPC-Method"]; httpMethod != "" {
		msg.Headers["grpc-method"] = httpMethod
		delete(msg.Headers, "X-GRPC-Method")
	}
	
	return msg
}

// convertWebSocketToHTTP converts WebSocket message to HTTP
func (ht *HTTPTranslator) convertWebSocketToHTTP(msg *types.Message) *types.Message {
	// WebSocket messages are often real-time, add timestamp
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		payloadMap["websocket_timestamp"] = time.Now().Format(time.RFC3339)
		payloadMap["websocket_type"] = msg.Headers["websocket-type"]
		msg.Payload = payloadMap
	}
	
	msg.Headers["X-WebSocket-Type"] = msg.Headers["websocket-type"]
	msg.Headers["X-WebSocket-Origin"] = msg.Headers["websocket-origin"]
	
	return msg
}

// convertHTTPToWebSocket converts HTTP message to WebSocket
func (ht *HTTPTranslator) convertHTTPToWebSocket(msg *types.Message) *types.Message {
	// Remove HTTP-specific metadata
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		delete(payloadMap, "websocket_timestamp")
		if wsType, exists := payloadMap["websocket_type"]; exists {
			msg.Headers["websocket-type"] = fmt.Sprintf("%v", wsType)
			delete(payloadMap, "websocket_type")
		}
	}
	
	// Convert headers
	if wsType := msg.Headers["X-WebSocket-Type"]; wsType != "" {
		msg.Headers["websocket-type"] = wsType
		delete(msg.Headers, "X-WebSocket-Type")
	}
	
	return msg
}

// convertA2AToHTTP converts Agent-to-Agent message to HTTP
func (ht *HTTPTranslator) convertA2AToHTTP(msg *types.Message) *types.Message {
	// A2A protocol includes agent capabilities and routing info
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		payloadMap["agent_capabilities"] = msg.Headers["agent-capabilities"]
		payloadMap["routing_priority"] = msg.Headers["routing-priority"]
		payloadMap["a2a_version"] = msg.Headers["a2a-version"]
		msg.Payload = payloadMap
	}
	
	msg.Headers["X-Agent-Capabilities"] = msg.Headers["agent-capabilities"]
	msg.Headers["X-Routing-Priority"] = msg.Headers["routing-priority"]
	
	return msg
}

// convertHTTPToA2A converts HTTP message to Agent-to-Agent
func (ht *HTTPTranslator) convertHTTPToA2A(msg *types.Message) *types.Message {
	// Extract A2A metadata from payload
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if capabilities, exists := payloadMap["agent_capabilities"]; exists {
			msg.Headers["agent-capabilities"] = fmt.Sprintf("%v", capabilities)
			delete(payloadMap, "agent_capabilities")
		}
		if priority, exists := payloadMap["routing_priority"]; exists {
			msg.Headers["routing-priority"] = fmt.Sprintf("%v", priority)
			delete(payloadMap, "routing_priority")
		}
		if version, exists := payloadMap["a2a_version"]; exists {
			msg.Headers["a2a-version"] = fmt.Sprintf("%v", version)
			delete(payloadMap, "a2a_version")
		}
	}
	
	// Convert headers
	if capabilities := msg.Headers["X-Agent-Capabilities"]; capabilities != "" {
		msg.Headers["agent-capabilities"] = capabilities
		delete(msg.Headers, "X-Agent-Capabilities")
	}
	
	return msg
}

// isHTTPSpecificHeader checks if a header is HTTP-specific
func (ht *HTTPTranslator) isHTTPSpecificHeader(header string) bool {
	httpHeaders := []string{
		"Content-Type", "Content-Length", "Content-Encoding",
		"Accept", "Accept-Encoding", "Accept-Language",
		"User-Agent", "Referer", "Host", "Connection",
		"X-Protocol-Source", "X-Protocol-Target", "X-Translation-Time",
	}
	
	header = strings.ToLower(header)
	for _, httpHeader := range httpHeaders {
		if strings.ToLower(httpHeader) == header {
			return true
		}
	}
	
	return false
}

// SupportedProtocols returns protocols this translator can handle
func (ht *HTTPTranslator) SupportedProtocols() []string {
	return []string{"http", "grpc", "websocket", "a2a"}
}

// IsHealthy checks if the translator is healthy
func (ht *HTTPTranslator) IsHealthy() bool {
	return ht.healthy
}

// GetStats returns translator statistics
func (ht *HTTPTranslator) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"translator_type":        "http",
		"translations_completed": ht.stats.TranslationsCompleted,
		"http_requests_sent":     ht.stats.HTTPRequestsSent,
		"http_responses_received": ht.stats.HTTPResponsesReceived,
		"avg_response_time":      ht.stats.AvgResponseTime.String(),
		"error_count":           ht.stats.ErrorCount,
		"last_translation":      ht.stats.LastTranslation.Format(time.RFC3339),
		"supported_protocols":    ht.SupportedProtocols(),
		"healthy":               ht.healthy,
	}
}

// SendHTTPRequest sends an HTTP request (for actual HTTP communication)
func (ht *HTTPTranslator) SendHTTPRequest(ctx context.Context, method, url string, payload interface{}, headers map[string]string) (*http.Response, error) {
	ht.stats.HTTPRequestsSent++
	
	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			ht.stats.ErrorCount++
			return nil, fmt.Errorf("failed to marshal payload: %w", err)
		}
		body = bytes.NewReader(jsonData)
	}
	
	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		ht.stats.ErrorCount++
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}
	
	// Set headers
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	
	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	
	resp, err := ht.httpClient.Do(req)
	if err != nil {
		ht.stats.ErrorCount++
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	
	ht.stats.HTTPResponsesReceived++
	return resp, nil
}