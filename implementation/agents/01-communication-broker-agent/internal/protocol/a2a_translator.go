package protocol

import (
	"context"
	"fmt"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// A2ATranslator handles Agent-to-Agent (A2A) protocol translations
type A2ATranslator struct {
	config  *config.Config
	healthy bool
	stats   *A2ATranslatorStats
}

// A2ATranslatorStats tracks A2A translator statistics
type A2ATranslatorStats struct {
	TranslationsCompleted int64         `json:"translations_completed"`
	AgentMessagesRouted  int64         `json:"agent_messages_routed"`
	CapabilityNegotiations int64       `json:"capability_negotiations"`
	AvgNegotiationTime   time.Duration `json:"avg_negotiation_time"`
	ErrorCount           int64         `json:"error_count"`
	LastTranslation      time.Time     `json:"last_translation"`
	AgentCollaborations  int64         `json:"agent_collaborations"`
}

// NewA2ATranslator creates a new A2A translator
func NewA2ATranslator(cfg *config.Config) *A2ATranslator {
	return &A2ATranslator{
		config:  cfg,
		healthy: true,
		stats:   &A2ATranslatorStats{},
	}
}

// Translate translates messages to/from A2A protocol
func (at *A2ATranslator) Translate(ctx context.Context, msg *types.Message, fromProtocol, toProtocol string) (*types.Message, error) {
	startTime := time.Now()
	defer func() {
		at.stats.TranslationsCompleted++
		at.stats.LastTranslation = time.Now()
		
		duration := time.Since(startTime)
		if at.stats.TranslationsCompleted > 0 {
			totalTime := at.stats.AvgNegotiationTime * time.Duration(at.stats.TranslationsCompleted-1)
			at.stats.AvgNegotiationTime = (totalTime + duration) / time.Duration(at.stats.TranslationsCompleted)
		}
	}()
	
	switch {
	case toProtocol == "a2a":
		return at.translateToA2A(ctx, msg, fromProtocol)
	case fromProtocol == "a2a":
		return at.translateFromA2A(ctx, msg, toProtocol)
	default:
		at.stats.ErrorCount++
		return nil, fmt.Errorf("A2A translator cannot handle %s to %s translation", fromProtocol, toProtocol)
	}
}

// translateToA2A converts any protocol message to A2A format
func (at *A2ATranslator) translateToA2A(ctx context.Context, msg *types.Message, fromProtocol string) (*types.Message, error) {
	at.stats.AgentMessagesRouted++
	
	// Create A2A-compatible message
	a2aMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers
	for k, v := range msg.Headers {
		a2aMsg.Headers[k] = v
	}
	
	// Add A2A-specific headers
	a2aMsg.Headers["a2a-version"] = "2.0"
	a2aMsg.Headers["a2a-protocol"] = "TwoDot-Agent-Communication"
	a2aMsg.Headers["agent-id"] = msg.From
	a2aMsg.Headers["target-agent-id"] = msg.To
	a2aMsg.Headers["routing-priority"] = at.determineRoutingPriority(msg)
	a2aMsg.Headers["agent-capabilities"] = at.generateCapabilitiesHeader(msg, fromProtocol)
	a2aMsg.Headers["collaboration-id"] = at.generateCollaborationID()
	a2aMsg.Headers["x-protocol-source"] = fromProtocol
	a2aMsg.Headers["x-protocol-target"] = "a2a"
	
	// Convert payload based on source protocol
	switch fromProtocol {
	case "http":
		a2aMsg = at.convertHTTPToA2APayload(a2aMsg)
	case "grpc":
		a2aMsg = at.convertGRPCToA2APayload(a2aMsg)
	case "websocket":
		a2aMsg = at.convertWebSocketToA2APayload(a2aMsg)
	default:
		// For unknown protocols, wrap in A2A envelope
		a2aMsg.Payload = map[string]interface{}{
			"agent_envelope": map[string]interface{}{
				"original_protocol": fromProtocol,
				"data":             msg.Payload,
				"collaboration_context": map[string]interface{}{
					"task_type":    "protocol_translation",
					"priority":     at.determineRoutingPriority(msg),
					"requires_ack": true,
				},
				"agent_metadata": map[string]interface{}{
					"source_capabilities": at.inferCapabilities(fromProtocol),
					"timestamp":          time.Now().Unix(),
					"message_class":      "inter_agent_communication",
				},
			},
		}
	}
	
	// Mark as agent collaboration
	at.stats.AgentCollaborations++
	
	return a2aMsg, nil
}

// translateFromA2A converts A2A message to target protocol format
func (at *A2ATranslator) translateFromA2A(ctx context.Context, msg *types.Message, toProtocol string) (*types.Message, error) {
	at.stats.AgentMessagesRouted++
	
	// Create target protocol message
	targetMsg := &types.Message{
		ID:        msg.ID,
		Type:      msg.Type,
		From:      msg.From,
		To:        msg.To,
		Payload:   msg.Payload,
		Timestamp: msg.Timestamp,
		Headers:   make(map[string]string),
	}
	
	// Copy original headers (excluding A2A-specific ones)
	for k, v := range msg.Headers {
		if !at.isA2ASpecificHeader(k) {
			targetMsg.Headers[k] = v
		}
	}
	
	// Add target protocol headers with A2A context
	targetMsg.Headers["x-protocol-source"] = "a2a"
	targetMsg.Headers["x-protocol-target"] = toProtocol
	targetMsg.Headers["x-agent-source"] = msg.Headers["agent-id"]
	targetMsg.Headers["x-agent-capabilities"] = msg.Headers["agent-capabilities"]
	targetMsg.Headers["x-collaboration-id"] = msg.Headers["collaboration-id"]
	targetMsg.Headers["x-routing-priority"] = msg.Headers["routing-priority"]
	
	// Convert payload based on target protocol
	switch toProtocol {
	case "http":
		targetMsg = at.convertA2AToHTTPPayload(targetMsg)
	case "grpc":
		targetMsg = at.convertA2AToGRPCPayload(targetMsg)
	case "websocket":
		targetMsg = at.convertA2AToWebSocketPayload(targetMsg)
	default:
		// For unknown protocols, extract data from A2A envelope
		if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
			if envelope, exists := payloadMap["agent_envelope"]; exists {
				if envelopeMap, ok := envelope.(map[string]interface{}); ok {
					if data, exists := envelopeMap["data"]; exists {
						targetMsg.Payload = data
					}
				}
			}
		}
	}
	
	return targetMsg, nil
}

// convertHTTPToA2APayload converts HTTP to A2A agent communication format
func (at *A2ATranslator) convertHTTPToA2APayload(msg *types.Message) *types.Message {
	a2aPayload := map[string]interface{}{
		"agent_envelope": map[string]interface{}{
			"communication_type": "http_to_agent",
			"http_context": map[string]interface{}{
				"method":       msg.Headers["X-HTTP-Method"],
				"url":          msg.Headers["X-HTTP-URL"],
				"headers":      at.extractHTTPHeaders(msg.Headers),
				"body":         msg.Payload,
				"content_type": msg.Headers["Content-Type"],
			},
			"collaboration_context": map[string]interface{}{
				"task_type":         "http_processing",
				"requires_response": true,
				"priority":         msg.Headers["routing-priority"],
				"timeout":          "30s",
			},
			"agent_metadata": map[string]interface{}{
				"source_protocol":    "http",
				"expected_response": "synchronous",
				"capabilities_needed": []string{"http_handling", "request_processing"},
			},
		},
	}
	
	msg.Payload = a2aPayload
	return msg
}

// convertA2AToHTTPPayload converts A2A back to HTTP format
func (at *A2ATranslator) convertA2AToHTTPPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if envelope, exists := payloadMap["agent_envelope"]; exists {
			if envelopeMap, ok := envelope.(map[string]interface{}); ok {
				if httpContext, exists := envelopeMap["http_context"]; exists {
					if httpMap, ok := httpContext.(map[string]interface{}); ok {
						// Extract HTTP data from A2A envelope
						if body, exists := httpMap["body"]; exists {
							msg.Payload = body
						}
						
						// Restore HTTP headers
						if headers, exists := httpMap["headers"]; exists {
							if headerMap, ok := headers.(map[string]interface{}); ok {
								for k, v := range headerMap {
									msg.Headers[k] = fmt.Sprintf("%v", v)
								}
							}
						}
						
						// Set HTTP-specific headers
						if method, exists := httpMap["method"]; exists {
							msg.Headers["X-HTTP-Method"] = fmt.Sprintf("%v", method)
						}
						if url, exists := httpMap["url"]; exists {
							msg.Headers["X-HTTP-URL"] = fmt.Sprintf("%v", url)
						}
						if contentType, exists := httpMap["content_type"]; exists {
							msg.Headers["Content-Type"] = fmt.Sprintf("%v", contentType)
						}
					}
				}
			}
		}
	}
	
	return msg
}

// convertGRPCToA2APayload converts gRPC to A2A agent communication format
func (at *A2ATranslator) convertGRPCToA2APayload(msg *types.Message) *types.Message {
	a2aPayload := map[string]interface{}{
		"agent_envelope": map[string]interface{}{
			"communication_type": "grpc_to_agent",
			"grpc_context": map[string]interface{}{
				"service":        msg.Headers["grpc-service"],
				"method":         msg.Headers["grpc-method"],
				"protobuf_data":  msg.Payload,
				"encoding":       msg.Headers["grpc-encoding"],
				"metadata":       at.extractGRPCMetadata(msg.Headers),
			},
			"collaboration_context": map[string]interface{}{
				"task_type":         "grpc_processing",
				"requires_response": true,
				"streaming":         false,
				"priority":         msg.Headers["routing-priority"],
			},
			"agent_metadata": map[string]interface{}{
				"source_protocol":    "grpc",
				"expected_response": "streaming_or_unary",
				"capabilities_needed": []string{"grpc_handling", "protobuf_processing"},
			},
		},
	}
	
	msg.Payload = a2aPayload
	return msg
}

// convertA2AToGRPCPayload converts A2A back to gRPC format
func (at *A2ATranslator) convertA2AToGRPCPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if envelope, exists := payloadMap["agent_envelope"]; exists {
			if envelopeMap, ok := envelope.(map[string]interface{}); ok {
				if grpcContext, exists := envelopeMap["grpc_context"]; exists {
					if grpcMap, ok := grpcContext.(map[string]interface{}); ok {
						// Extract gRPC data from A2A envelope
						if protobufData, exists := grpcMap["protobuf_data"]; exists {
							msg.Payload = protobufData
						}
						
						// Restore gRPC headers
						if service, exists := grpcMap["service"]; exists {
							msg.Headers["grpc-service"] = fmt.Sprintf("%v", service)
						}
						if method, exists := grpcMap["method"]; exists {
							msg.Headers["grpc-method"] = fmt.Sprintf("%v", method)
						}
						if encoding, exists := grpcMap["encoding"]; exists {
							msg.Headers["grpc-encoding"] = fmt.Sprintf("%v", encoding)
						}
					}
				}
			}
		}
	}
	
	return msg
}

// convertWebSocketToA2APayload converts WebSocket to A2A format
func (at *A2ATranslator) convertWebSocketToA2APayload(msg *types.Message) *types.Message {
	a2aPayload := map[string]interface{}{
		"agent_envelope": map[string]interface{}{
			"communication_type": "websocket_to_agent",
			"websocket_context": map[string]interface{}{
				"connection_id": msg.Headers["websocket-connection-id"],
				"message_type":  msg.Headers["websocket-type"],
				"stream_data":   msg.Payload,
				"real_time":     true,
				"origin":        msg.Headers["websocket-origin"],
			},
			"collaboration_context": map[string]interface{}{
				"task_type":         "realtime_processing",
				"requires_response": false,
				"streaming":         true,
				"priority":         "high", // WebSocket messages are typically real-time
			},
			"agent_metadata": map[string]interface{}{
				"source_protocol":    "websocket",
				"expected_response": "streaming",
				"capabilities_needed": []string{"realtime_handling", "stream_processing"},
			},
		},
	}
	
	msg.Payload = a2aPayload
	return msg
}

// convertA2AToWebSocketPayload converts A2A back to WebSocket format
func (at *A2ATranslator) convertA2AToWebSocketPayload(msg *types.Message) *types.Message {
	if payloadMap, ok := msg.Payload.(map[string]interface{}); ok {
		if envelope, exists := payloadMap["agent_envelope"]; exists {
			if envelopeMap, ok := envelope.(map[string]interface{}); ok {
				if wsContext, exists := envelopeMap["websocket_context"]; exists {
					if wsMap, ok := wsContext.(map[string]interface{}); ok {
						// Extract WebSocket data from A2A envelope
						if streamData, exists := wsMap["stream_data"]; exists {
							msg.Payload = streamData
						}
						
						// Restore WebSocket headers
						if connId, exists := wsMap["connection_id"]; exists {
							msg.Headers["websocket-connection-id"] = fmt.Sprintf("%v", connId)
						}
						if msgType, exists := wsMap["message_type"]; exists {
							msg.Headers["websocket-type"] = fmt.Sprintf("%v", msgType)
						}
						if origin, exists := wsMap["origin"]; exists {
							msg.Headers["websocket-origin"] = fmt.Sprintf("%v", origin)
						}
					}
				}
			}
		}
	}
	
	return msg
}

// determineRoutingPriority determines the routing priority for A2A communication
func (at *A2ATranslator) determineRoutingPriority(msg *types.Message) string {
	// Check existing priority
	if priority := msg.Headers["priority"]; priority != "" {
		return priority
	}
	
	// Determine based on message type
	switch msg.Type {
	case types.MessageTypeEvent:
		return "high"
	case types.MessageTypeRequest:
		return "medium"
	case types.MessageTypeResponse:
		return "medium"
	default:
		return "low"
	}
}

// generateCapabilitiesHeader generates capabilities header for A2A communication
func (at *A2ATranslator) generateCapabilitiesHeader(msg *types.Message, sourceProtocol string) string {
	capabilities := []string{"message_routing", "protocol_translation"}
	
	switch sourceProtocol {
	case "http":
		capabilities = append(capabilities, "http_handling", "rest_api")
	case "grpc":
		capabilities = append(capabilities, "grpc_handling", "protobuf_processing")
	case "websocket":
		capabilities = append(capabilities, "realtime_handling", "stream_processing")
	}
	
	return fmt.Sprintf("[%s]", fmt.Sprintf("\"%s\"", capabilities[0])) // Simplified JSON array
}

// generateCollaborationID generates a unique collaboration ID
func (at *A2ATranslator) generateCollaborationID() string {
	return fmt.Sprintf("collab-%d", time.Now().UnixNano())
}

// inferCapabilities infers capabilities based on protocol
func (at *A2ATranslator) inferCapabilities(protocol string) []string {
	switch protocol {
	case "http":
		return []string{"http_client", "rest_api", "json_processing"}
	case "grpc":
		return []string{"grpc_client", "protobuf_processing", "streaming"}
	case "websocket":
		return []string{"websocket_client", "realtime_communication", "stream_processing"}
	default:
		return []string{"generic_protocol", "message_processing"}
	}
}

// extractHTTPHeaders extracts HTTP headers for A2A context
func (at *A2ATranslator) extractHTTPHeaders(headers map[string]string) map[string]string {
	httpHeaders := make(map[string]string)
	
	for k, v := range headers {
		if !at.isA2ASpecificHeader(k) && k != "x-protocol-source" && k != "x-protocol-target" {
			httpHeaders[k] = v
		}
	}
	
	return httpHeaders
}

// extractGRPCMetadata extracts gRPC metadata for A2A context
func (at *A2ATranslator) extractGRPCMetadata(headers map[string]string) map[string]string {
	grpcMetadata := make(map[string]string)
	
	for k, v := range headers {
		if k == "grpc-timeout" || k == "grpc-encoding" || k == "grpc-accept-encoding" {
			grpcMetadata[k] = v
		}
	}
	
	return grpcMetadata
}

// isA2ASpecificHeader checks if a header is A2A-specific
func (at *A2ATranslator) isA2ASpecificHeader(header string) bool {
	a2aHeaders := []string{
		"a2a-version", "a2a-protocol", "agent-id", "target-agent-id",
		"routing-priority", "agent-capabilities", "collaboration-id",
		"x-protocol-source", "x-protocol-target",
	}
	
	for _, a2aHeader := range a2aHeaders {
		if header == a2aHeader {
			return true
		}
	}
	
	return false
}

// SupportedProtocols returns protocols this translator can handle
func (at *A2ATranslator) SupportedProtocols() []string {
	return []string{"a2a", "http", "grpc", "websocket"}
}

// IsHealthy checks if the translator is healthy
func (at *A2ATranslator) IsHealthy() bool {
	return at.healthy
}

// GetStats returns translator statistics
func (at *A2ATranslator) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"translator_type":         "a2a",
		"translations_completed":  at.stats.TranslationsCompleted,
		"agent_messages_routed":   at.stats.AgentMessagesRouted,
		"capability_negotiations": at.stats.CapabilityNegotiations,
		"avg_negotiation_time":    at.stats.AvgNegotiationTime.String(),
		"error_count":            at.stats.ErrorCount,
		"agent_collaborations":   at.stats.AgentCollaborations,
		"last_translation":       at.stats.LastTranslation.Format(time.RFC3339),
		"supported_protocols":    at.SupportedProtocols(),
		"healthy":               at.healthy,
	}
}