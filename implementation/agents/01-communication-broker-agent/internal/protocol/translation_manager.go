package protocol

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ai.twodot.com/platform/agents/communication-broker/internal/config"
	"ai.twodot.com/platform/agents/communication-broker/pkg/types"
)

// TranslationManager manages protocol translation between different communication protocols
type TranslationManager struct {
	config      *config.Config
	translators map[string]ProtocolTranslator
	mu          sync.RWMutex
	metrics     *TranslationMetrics
}

// ProtocolTranslator defines the interface for protocol translators
type ProtocolTranslator interface {
	// Translate translates a message from one protocol to another
	Translate(ctx context.Context, msg *types.Message, fromProtocol, toProtocol string) (*types.Message, error)
	
	// SupportedProtocols returns the protocols this translator supports
	SupportedProtocols() []string
	
	// IsHealthy checks if the translator is healthy
	IsHealthy() bool
	
	// GetStats returns translator statistics
	GetStats() map[string]interface{}
}

// TranslationRequest represents a protocol translation request
type TranslationRequest struct {
	Message      *types.Message `json:"message"`
	FromProtocol string         `json:"from_protocol"`
	ToProtocol   string         `json:"to_protocol"`
	Options      map[string]interface{} `json:"options,omitempty"`
}

// TranslationResponse represents a protocol translation response
type TranslationResponse struct {
	TranslatedMessage *types.Message             `json:"translated_message"`
	Metadata         map[string]interface{}     `json:"metadata"`
	Timestamp        time.Time                  `json:"timestamp"`
}

// TranslationMetrics tracks protocol translation metrics
type TranslationMetrics struct {
	TotalTranslations int64            `json:"total_translations"`
	SuccessfulTranslations int64       `json:"successful_translations"`
	FailedTranslations int64           `json:"failed_translations"`
	TranslationsByProtocol map[string]int64 `json:"translations_by_protocol"`
	AvgTranslationTime time.Duration    `json:"avg_translation_time"`
	LastTranslation   time.Time        `json:"last_translation"`
	mu                sync.RWMutex
}

// NewTranslationManager creates a new protocol translation manager
func NewTranslationManager(cfg *config.Config) *TranslationManager {
	tm := &TranslationManager{
		config:      cfg,
		translators: make(map[string]ProtocolTranslator),
		metrics: &TranslationMetrics{
			TranslationsByProtocol: make(map[string]int64),
		},
	}
	
	// Register built-in translators
	tm.registerBuiltinTranslators()
	
	return tm
}

// registerBuiltinTranslators registers the built-in protocol translators
func (tm *TranslationManager) registerBuiltinTranslators() {
	// HTTP translator
	httpTranslator := NewHTTPTranslator(tm.config)
	tm.RegisterTranslator("http", httpTranslator)
	
	// gRPC translator
	grpcTranslator := NewGRPCTranslator(tm.config)
	tm.RegisterTranslator("grpc", grpcTranslator)
	
	// WebSocket translator
	wsTranslator := NewWebSocketTranslator(tm.config)
	tm.RegisterTranslator("websocket", wsTranslator)
	
	// A2A (Agent-to-Agent) translator
	a2aTranslator := NewA2ATranslator(tm.config)
	tm.RegisterTranslator("a2a", a2aTranslator)
}

// RegisterTranslator registers a new protocol translator
func (tm *TranslationManager) RegisterTranslator(protocol string, translator ProtocolTranslator) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	tm.translators[protocol] = translator
}

// GetTranslator gets a translator for a specific protocol
func (tm *TranslationManager) GetTranslator(protocol string) (ProtocolTranslator, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	translator, exists := tm.translators[protocol]
	return translator, exists
}

// TranslateMessage translates a message between protocols
func (tm *TranslationManager) TranslateMessage(ctx context.Context, req *TranslationRequest) (*TranslationResponse, error) {
	startTime := time.Now()
	
	// Validate request
	if req.Message == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}
	
	if req.FromProtocol == "" || req.ToProtocol == "" {
		return nil, fmt.Errorf("source and target protocols must be specified")
	}
	
	// If protocols are the same, return original message
	if req.FromProtocol == req.ToProtocol {
		return &TranslationResponse{
			TranslatedMessage: req.Message,
			Metadata: map[string]interface{}{
				"translation_type": "none",
				"reason":          "same_protocol",
			},
			Timestamp: time.Now(),
		}, nil
	}
	
	// Get appropriate translator
	translator, exists := tm.GetTranslator(req.ToProtocol)
	if !exists {
		tm.recordTranslation(req.FromProtocol, req.ToProtocol, false, time.Since(startTime))
		return nil, fmt.Errorf("no translator found for protocol: %s", req.ToProtocol)
	}
	
	// Check translator health
	if !translator.IsHealthy() {
		tm.recordTranslation(req.FromProtocol, req.ToProtocol, false, time.Since(startTime))
		return nil, fmt.Errorf("translator for protocol %s is unhealthy", req.ToProtocol)
	}
	
	// Perform translation
	translatedMsg, err := translator.Translate(ctx, req.Message, req.FromProtocol, req.ToProtocol)
	if err != nil {
		tm.recordTranslation(req.FromProtocol, req.ToProtocol, false, time.Since(startTime))
		return nil, fmt.Errorf("translation failed: %w", err)
	}
	
	// Record successful translation
	tm.recordTranslation(req.FromProtocol, req.ToProtocol, true, time.Since(startTime))
	
	// Prepare response
	response := &TranslationResponse{
		TranslatedMessage: translatedMsg,
		Metadata: map[string]interface{}{
			"from_protocol":      req.FromProtocol,
			"to_protocol":        req.ToProtocol,
			"translation_time":   time.Since(startTime),
			"translator_stats":   translator.GetStats(),
		},
		Timestamp: time.Now(),
	}
	
	return response, nil
}

// GetSupportedProtocols returns all supported protocols
func (tm *TranslationManager) GetSupportedProtocols() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	protocols := make([]string, 0, len(tm.translators))
	for protocol := range tm.translators {
		protocols = append(protocols, protocol)
	}
	
	return protocols
}

// GetTranslationMatrix returns the translation capability matrix
func (tm *TranslationManager) GetTranslationMatrix() map[string][]string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	matrix := make(map[string][]string)
	
	for protocol, translator := range tm.translators {
		matrix[protocol] = translator.SupportedProtocols()
	}
	
	return matrix
}

// recordTranslation records translation metrics
func (tm *TranslationManager) recordTranslation(fromProtocol, toProtocol string, success bool, duration time.Duration) {
	tm.metrics.mu.Lock()
	defer tm.metrics.mu.Unlock()
	
	tm.metrics.TotalTranslations++
	tm.metrics.LastTranslation = time.Now()
	
	if success {
		tm.metrics.SuccessfulTranslations++
	} else {
		tm.metrics.FailedTranslations++
	}
	
	// Update protocol-specific counters
	translationKey := fmt.Sprintf("%s_to_%s", fromProtocol, toProtocol)
	tm.metrics.TranslationsByProtocol[translationKey]++
	
	// Update average translation time
	if tm.metrics.TotalTranslations > 0 {
		totalTime := tm.metrics.AvgTranslationTime * time.Duration(tm.metrics.TotalTranslations-1)
		tm.metrics.AvgTranslationTime = (totalTime + duration) / time.Duration(tm.metrics.TotalTranslations)
	}
}

// GetMetrics returns current translation metrics
func (tm *TranslationManager) GetMetrics() *TranslationMetrics {
	tm.metrics.mu.RLock()
	defer tm.metrics.mu.RUnlock()
	
	// Return a copy to prevent external modifications
	metrics := &TranslationMetrics{
		TotalTranslations:      tm.metrics.TotalTranslations,
		SuccessfulTranslations: tm.metrics.SuccessfulTranslations,
		FailedTranslations:     tm.metrics.FailedTranslations,
		TranslationsByProtocol: make(map[string]int64),
		AvgTranslationTime:     tm.metrics.AvgTranslationTime,
		LastTranslation:        tm.metrics.LastTranslation,
	}
	
	for k, v := range tm.metrics.TranslationsByProtocol {
		metrics.TranslationsByProtocol[k] = v
	}
	
	return metrics
}

// GetTranslatorHealth returns health status of all translators
func (tm *TranslationManager) GetTranslatorHealth() map[string]bool {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	health := make(map[string]bool)
	for protocol, translator := range tm.translators {
		health[protocol] = translator.IsHealthy()
	}
	
	return health
}

// GetDetailedStats returns detailed statistics for all translators
func (tm *TranslationManager) GetDetailedStats() map[string]interface{} {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	stats := make(map[string]interface{})
	
	for protocol, translator := range tm.translators {
		stats[protocol] = translator.GetStats()
	}
	
	// Add manager-level stats
	stats["manager"] = map[string]interface{}{
		"total_translators": len(tm.translators),
		"supported_protocols": tm.GetSupportedProtocols(),
		"translation_matrix":  tm.GetTranslationMatrix(),
		"metrics":            tm.GetMetrics(),
		"health":             tm.GetTranslatorHealth(),
	}
	
	return stats
}

// ValidateTranslationPath validates if a translation path is supported
func (tm *TranslationManager) ValidateTranslationPath(fromProtocol, toProtocol string) error {
	if fromProtocol == toProtocol {
		return nil // Same protocol is always valid
	}
	
	translator, exists := tm.GetTranslator(toProtocol)
	if !exists {
		return fmt.Errorf("no translator available for target protocol: %s", toProtocol)
	}
	
	supportedProtocols := translator.SupportedProtocols()
	for _, protocol := range supportedProtocols {
		if protocol == fromProtocol {
			return nil
		}
	}
	
	return fmt.Errorf("translation from %s to %s is not supported", fromProtocol, toProtocol)
}

// OptimizeTranslations provides optimization suggestions
func (tm *TranslationManager) OptimizeTranslations() []string {
	metrics := tm.GetMetrics()
	suggestions := make([]string, 0)
	
	// Analyze metrics and provide suggestions
	if metrics.FailedTranslations > 0 {
		failureRate := float64(metrics.FailedTranslations) / float64(metrics.TotalTranslations)
		if failureRate > 0.1 {
			suggestions = append(suggestions, fmt.Sprintf("High failure rate detected (%.1f%%). Consider investigating translator health.", failureRate*100))
		}
	}
	
	if metrics.AvgTranslationTime > 100*time.Millisecond {
		suggestions = append(suggestions, "Average translation time is high. Consider optimizing translators or adding caching.")
	}
	
	// Check for unused protocols
	matrix := tm.GetTranslationMatrix()
	if len(matrix) > 4 {
		suggestions = append(suggestions, "Many protocols registered. Consider removing unused translators to improve performance.")
	}
	
	if len(suggestions) == 0 {
		suggestions = append(suggestions, "Translation performance is optimal.")
	}
	
	return suggestions
}