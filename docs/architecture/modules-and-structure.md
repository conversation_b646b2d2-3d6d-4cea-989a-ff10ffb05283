# Modules and Folder Structure Documentation

## Overview

This document provides a comprehensive breakdown of the AI-Native Agent Platform's modular architecture, folder structure, and component organization. The platform follows a microservices architecture with clear separation of concerns and modular design principles.

## Table of Contents

1. [Project Structure Overview](#project-structure-overview)
2. [Core Modules](#core-modules)
3. [Agent Modules](#agent-modules)
4. [Infrastructure Modules](#infrastructure-modules)
5. [Platform Services](#platform-services)
6. [Shared Libraries](#shared-libraries)
7. [Configuration Management](#configuration-management)
8. [Build and Deployment](#build-and-deployment)
9. [Module Dependencies](#module-dependencies)
10. [Development Guidelines](#development-guidelines)

---

## Project Structure Overview

```
ai-platform/
├── agents/                           # Agent implementations and templates
│   ├── templates/                    # Reusable agent templates
│   │   ├── python/                   # Python agent templates
│   │   ├── java/                     # Java agent templates
│   │   ├── go/                       # Go agent templates
│   │   └── javascript/               # Node.js agent templates
│   ├── examples/                     # Example agent implementations
│   ├── runtime/                      # Agent runtime components
│   └── sdk/                          # Agent development SDKs
│
├── meta-agents/                      # Platform meta-agents
│   ├── agent-factory/                # Agent creation and management
│   ├── task-orchestrator/            # Workflow orchestration
│   ├── resource-manager/             # Resource allocation and scaling
│   ├── communication-broker/         # Inter-agent communication
│   └── lifecycle-manager/            # Agent lifecycle management
│
├── platform/                        # Core platform services
│   ├── api-gateway/                  # API gateway and routing
│   ├── auth-service/                 # Authentication and authorization
│   ├── dashboard/                    # Web interface
│   ├── workflow-engine/              # Workflow execution engine
│   ├── monitoring/                   # Monitoring and observability
│   └── data-service/                 # Data management service
│
├── infrastructure/                   # Infrastructure components
│   ├── kubernetes/                   # Kubernetes manifests
│   ├── helm-charts/                  # Helm charts
│   ├── terraform/                    # Infrastructure as code
│   ├── monitoring/                   # Monitoring stack
│   └── security/                     # Security configurations
│
├── shared/                          # Shared libraries and utilities
│   ├── common/                      # Common utilities
│   ├── proto/                       # Protocol buffer definitions
│   ├── configs/                     # Shared configurations
│   └── libraries/                   # Shared libraries
│
├── tools/                           # Development and operational tools
│   ├── cli/                         # Command-line interface
│   ├── dev-tools/                   # Development utilities
│   ├── migration/                   # Database migration tools
│   └── testing/                     # Testing utilities
│
├── tests/                           # Test suites
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   ├── e2e/                         # End-to-end tests
│   └── performance/                 # Performance tests
│
├── docs/                            # Documentation
│   ├── architecture/                # Architecture documentation
│   ├── api/                         # API documentation
│   ├── tutorials/                   # Tutorials and guides
│   ├── deployment/                  # Deployment guides
│   └── examples/                    # Example configurations
│
├── scripts/                         # Build and utility scripts
│   ├── build/                       # Build scripts
│   ├── deployment/                  # Deployment scripts
│   └── maintenance/                 # Maintenance scripts
│
├── configs/                         # Configuration files
│   ├── environments/                # Environment-specific configs
│   ├── security/                    # Security configurations
│   └── monitoring/                  # Monitoring configurations
│
├── BUILD.bazel                      # Bazel build configuration
├── WORKSPACE                        # Bazel workspace configuration
├── docker-compose.yml               # Local development setup
├── Makefile                         # Build automation
└── README.md                        # Project README
```

---

## Core Modules

### 1. Agent Factory Module

**Location**: `meta-agents/agent-factory/`

**Purpose**: Handles agent creation, templating, and deployment lifecycle.

```
agent-factory/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── io/platform/agentfactory/
│   │   │       ├── controller/          # REST controllers
│   │   │       ├── service/             # Business logic services
│   │   │       ├── repository/          # Data access layer
│   │   │       ├── model/               # Data models
│   │   │       ├── config/              # Configuration classes
│   │   │       └── factory/             # Agent creation factories
│   │   └── resources/
│   │       ├── application.yml          # Application configuration
│   │       ├── templates/               # Agent templates
│   │       └── schemas/                 # JSON schemas
│   └── test/                           # Test classes
├── docker/
│   ├── Dockerfile                      # Container definition
│   └── docker-compose.yml             # Local development
├── k8s/
│   ├── deployment.yaml                 # Kubernetes deployment
│   ├── service.yaml                    # Kubernetes service
│   └── configmap.yaml                  # Configuration maps
├── BUILD.bazel                         # Bazel build file
└── pom.xml                            # Maven configuration
```

**Key Components**:

- **AgentTemplateService**: Manages agent templates and configurations
- **AgentDeploymentService**: Handles agent deployment to Kubernetes
- **ResourceCalculatorService**: Calculates resource requirements
- **ValidationService**: Validates agent configurations
- **LifecycleController**: Manages agent lifecycle events

### 2. Task Orchestrator Module

**Location**: `meta-agents/task-orchestrator/`

**Purpose**: Orchestrates workflow execution and task scheduling.

```
task-orchestrator/
├── src/
│   ├── main/
│   │   ├── go/
│   │   │   └── orchestrator/
│   │   │       ├── cmd/                 # Application entry points
│   │   │       ├── internal/            # Private application code
│   │   │       │   ├── api/             # API handlers
│   │   │       │   ├── workflow/        # Workflow engine
│   │   │       │   ├── scheduler/       # Task scheduler
│   │   │       │   ├── executor/        # Task executor
│   │   │       │   ├── storage/         # Data storage
│   │   │       │   └── config/          # Configuration
│   │   │       ├── pkg/                 # Public packages
│   │   │       │   ├── models/          # Data models
│   │   │       │   ├── client/          # Client libraries
│   │   │       │   └── utils/           # Utilities
│   │   │       └── proto/               # Protocol buffers
│   │   └── resources/
│   │       ├── config.yaml              # Configuration
│   │       └── migrations/              # Database migrations
│   └── test/                           # Test files
├── docker/
│   ├── Dockerfile                      # Container definition
│   └── docker-compose.yml             # Development setup
├── k8s/
│   ├── deployment.yaml                 # Kubernetes deployment
│   ├── service.yaml                    # Service definition
│   └── rbac.yaml                       # RBAC configuration
├── BUILD.bazel                         # Bazel build file
├── go.mod                             # Go modules
└── go.sum                             # Go checksums
```

**Key Components**:

- **WorkflowEngine**: Core workflow execution engine
- **TaskScheduler**: Schedules and manages task execution
- **DependencyResolver**: Resolves task dependencies
- **StateManager**: Manages workflow execution state
- **EventHandler**: Handles workflow events and notifications

### 3. Resource Manager Module

**Location**: `meta-agents/resource-manager/`

**Purpose**: Manages resource allocation, scaling, and optimization.

```
resource-manager/
├── src/
│   ├── main/
│   │   ├── python/
│   │   │   └── resource_manager/
│   │   │       ├── api/                 # API endpoints
│   │   │       ├── core/                # Core logic
│   │   │       │   ├── allocator.py     # Resource allocation
│   │   │       │   ├── scaler.py        # Auto-scaling logic
│   │   │       │   ├── optimizer.py     # Resource optimization
│   │   │       │   └── monitor.py       # Resource monitoring
│   │   │       ├── models/              # Data models
│   │   │       ├── services/            # Business services
│   │   │       ├── utils/               # Utility functions
│   │   │       └── config/              # Configuration
│   │   └── resources/
│   │       ├── config.yaml              # Application config
│   │       └── schemas/                 # Data schemas
│   └── test/                           # Test files
├── requirements/
│   ├── base.txt                        # Base dependencies
│   ├── dev.txt                         # Development dependencies
│   └── prod.txt                        # Production dependencies
├── docker/
│   ├── Dockerfile                      # Container definition
│   └── docker-compose.yml             # Development setup
├── k8s/
│   ├── deployment.yaml                 # Kubernetes deployment
│   ├── service.yaml                    # Service definition
│   └── hpa.yaml                        # Horizontal Pod Autoscaler
├── BUILD.bazel                         # Bazel build file
└── setup.py                           # Python package setup
```

**Key Components**:

- **ResourceAllocator**: Allocates resources to agents and workflows
- **AutoScaler**: Handles automatic scaling based on metrics
- **ResourceOptimizer**: Optimizes resource utilization
- **MetricsCollector**: Collects resource usage metrics
- **PolicyEngine**: Applies resource policies and constraints

---

## Agent Modules

### 4. Python Agent Template

**Location**: `agents/templates/python/`

**Purpose**: Provides template and runtime for Python-based agents.

```
python/
├── agent_template/
│   ├── src/
│   │   ├── agent/
│   │   │   ├── __init__.py
│   │   │   ├── base_agent.py           # Base agent class
│   │   │   ├── message_handler.py      # Message handling
│   │   │   ├── task_processor.py       # Task processing
│   │   │   ├── ai_service.py           # AI model integration
│   │   │   └── config_manager.py       # Configuration management
│   │   ├── handlers/
│   │   │   ├── __init__.py
│   │   │   ├── http_handler.py         # HTTP request handler
│   │   │   ├── event_handler.py        # Event handler
│   │   │   └── workflow_handler.py     # Workflow handler
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── data_service.py         # Data processing
│   │   │   ├── integration_service.py  # External integrations
│   │   │   └── monitoring_service.py   # Health monitoring
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── request_models.py       # Request models
│   │   │   ├── response_models.py      # Response models
│   │   │   └── config_models.py        # Configuration models
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── logger.py               # Logging utilities
│   │       ├── validator.py            # Input validation
│   │       └── helpers.py              # Helper functions
│   ├── tests/
│   │   ├── unit/                       # Unit tests
│   │   ├── integration/                # Integration tests
│   │   └── fixtures/                   # Test fixtures
│   ├── config/
│   │   ├── development.yaml            # Dev configuration
│   │   ├── staging.yaml                # Staging configuration
│   │   └── production.yaml             # Production configuration
│   ├── docker/
│   │   ├── Dockerfile                  # Container definition
│   │   └── requirements.txt            # Python dependencies
│   ├── k8s/
│   │   ├── deployment-template.yaml    # Deployment template
│   │   └── service-template.yaml       # Service template
│   ├── BUILD.bazel                     # Bazel build file
│   └── setup.py                        # Package setup
├── runtime/
│   ├── src/
│   │   ├── runtime/
│   │   │   ├── __init__.py
│   │   │   ├── agent_runtime.py        # Agent runtime engine
│   │   │   ├── lifecycle_manager.py    # Lifecycle management
│   │   │   ├── communication.py        # Communication layer
│   │   │   └── health_monitor.py       # Health monitoring
│   │   └── platform/
│   │       ├── __init__.py
│   │       ├── platform_client.py      # Platform API client
│   │       ├── message_bus.py          # Message bus client
│   │       └── metrics_client.py       # Metrics reporting
│   ├── tests/
│   └── BUILD.bazel
└── sdk/
    ├── src/
    │   ├── platform_sdk/
    │   │   ├── __init__.py
    │   │   ├── agent.py                 # Agent SDK
    │   │   ├── workflow.py              # Workflow SDK
    │   │   ├── client.py                # Platform client
    │   │   └── decorators.py            # Utility decorators
    │   └── examples/
    │       ├── simple_agent.py          # Simple agent example
    │       ├── data_processor.py        # Data processing agent
    │       └── ai_assistant.py          # AI assistant agent
    ├── tests/
    ├── docs/
    ├── BUILD.bazel
    └── setup.py
```

### 5. Java Agent Template

**Location**: `agents/templates/java/`

**Purpose**: Provides template and runtime for Java-based agents.

```
java/
├── agent-template/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── io/platform/agent/
│   │   │   │       ├── core/            # Core agent functionality
│   │   │   │       ├── handlers/        # Request handlers
│   │   │   │       ├── services/        # Business services
│   │   │   │       ├── models/          # Data models
│   │   │   │       ├── config/          # Configuration
│   │   │   │       └── utils/           # Utilities
│   │   │   └── resources/
│   │   │       ├── application.yml      # Spring configuration
│   │   │       └── logback.xml          # Logging configuration
│   │   └── test/                       # Test classes
│   ├── docker/
│   │   ├── Dockerfile                  # Container definition
│   │   └── docker-compose.yml         # Development setup
│   ├── k8s/
│   │   ├── deployment-template.yaml    # Deployment template
│   │   └── service-template.yaml       # Service template
│   ├── BUILD.bazel                     # Bazel build file
│   └── pom.xml                        # Maven configuration
├── runtime/
│   ├── src/
│   │   ├── main/
│   │   │   └── java/
│   │   │       └── io/platform/runtime/
│   │   │           ├── AgentRuntime.java        # Runtime engine
│   │   │           ├── LifecycleManager.java    # Lifecycle management
│   │   │           ├── CommunicationLayer.java  # Communication
│   │   │           └── HealthMonitor.java       # Health monitoring
│   │   └── test/
│   ├── BUILD.bazel
│   └── pom.xml
└── sdk/
    ├── src/
    │   ├── main/
    │   │   └── java/
    │   │       └── io/platform/sdk/
    │   │           ├── Agent.java               # Agent SDK
    │   │           ├── WorkflowClient.java      # Workflow client
    │   │           ├── PlatformClient.java      # Platform client
    │   │           └── annotations/             # Annotations
    │   └── test/
    ├── examples/
    │   ├── SimpleAgent.java            # Simple agent example
    │   ├── DataProcessor.java          # Data processing agent
    │   └── AIAssistant.java            # AI assistant agent
    ├── BUILD.bazel
    └── pom.xml
```

---

## Infrastructure Modules

### 6. Kubernetes Infrastructure

**Location**: `infrastructure/kubernetes/`

**Purpose**: Kubernetes manifests and configurations for platform deployment.

```
kubernetes/
├── base/                               # Base Kubernetes manifests
│   ├── namespace.yaml                  # Namespace definition
│   ├── rbac/                           # RBAC configurations
│   │   ├── service-accounts.yaml       # Service accounts
│   │   ├── cluster-roles.yaml          # Cluster roles
│   │   └── role-bindings.yaml          # Role bindings
│   ├── network-policies/               # Network policies
│   │   ├── default-deny.yaml           # Default deny policy
│   │   ├── api-gateway-policy.yaml     # API gateway policy
│   │   └── agent-communication.yaml    # Agent communication policy
│   └── storage/                        # Storage configurations
│       ├── storage-classes.yaml        # Storage classes
│       └── persistent-volumes.yaml     # Persistent volumes
├── platform/                          # Platform services
│   ├── api-gateway/
│   │   ├── deployment.yaml             # API gateway deployment
│   │   ├── service.yaml                # Service definition
│   │   ├── ingress.yaml                # Ingress configuration
│   │   └── configmap.yaml              # Configuration map
│   ├── auth-service/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── secret.yaml                 # Authentication secrets
│   ├── dashboard/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── configmap.yaml
│   └── workflow-engine/
│       ├── deployment.yaml
│       ├── service.yaml
│       └── configmap.yaml
├── meta-agents/                        # Meta-agent deployments
│   ├── agent-factory/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── hpa.yaml                    # Horizontal Pod Autoscaler
│   ├── task-orchestrator/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── hpa.yaml
│   └── resource-manager/
│       ├── deployment.yaml
│       ├── service.yaml
│       └── hpa.yaml
├── data/                               # Data services
│   ├── postgresql/
│   │   ├── statefulset.yaml            # PostgreSQL StatefulSet
│   │   ├── service.yaml                # Service definition
│   │   ├── configmap.yaml              # Configuration
│   │   └── secret.yaml                 # Database secrets
│   ├── redis/
│   │   ├── deployment.yaml             # Redis deployment
│   │   ├── service.yaml                # Service definition
│   │   └── configmap.yaml              # Configuration
│   └── elasticsearch/
│       ├── statefulset.yaml            # Elasticsearch cluster
│       ├── service.yaml                # Service definition
│       └── configmap.yaml              # Configuration
├── monitoring/                         # Monitoring stack
│   ├── prometheus/
│   │   ├── deployment.yaml             # Prometheus deployment
│   │   ├── service.yaml                # Service definition
│   │   ├── configmap.yaml              # Prometheus configuration
│   │   └── rbac.yaml                   # Monitoring RBAC
│   ├── grafana/
│   │   ├── deployment.yaml             # Grafana deployment
│   │   ├── service.yaml                # Service definition
│   │   ├── configmap.yaml              # Grafana configuration
│   │   └── dashboards/                 # Dashboard configurations
│   └── jaeger/
│       ├── deployment.yaml             # Jaeger deployment
│       ├── service.yaml                # Service definition
│       └── configmap.yaml              # Jaeger configuration
└── overlays/                           # Environment-specific overlays
    ├── development/
    │   ├── kustomization.yaml           # Development overlay
    │   └── patches/                     # Development patches
    ├── staging/
    │   ├── kustomization.yaml           # Staging overlay
    │   └── patches/                     # Staging patches
    └── production/
        ├── kustomization.yaml           # Production overlay
        └── patches/                     # Production patches
```

### 7. Helm Charts

**Location**: `infrastructure/helm-charts/`

**Purpose**: Helm charts for easy deployment and configuration management.

```
helm-charts/
├── ai-platform/                        # Main platform chart
│   ├── Chart.yaml                      # Chart metadata
│   ├── values.yaml                     # Default values
│   ├── templates/                      # Kubernetes templates
│   │   ├── deployment.yaml             # Deployment template
│   │   ├── service.yaml                # Service template
│   │   ├── ingress.yaml                # Ingress template
│   │   ├── configmap.yaml              # ConfigMap template
│   │   ├── secret.yaml                 # Secret template
│   │   ├── rbac.yaml                   # RBAC template
│   │   └── _helpers.tpl                # Template helpers
│   ├── charts/                         # Subchart dependencies
│   │   ├── postgresql/                 # PostgreSQL subchart
│   │   ├── redis/                      # Redis subchart
│   │   └── elasticsearch/              # Elasticsearch subchart
│   └── values/                         # Environment-specific values
│       ├── development.yaml            # Development values
│       ├── staging.yaml                # Staging values
│       └── production.yaml             # Production values
├── agent-runtime/                      # Agent runtime chart
│   ├── Chart.yaml
│   ├── values.yaml
│   ├── templates/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   ├── configmap.yaml
│   │   └── hpa.yaml
│   └── values/
│       ├── python-agent.yaml          # Python agent values
│       ├── java-agent.yaml            # Java agent values
│       └── go-agent.yaml              # Go agent values
├── monitoring/                         # Monitoring stack chart
│   ├── Chart.yaml
│   ├── values.yaml
│   ├── templates/
│   │   ├── prometheus/
│   │   ├── grafana/
│   │   └── jaeger/
│   └── dashboards/                     # Grafana dashboards
└── security/                           # Security components chart
    ├── Chart.yaml
    ├── values.yaml
    ├── templates/
    │   ├── cert-manager/
    │   ├── vault/
    │   └── policy-engine/
    └── policies/                       # Security policies
```

---

## Platform Services

### 8. API Gateway Module

**Location**: `platform/api-gateway/`

**Purpose**: Central API gateway for routing and cross-cutting concerns.

```
api-gateway/
├── src/
│   ├── main/
│   │   ├── go/
│   │   │   └── gateway/
│   │   │       ├── cmd/                 # Application entry point
│   │   │       ├── internal/            # Private packages
│   │   │       │   ├── api/             # API handlers
│   │   │       │   ├── auth/            # Authentication middleware
│   │   │       │   ├── ratelimit/       # Rate limiting
│   │   │       │   ├── proxy/           # Reverse proxy
│   │   │       │   ├── middleware/      # Custom middleware
│   │   │       │   └── config/          # Configuration
│   │   │       ├── pkg/                 # Public packages
│   │   │       │   ├── models/          # Data models
│   │   │       │   ├── client/          # HTTP clients
│   │   │       │   └── utils/           # Utilities
│   │   │       └── proto/               # gRPC definitions
│   │   └── resources/
│   │       ├── config.yaml              # Configuration
│   │       ├── routes.yaml              # Route definitions
│   │       └── policies.yaml            # Security policies
│   └── test/                           # Test files
├── configs/
│   ├── development.yaml                # Development config
│   ├── staging.yaml                    # Staging config
│   └── production.yaml                 # Production config
├── docker/
│   ├── Dockerfile                      # Container definition
│   └── docker-compose.yml             # Local development
├── k8s/
│   ├── deployment.yaml                 # Kubernetes deployment
│   ├── service.yaml                    # Service definition
│   ├── ingress.yaml                    # Ingress configuration
│   └── configmap.yaml                  # Configuration map
├── BUILD.bazel                         # Bazel build file
├── go.mod                             # Go modules
└── go.sum                             # Go checksums
```

### 9. Dashboard Module

**Location**: `platform/dashboard/`

**Purpose**: Web-based user interface for platform management.

```
dashboard/
├── frontend/                           # React frontend
│   ├── public/
│   │   ├── index.html                  # HTML template
│   │   ├── favicon.ico                 # Favicon
│   │   └── manifest.json               # PWA manifest
│   ├── src/
│   │   ├── components/                 # React components
│   │   │   ├── common/                 # Common components
│   │   │   ├── agents/                 # Agent management
│   │   │   ├── workflows/              # Workflow management
│   │   │   ├── monitoring/             # Monitoring dashboards
│   │   │   └── settings/               # Settings pages
│   │   ├── pages/                      # Page components
│   │   │   ├── Dashboard.tsx           # Main dashboard
│   │   │   ├── Agents.tsx              # Agents page
│   │   │   ├── Workflows.tsx           # Workflows page
│   │   │   └── Settings.tsx            # Settings page
│   │   ├── services/                   # API services
│   │   │   ├── api.ts                  # API client
│   │   │   ├── auth.ts                 # Authentication
│   │   │   └── websocket.ts            # WebSocket client
│   │   ├── store/                      # State management
│   │   │   ├── index.ts                # Store configuration
│   │   │   ├── agents.ts               # Agent state
│   │   │   ├── workflows.ts            # Workflow state
│   │   │   └── auth.ts                 # Auth state
│   │   ├── utils/                      # Utility functions
│   │   ├── types/                      # TypeScript types
│   │   ├── styles/                     # CSS/SCSS files
│   │   ├── App.tsx                     # Main App component
│   │   └── index.tsx                   # Entry point
│   ├── package.json                    # NPM dependencies
│   ├── tsconfig.json                   # TypeScript config
│   ├── webpack.config.js               # Webpack configuration
│   └── BUILD.bazel                     # Bazel build file
├── backend/                            # Node.js backend
│   ├── src/
│   │   ├── controllers/                # Express controllers
│   │   ├── middleware/                 # Express middleware
│   │   ├── routes/                     # API routes
│   │   ├── services/                   # Business services
│   │   ├── models/                     # Data models
│   │   ├── config/                     # Configuration
│   │   └── utils/                      # Utilities
│   ├── tests/                          # Test files
│   ├── package.json                    # NPM dependencies
│   ├── tsconfig.json                   # TypeScript config
│   └── BUILD.bazel                     # Bazel build file
├── docker/
│   ├── Dockerfile.frontend             # Frontend container
│   ├── Dockerfile.backend              # Backend container
│   └── docker-compose.yml             # Development setup
├── k8s/
│   ├── frontend-deployment.yaml        # Frontend deployment
│   ├── backend-deployment.yaml         # Backend deployment
│   ├── service.yaml                    # Service definition
│   └── ingress.yaml                    # Ingress configuration
└── nginx/
    ├── nginx.conf                      # Nginx configuration
    └── default.conf                    # Default site config
```

---

## Shared Libraries

### 10. Common Utilities Module

**Location**: `shared/common/`

**Purpose**: Shared utilities and common functionality across all services.

```
common/
├── go/                                 # Go shared libraries
│   ├── pkg/
│   │   ├── auth/                       # Authentication utilities
│   │   │   ├── jwt.go                  # JWT handling
│   │   │   ├── rbac.go                 # RBAC utilities
│   │   │   └── middleware.go           # Auth middleware
│   │   ├── config/                     # Configuration utilities
│   │   │   ├── loader.go               # Config loader
│   │   │   ├── validator.go            # Config validation
│   │   │   └── env.go                  # Environment handling
│   │   ├── database/                   # Database utilities
│   │   │   ├── connection.go           # Connection management
│   │   │   ├── migration.go            # Migration utilities
│   │   │   └── transaction.go          # Transaction helpers
│   │   ├── http/                       # HTTP utilities
│   │   │   ├── client.go               # HTTP client
│   │   │   ├── middleware.go           # HTTP middleware
│   │   │   └── response.go             # Response helpers
│   │   ├── logging/                    # Logging utilities
│   │   │   ├── logger.go               # Structured logger
│   │   │   ├── context.go              # Context logging
│   │   │   └── middleware.go           # Logging middleware
│   │   ├── metrics/                    # Metrics utilities
│   │   │   ├── prometheus.go           # Prometheus metrics
│   │   │   ├── collector.go            # Metrics collector
│   │   │   └── middleware.go           # Metrics middleware
│   │   ├── tracing/                    # Distributed tracing
│   │   │   ├── jaeger.go               # Jaeger integration
│   │   │   ├── span.go                 # Span utilities
│   │   │   └── middleware.go           # Tracing middleware
│   │   └── utils/                      # General utilities
│   │       ├── crypto.go               # Cryptographic functions
│   │       ├── validation.go           # Input validation
│   │       └── time.go                 # Time utilities
│   ├── go.mod                          # Go modules
│   └── BUILD.bazel                     # Bazel build file
├── java/                               # Java shared libraries
│   ├── src/
│   │   ├── main/
│   │   │   └── java/
│   │   │       └── io/platform/common/
│   │   │           ├── auth/           # Authentication utilities
│   │   │           ├── config/         # Configuration utilities
│   │   │           ├── database/       # Database utilities
│   │   │           ├── http/           # HTTP utilities
│   │   │           ├── logging/        # Logging utilities
│   │   │           ├── metrics/        # Metrics utilities
│   │   │           ├── tracing/        # Tracing utilities
│   │   │           └── utils/          # General utilities
│   │   └── test/                       # Test classes
│   ├── pom.xml                         # Maven configuration
│   └── BUILD.bazel                     # Bazel build file
└── python/                             # Python shared libraries
    ├── src/
    │   └── platform_common/
    │       ├── __init__.py
    │       ├── auth/                   # Authentication utilities
    │       │   ├── __init__.py
    │       │   ├── jwt.py              # JWT handling
    │       │   ├── rbac.py             # RBAC utilities
    │       │   └── middleware.py       # Auth middleware
    │       ├── config/                 # Configuration utilities
    │       │   ├── __init__.py
    │       │   ├── loader.py           # Config loader
    │       │   ├── validator.py        # Config validation
    │       │   └── env.py              # Environment handling
    │       ├── database/               # Database utilities
    │       │   ├── __init__.py
    │       │   ├── connection.py       # Connection management
    │       │   ├── migration.py        # Migration utilities
    │       │   └── transaction.py      # Transaction helpers
    │       ├── http/                   # HTTP utilities
    │       │   ├── __init__.py
    │       │   ├── client.py           # HTTP client
    │       │   ├── middleware.py       # HTTP middleware
    │       │   └── response.py         # Response helpers
    │       ├── logging/                # Logging utilities
    │       │   ├── __init__.py
    │       │   ├── logger.py           # Structured logger
    │       │   ├── context.py          # Context logging
    │       │   └── middleware.py       # Logging middleware
    │       ├── metrics/                # Metrics utilities
    │       │   ├── __init__.py
    │       │   ├── prometheus.py       # Prometheus metrics
    │       │   ├── collector.py        # Metrics collector
    │       │   └── middleware.py       # Metrics middleware
    │       ├── tracing/                # Distributed tracing
    │       │   ├── __init__.py
    │       │   ├── jaeger.py           # Jaeger integration
    │       │   ├── span.py             # Span utilities
    │       │   └── middleware.py       # Tracing middleware
    │       └── utils/                  # General utilities
    │           ├── __init__.py
    │           ├── crypto.py           # Cryptographic functions
    │           ├── validation.py       # Input validation
    │           └── time.py             # Time utilities
    ├── requirements.txt                # Python dependencies
    ├── setup.py                        # Package setup
    └── BUILD.bazel                     # Bazel build file
```

---

## Configuration Management

### 11. Configuration Structure

**Location**: `configs/`

**Purpose**: Centralized configuration management for all environments.

```
configs/
├── environments/                       # Environment-specific configurations
│   ├── development/
│   │   ├── platform.yaml               # Platform configuration
│   │   ├── agents.yaml                 # Agent configurations
│   │   ├── security.yaml               # Security settings
│   │   ├── monitoring.yaml             # Monitoring configuration
│   │   └── database.yaml               # Database settings
│   ├── staging/
│   │   ├── platform.yaml
│   │   ├── agents.yaml
│   │   ├── security.yaml
│   │   ├── monitoring.yaml
│   │   └── database.yaml
│   └── production/
│       ├── platform.yaml
│       ├── agents.yaml
│       ├── security.yaml
│       ├── monitoring.yaml
│       └── database.yaml
├── templates/                          # Configuration templates
│   ├── agent-template.yaml             # Agent configuration template
│   ├── workflow-template.yaml          # Workflow configuration template
│   └── deployment-template.yaml        # Deployment template
├── schemas/                            # Configuration schemas
│   ├── platform-schema.json            # Platform config schema
│   ├── agent-schema.json               # Agent config schema
│   └── workflow-schema.json            # Workflow config schema
├── security/                           # Security configurations
│   ├── rbac/                           # RBAC policies
│   │   ├── roles.yaml                  # Role definitions
│   │   ├── policies.yaml               # Policy definitions
│   │   └── bindings.yaml               # Role bindings
│   ├── network-policies/               # Network policies
│   │   ├── default-deny.yaml           # Default deny policy
│   │   ├── api-gateway.yaml            # API gateway policy
│   │   └── inter-service.yaml          # Inter-service policy
│   └── certificates/                   # Certificate configurations
│       ├── ca-config.yaml              # CA configuration
│       ├── cert-manager.yaml           # Cert-manager config
│       └── tls-config.yaml             # TLS configuration
└── monitoring/                         # Monitoring configurations
    ├── prometheus/
    │   ├── prometheus.yaml              # Prometheus configuration
    │   ├── rules/                       # Alert rules
    │   │   ├── platform-rules.yaml     # Platform alerts
    │   │   ├── agent-rules.yaml        # Agent alerts
    │   │   └── infrastructure-rules.yaml # Infrastructure alerts
    │   └── targets/                     # Scrape targets
    │       ├── platform-targets.yaml   # Platform targets
    │       └── agent-targets.yaml      # Agent targets
    ├── grafana/
    │   ├── grafana.yaml                 # Grafana configuration
    │   ├── datasources/                 # Data source configs
    │   │   ├── prometheus.yaml          # Prometheus datasource
    │   │   └── elasticsearch.yaml       # Elasticsearch datasource
    │   └── dashboards/                  # Dashboard definitions
    │       ├── platform-overview.json  # Platform overview
    │       ├── agent-performance.json  # Agent performance
    │       └── system-health.json      # System health
    └── jaeger/
        ├── jaeger.yaml                  # Jaeger configuration
        └── sampling.yaml                # Sampling configuration
```

---

## Build and Deployment

### 12. Build System Structure

**Location**: Root level build files and `scripts/`

**Purpose**: Build automation and deployment scripts.

```
# Root level files
BUILD.bazel                             # Root Bazel build file
WORKSPACE                               # Bazel workspace configuration
Makefile                                # Make automation
docker-compose.yml                      # Local development setup

scripts/
├── build/                              # Build scripts
│   ├── build-all.sh                    # Build all components
│   ├── build-agents.sh                 # Build agent templates
│   ├── build-platform.sh               # Build platform services
│   ├── build-docker.sh                 # Build Docker images
│   └── clean.sh                        # Clean build artifacts
├── deployment/                         # Deployment scripts
│   ├── deploy-local.sh                 # Local deployment
│   ├── deploy-k8s.sh                   # Kubernetes deployment
│   ├── deploy-helm.sh                  # Helm deployment
│   ├── rollback.sh                     # Rollback deployment
│   └── health-check.sh                 # Health check script
├── testing/                            # Testing scripts
│   ├── run-unit-tests.sh               # Run unit tests
│   ├── run-integration-tests.sh        # Run integration tests
│   ├── run-e2e-tests.sh                # Run end-to-end tests
│   └── run-performance-tests.sh        # Run performance tests
├── maintenance/                        # Maintenance scripts
│   ├── backup-database.sh              # Database backup
│   ├── cleanup-logs.sh                 # Log cleanup
│   ├── update-certificates.sh          # Certificate update
│   └── scale-agents.sh                 # Agent scaling
└── development/                        # Development utilities
    ├── setup-dev-env.sh                # Development environment setup
    ├── generate-certs.sh               # Generate certificates
    ├── seed-database.sh                # Database seeding
    └── port-forward.sh                 # Port forwarding setup
```

---

## Module Dependencies

### 13. Dependency Relationships

The platform follows a layered architecture with clear dependency relationships:

**Layer 1: Infrastructure and Shared Libraries**
- `shared/common/` - No dependencies
- `infrastructure/` - No platform dependencies

**Layer 2: Core Platform Services**
- `platform/api-gateway/` - Depends on: `shared/common/`
- `platform/auth-service/` - Depends on: `shared/common/`
- `platform/dashboard/` - Depends on: `platform/api-gateway/`

**Layer 3: Meta-Agents**
- `meta-agents/agent-factory/` - Depends on: `platform/`, `shared/common/`
- `meta-agents/task-orchestrator/` - Depends on: `platform/`, `shared/common/`
- `meta-agents/resource-manager/` - Depends on: `platform/`, `shared/common/`

**Layer 4: Agent Runtime and Templates**
- `agents/runtime/` - Depends on: `meta-agents/`, `platform/`, `shared/common/`
- `agents/templates/` - Depends on: `agents/runtime/`, `shared/common/`

**Build Dependencies**:
```mermaid
graph TD
    A[shared/common] --> B[platform/auth-service]
    A --> C[platform/api-gateway]
    A --> D[platform/dashboard]
    
    B --> E[meta-agents/agent-factory]
    C --> E
    B --> F[meta-agents/task-orchestrator]
    C --> F
    B --> G[meta-agents/resource-manager]
    C --> G
    
    E --> H[agents/runtime]
    F --> H
    G --> H
    
    H --> I[agents/templates/python]
    H --> J[agents/templates/java]
    H --> K[agents/templates/go]
```

---

## Development Guidelines

### 14. Module Development Standards

**Naming Conventions**:
- Module names: `kebab-case` (e.g., `agent-factory`, `task-orchestrator`)
- Package names: Language-specific conventions
- File names: Language-specific conventions

**Directory Structure**:
- Each module must have a `BUILD.bazel` file
- Include `README.md` with module documentation
- Separate source code, tests, and configuration
- Include Docker and Kubernetes manifests

**Code Organization**:
- Follow language-specific best practices
- Implement consistent error handling
- Include comprehensive logging
- Add metrics and health checks

**Testing Requirements**:
- Unit tests with >80% coverage
- Integration tests for external dependencies
- End-to-end tests for critical paths
- Performance tests for resource-intensive modules

**Documentation Standards**:
- API documentation for all public interfaces
- Code comments for complex logic
- Architecture decision records (ADRs)
- Deployment and operational guides

This modular structure ensures maintainability, scalability, and clear separation of concerns across the entire AI-Native Agent Platform.