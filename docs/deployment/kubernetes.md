# Kubernetes Deployment Guide

## Overview

This guide covers deploying the AI-Native Agent Platform on Kubernetes. The platform is designed to be cloud-native and scales efficiently on Kubernetes clusters, providing high availability, automatic scaling, and self-healing capabilities.

## Prerequisites

Before deploying, ensure you have:

- **Kubernetes cluster** (version 1.24+)
- **kubectl** configured to access your cluster
- **Helm** (version 3.8+)
- **Sufficient resources**: Minimum 16 CPU cores, 64GB RAM, 500GB storage
- **Network policies** support (for security)
- **Ingress controller** (NGINX, Istio, or similar)
- **Persistent storage** (for databases and file storage)

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Kubernetes Cluster                        │
├─────────────────────────────────────────────────────────────┤
│                      Ingress Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web UI    │  │  API Gateway │  │  Workflows  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Meta Agents  │  │   Agents    │  │ AI Models   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │    Redis    │  │ Object Store│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                   Infrastructure                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Monitoring  │  │   Logging   │  │ Service Mesh│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start Deployment

### 1. Add Helm Repository

```bash
helm repo add ai-platform https://charts.platform.io
helm repo update
```

### 2. Create Namespace

```bash
kubectl create namespace ai-platform
kubectl label namespace ai-platform name=ai-platform
```

### 3. Install with Default Configuration

```bash
helm install ai-platform ai-platform/platform \
  --namespace ai-platform \
  --create-namespace \
  --wait
```

### 4. Verify Installation

```bash
# Check pod status
kubectl get pods -n ai-platform

# Check services
kubectl get svc -n ai-platform

# Check ingress
kubectl get ingress -n ai-platform
```

## Production Deployment

### 1. Prepare Configuration

Create a values file for production deployment:

```yaml
# values-production.yaml
global:
  environment: production
  storageClass: fast-ssd
  imageRegistry: registry.platform.io
  imagePullPolicy: IfNotPresent
  
# High availability configuration
replicaCount:
  apiGateway: 3
  webUI: 2
  metaAgents: 3
  
# Resource specifications
resources:
  apiGateway:
    requests:
      cpu: 2
      memory: 4Gi
    limits:
      cpu: 4
      memory: 8Gi
      
  metaAgents:
    requests:
      cpu: 4
      memory: 8Gi
    limits:
      cpu: 8
      memory: 16Gi
      
  agents:
    requests:
      cpu: 2
      memory: 4Gi
    limits:
      cpu: 8
      memory: 16Gi

# Auto-scaling configuration
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Database configuration
postgresql:
  enabled: true
  architecture: replication
  auth:
    postgresPassword: "secure-password"
    database: platform_db
  primary:
    resources:
      requests:
        cpu: 4
        memory: 8Gi
      limits:
        cpu: 8
        memory: 16Gi
    persistence:
      enabled: true
      size: 500Gi
      storageClass: fast-ssd
  readReplicas:
    replicaCount: 2
    resources:
      requests:
        cpu: 2
        memory: 4Gi
      limits:
        cpu: 4
        memory: 8Gi

# Redis configuration
redis:
  enabled: true
  architecture: replication
  auth:
    enabled: true
    password: "redis-password"
  master:
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 2
        memory: 4Gi
    persistence:
      enabled: true
      size: 100Gi
  replica:
    replicaCount: 3
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 1
        memory: 2Gi

# Storage configuration
storage:
  objectStore:
    type: s3
    bucket: ai-platform-storage
    region: us-west-2
    accessKeyId: "ACCESS_KEY_ID"
    secretAccessKey: "SECRET_ACCESS_KEY"
    
  persistentVolumes:
    storageClass: fast-ssd
    fileStorage:
      size: 1Ti
    cacheStorage:
      size: 500Gi

# Networking
networking:
  ingress:
    enabled: true
    className: nginx
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-prod
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    hosts:
      - host: platform.company.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: platform-tls
        hosts:
          - platform.company.com

# Security configuration
security:
  networkPolicies:
    enabled: true
  podSecurityPolicy:
    enabled: true
  rbac:
    enabled: true
  encryption:
    enabled: true
    
# Monitoring and observability
monitoring:
  prometheus:
    enabled: true
    retention: 30d
    storage: 500Gi
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
  jaeger:
    enabled: true
    storage: elasticsearch
  
# AI Model providers
aiModels:
  providers:
    openai:
      enabled: true
      apiKey: "openai-api-key"
    anthropic:
      enabled: true
      apiKey: "anthropic-api-key"
    google:
      enabled: true
      serviceAccount: "google-service-account.json"
```

### 2. Deploy Production Environment

```bash
# Create secrets first
kubectl create secret generic ai-model-keys \
  --from-literal=openai-api-key="your-openai-key" \
  --from-literal=anthropic-api-key="your-anthropic-key" \
  --namespace ai-platform

kubectl create secret generic database-credentials \
  --from-literal=postgres-password="secure-password" \
  --from-literal=redis-password="redis-password" \
  --namespace ai-platform

# Deploy with production values
helm install ai-platform ai-platform/platform \
  --namespace ai-platform \
  --values values-production.yaml \
  --wait \
  --timeout 20m
```

### 3. Verify Production Deployment

```bash
# Check all components are running
kubectl get all -n ai-platform

# Check persistent volumes
kubectl get pv,pvc -n ai-platform

# Check ingress and certificates
kubectl get ingress -n ai-platform
kubectl get certificate -n ai-platform

# Test external access
curl https://platform.company.com/health
```

## Component-Specific Deployments

### 1. Meta-Agent Services

```yaml
# meta-agents-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-factory
  namespace: ai-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-factory
  template:
    metadata:
      labels:
        app: agent-factory
        version: v1.0.0
    spec:
      serviceAccountName: agent-factory-sa
      containers:
      - name: agent-factory
        image: registry.platform.io/meta-agents/agent-factory:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: postgres-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: redis-url
        resources:
          requests:
            cpu: 2
            memory: 4Gi
          limits:
            cpu: 4
            memory: 8Gi
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: temp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: agent-factory-config
      - name: temp
        emptyDir:
          sizeLimit: 10Gi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - agent-factory
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: agent-factory-service
  namespace: ai-platform
spec:
  selector:
    app: agent-factory
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-factory-hpa
  namespace: ai-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-factory
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 2. Agent Runtime Environment

```yaml
# agent-runtime-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-runtime
  namespace: ai-platform
spec:
  replicas: 5
  selector:
    matchLabels:
      app: agent-runtime
  template:
    metadata:
      labels:
        app: agent-runtime
        version: v1.0.0
    spec:
      serviceAccountName: agent-runtime-sa
      containers:
      - name: agent-runtime
        image: registry.platform.io/agents/runtime:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8443
          name: agent-comm
        env:
        - name: AGENT_POOL_SIZE
          value: "10"
        - name: MAX_CONCURRENT_TASKS
          value: "100"
        - name: COMMUNICATION_BROKER_URL
          value: "https://communication-broker.ai-platform.svc.cluster.local"
        resources:
          requests:
            cpu: 2
            memory: 4Gi
          limits:
            cpu: 8
            memory: 16Gi
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        volumeMounts:
        - name: agent-storage
          mountPath: /app/storage
        - name: agent-cache
          mountPath: /app/cache
        - name: agent-logs
          mountPath: /app/logs
      volumes:
      - name: agent-storage
        persistentVolumeClaim:
          claimName: agent-storage-pvc
      - name: agent-cache
        emptyDir:
          sizeLimit: 50Gi
      - name: agent-logs
        emptyDir:
          sizeLimit: 10Gi
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: workload-type
                operator: In
                values:
                - compute-intensive
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - agent-runtime
              topologyKey: kubernetes.io/hostname
```

### 3. Database Deployment

```yaml
# postgresql-deployment.yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgresql-cluster
  namespace: ai-platform
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "1000"
      shared_buffers: "8GB"
      effective_cache_size: "24GB"
      work_mem: "64MB"
      maintenance_work_mem: "2GB"
      wal_buffers: "16MB"
      checkpoint_completion_target: "0.9"
      random_page_cost: "1.1"
      
  bootstrap:
    initdb:
      database: platform_db
      owner: platform_user
      secret:
        name: postgresql-credentials
        
  storage:
    size: 500Gi
    storageClass: fast-ssd
    
  monitoring:
    enabled: true
    
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://platform-backups/postgresql"
      s3Credentials:
        accessKeyId:
          name: s3-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: s3-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "5d"
      data:
        retention: "30d"
        
  resources:
    requests:
      memory: "8Gi"
      cpu: "4"
    limits:
      memory: "16Gi"
      cpu: "8"
      
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: storage-type
            operator: In
            values:
            - ssd
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            postgresql: postgresql-cluster
        topologyKey: kubernetes.io/hostname
```

## Networking and Security

### 1. Network Policies

```yaml
# network-policies.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
  namespace: ai-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-ingress-to-api-gateway
  namespace: ai-platform
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-internal-communication
  namespace: ai-platform
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-platform
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: ai-platform
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

### 2. RBAC Configuration

```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-factory-sa
  namespace: ai-platform
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: agent-factory-role
  namespace: ai-platform
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: agent-factory-rolebinding
  namespace: ai-platform
subjects:
- kind: ServiceAccount
  name: agent-factory-sa
  namespace: ai-platform
roleRef:
  kind: Role
  name: agent-factory-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-runtime-sa
  namespace: ai-platform
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: agent-runtime-role
  namespace: ai-platform
rules:
- apiGroups: [""]
  resources: ["pods", "configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: agent-runtime-rolebinding
  namespace: ai-platform
subjects:
- kind: ServiceAccount
  name: agent-runtime-sa
  namespace: ai-platform
roleRef:
  kind: Role
  name: agent-runtime-role
  apiGroup: rbac.authorization.k8s.io
```

### 3. Pod Security Standards

```yaml
# pod-security-policy.yaml
apiVersion: v1
kind: Pod
metadata:
  name: example-pod
  namespace: ai-platform
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 2000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: container
    image: example-image
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
        add:
        - NET_BIND_SERVICE
    resources:
      limits:
        cpu: "2"
        memory: "4Gi"
      requests:
        cpu: "1"
        memory: "2Gi"
```

## Storage Configuration

### 1. Persistent Volume Claims

```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: agent-storage-pvc
  namespace: ai-platform
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Ti
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: model-cache-pvc
  namespace: ai-platform
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: workflow-data-pvc
  namespace: ai-platform
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 200Gi
  storageClassName: standard
```

### 2. Storage Classes

```yaml
# storage-classes.yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
  kmsKeyId: "arn:aws:kms:region:account:key/key-id"
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: nvme-ssd
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  iops: "16000"
  throughput: "1000"
  encrypted: "true"
reclaimPolicy: Delete
volumeBindingMode: Immediate
allowVolumeExpansion: true
```

## Monitoring and Observability

### 1. Prometheus Configuration

```yaml
# prometheus.yaml
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: platform-prometheus
  namespace: ai-platform
spec:
  serviceAccountName: prometheus
  serviceMonitorSelector:
    matchLabels:
      team: platform
  ruleSelector:
    matchLabels:
      team: platform
  resources:
    requests:
      memory: 4Gi
      cpu: 2
    limits:
      memory: 8Gi
      cpu: 4
  retention: 30d
  storage:
    volumeClaimTemplate:
      spec:
        storageClassName: fast-ssd
        resources:
          requests:
            storage: 500Gi
  alerting:
    alertmanagers:
    - namespace: ai-platform
      name: alertmanager-main
      port: web
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: platform-services
  namespace: ai-platform
  labels:
    team: platform
spec:
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### 2. Grafana Dashboard

```yaml
# grafana.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: ai-platform
data:
  platform-overview.json: |
    {
      "dashboard": {
        "title": "AI Platform Overview",
        "panels": [
          {
            "title": "Agent Status",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(agent_status{status=\"active\"}) by (agent_type)",
                "legendFormat": "{{ agent_type }}"
              }
            ]
          },
          {
            "title": "Workflow Executions",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(workflow_executions_total[5m])",
                "legendFormat": "Executions/sec"
              }
            ]
          },
          {
            "title": "Resource Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "avg(container_cpu_usage_seconds_total) by (pod)",
                "legendFormat": "CPU - {{ pod }}"
              },
              {
                "expr": "avg(container_memory_usage_bytes) by (pod)",
                "legendFormat": "Memory - {{ pod }}"
              }
            ]
          }
        ]
      }
    }
```

## Backup and Disaster Recovery

### 1. Database Backup

```yaml
# backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgresql-backup
  namespace: ai-platform
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              pg_dump -h postgresql-cluster-rw \
                      -U platform_user \
                      -d platform_db \
                      --verbose \
                      --no-password \
                      --format=custom \
                      --file=/backup/platform_db_$(date +%Y%m%d_%H%M%S).dump
              
              # Upload to S3
              aws s3 cp /backup/platform_db_$(date +%Y%m%d_%H%M%S).dump \
                        s3://platform-backups/postgresql/
              
              # Cleanup local files older than 7 days
              find /backup -name "platform_db_*.dump" -mtime +7 -delete
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-credentials
                  key: password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: s3-credentials
                  key: ACCESS_KEY_ID
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: s3-credentials
                  key: SECRET_ACCESS_KEY
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure
```

### 2. Application State Backup

```yaml
# app-backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: application-backup
  namespace: ai-platform
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-sa
          containers:
          - name: app-backup
            image: registry.platform.io/tools/backup:v1.0.0
            command:
            - /bin/bash
            - -c
            - |
              # Backup Kubernetes resources
              kubectl get all -n ai-platform -o yaml > /backup/k8s-resources.yaml
              kubectl get configmaps -n ai-platform -o yaml > /backup/configmaps.yaml
              kubectl get secrets -n ai-platform -o yaml > /backup/secrets.yaml
              
              # Backup agent configurations
              kubectl get agents.platform.io -n ai-platform -o yaml > /backup/agents.yaml
              kubectl get workflows.platform.io -n ai-platform -o yaml > /backup/workflows.yaml
              
              # Create archive
              tar -czf /backup/platform-backup-$(date +%Y%m%d).tar.gz \
                       /backup/*.yaml
              
              # Upload to S3
              aws s3 cp /backup/platform-backup-$(date +%Y%m%d).tar.gz \
                        s3://platform-backups/application/
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure
```

## Scaling Configuration

### 1. Horizontal Pod Autoscaler

```yaml
# hpa-configurations.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: ai-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: request_rate
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
```

### 2. Vertical Pod Autoscaler

```yaml
# vpa-configurations.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: agent-factory-vpa
  namespace: ai-platform
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-factory
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: agent-factory
      minAllowed:
        cpu: 1
        memory: 2Gi
      maxAllowed:
        cpu: 8
        memory: 16Gi
      controlledResources: ["cpu", "memory"]
```

### 3. Cluster Autoscaler

```yaml
# cluster-autoscaler.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cluster-autoscaler
  template:
    metadata:
      labels:
        app: cluster-autoscaler
    spec:
      serviceAccountName: cluster-autoscaler
      containers:
      - image: k8s.gcr.io/autoscaling/cluster-autoscaler:v1.27.0
        name: cluster-autoscaler
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 300Mi
        command:
        - ./cluster-autoscaler
        - --v=4
        - --stderrthreshold=info
        - --cloud-provider=aws
        - --skip-nodes-with-local-storage=false
        - --expander=least-waste
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/ai-platform
        - --balance-similar-node-groups
        - --skip-nodes-with-system-pods=false
```

## Maintenance and Updates

### 1. Rolling Updates

```yaml
# rolling-update-strategy.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: example-service
  namespace: ai-platform
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        version: v1.1.0
    spec:
      containers:
      - name: service
        image: registry.platform.io/service:v1.1.0
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
```

### 2. Blue-Green Deployments

```bash
#!/bin/bash
# blue-green-deployment.sh

# Deploy green version
kubectl apply -f green-deployment.yaml

# Wait for green deployment to be ready
kubectl rollout status deployment/service-green -n ai-platform

# Run health checks on green deployment
if curl -f http://service-green.ai-platform.svc.cluster.local/health; then
    echo "Green deployment healthy, switching traffic..."
    
    # Switch service to green
    kubectl patch service service-main -n ai-platform \
        -p '{"spec":{"selector":{"version":"green"}}}'
    
    # Wait and verify
    sleep 30
    if curl -f http://service-main.ai-platform.svc.cluster.local/health; then
        echo "Traffic switch successful, removing blue deployment"
        kubectl delete deployment service-blue -n ai-platform
    else
        echo "Traffic switch failed, rolling back"
        kubectl patch service service-main -n ai-platform \
            -p '{"spec":{"selector":{"version":"blue"}}}'
    fi
else
    echo "Green deployment unhealthy, keeping blue"
    kubectl delete deployment service-green -n ai-platform
fi
```

### 3. Canary Deployments

```yaml
# canary-deployment.yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: service-rollout
  namespace: ai-platform
spec:
  replicas: 10
  strategy:
    canary:
      steps:
      - setWeight: 10
      - pause: {duration: 5m}
      - setWeight: 20
      - pause: {duration: 5m}
      - setWeight: 50
      - pause: {duration: 10m}
      - setWeight: 100
      canaryService: service-canary
      stableService: service-stable
      trafficRouting:
        nginx:
          stableIngress: service-stable-ingress
          annotationPrefix: nginx.ingress.kubernetes.io
      analysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: service
        startingStep: 2
        interval: 5m
  selector:
    matchLabels:
      app: service
  template:
    metadata:
      labels:
        app: service
    spec:
      containers:
      - name: service
        image: registry.platform.io/service:v1.1.0
```

## Troubleshooting

### 1. Common Issues

#### Pod Startup Failures
```bash
# Check pod events
kubectl describe pod pod-name -n ai-platform

# Check logs
kubectl logs pod-name -n ai-platform -f

# Check resource constraints
kubectl top pods -n ai-platform
kubectl top nodes
```

#### Service Discovery Issues
```bash
# Test DNS resolution
kubectl run test-pod --image=busybox --rm -it -- nslookup service-name.ai-platform.svc.cluster.local

# Check endpoints
kubectl get endpoints -n ai-platform

# Check service configuration
kubectl describe service service-name -n ai-platform
```

#### Storage Issues
```bash
# Check PVC status
kubectl get pvc -n ai-platform

# Check PV status
kubectl get pv

# Check storage class
kubectl describe storageclass fast-ssd
```

### 2. Performance Debugging

```bash
# Check resource utilization
kubectl top pods -n ai-platform --sort-by=cpu
kubectl top pods -n ai-platform --sort-by=memory

# Check node resource availability
kubectl describe nodes

# Check network policies
kubectl get networkpolicy -n ai-platform
kubectl describe networkpolicy policy-name -n ai-platform
```

### 3. Monitoring Commands

```bash
# Check all resources
kubectl get all -n ai-platform

# Check custom resources
kubectl get agents.platform.io -n ai-platform
kubectl get workflows.platform.io -n ai-platform

# Check events
kubectl get events -n ai-platform --sort-by='.lastTimestamp'

# Port forward for debugging
kubectl port-forward svc/api-gateway 8080:80 -n ai-platform
```

## Best Practices

### 1. Resource Management
- Use resource requests and limits for all containers
- Implement proper health checks
- Configure appropriate anti-affinity rules
- Use PodDisruptionBudgets for critical services

### 2. Security
- Enable RBAC and use least privilege principle
- Implement network policies
- Use pod security standards
- Encrypt data at rest and in transit

### 3. Monitoring
- Monitor all critical metrics
- Set up proper alerting
- Use distributed tracing
- Implement centralized logging

### 4. High Availability
- Deploy across multiple availability zones
- Use redundant data stores
- Implement proper backup strategies
- Test disaster recovery procedures

## Conclusion

This Kubernetes deployment guide provides a comprehensive foundation for running the AI-Native Agent Platform in production. The configuration examples can be adapted to your specific requirements and infrastructure setup.

Key considerations for successful deployment:
- Start with the basic setup and gradually add complexity
- Monitor resource usage and scale accordingly
- Implement proper backup and disaster recovery procedures
- Follow security best practices from day one
- Plan for future growth and scaling requirements

For additional support, refer to:
- [Monitoring Guide](monitoring.md)
- [Security Documentation](../architecture/security-model.md)
- [Troubleshooting Guide](../tutorials/troubleshooting.md)