# Monitoring and Observability

## Overview

The AI-Native Agent Platform includes comprehensive monitoring and observability features that provide deep insights into system performance, agent behavior, and workflow execution. This guide covers setting up and using the monitoring stack to ensure optimal platform operation.

## Monitoring Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Data Collection Layer                     │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │  Metrics    │ │    Logs     │ │   Traces    │ │ Events  │ │
│ │(Prometheus) │ │ (Fluentd)   │ │  (Jaeger)   │ │(K8s API)│ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     Storage Layer                            │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ Prometheus  │ │Elasticsearch│ │ Jaeger DB   │ │ InfluxDB│ │
│ │  TSDB       │ │   Cluster   │ │             │ │         │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  Visualization Layer                         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │  Grafana    │ │   Kibana    │ │ Jaeger UI   │ │Platform │ │
│ │ Dashboards  │ │   Logs      │ │   Traces    │ │Dashboard│ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     Alerting Layer                           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │ AlertManager│ │   PagerDuty │ │    Slack    │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## Metrics Collection

### 1. Prometheus Configuration

```yaml
# prometheus.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "platform_rules.yml"
  - "agent_rules.yml"
  - "workflow_rules.yml"

scrape_configs:
  # Platform services
  - job_name: 'platform-services'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - ai-platform
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__

  # Agent metrics
  - job_name: 'agents'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - ai-platform
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_component]
      action: keep
      regex: agent
    - source_labels: [__meta_kubernetes_pod_name]
      target_label: agent_name
    - source_labels: [__meta_kubernetes_pod_label_agent_type]
      target_label: agent_type

  # Node exporter
  - job_name: 'node-exporter'
    kubernetes_sd_configs:
    - role: node
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)

  # kube-state-metrics
  - job_name: 'kube-state-metrics'
    static_configs:
    - targets: ['kube-state-metrics:8080']

alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093
```

### 2. Custom Metrics for Agents

```python
# agent_metrics.py
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, start_http_server
import time
import threading

class AgentMetrics:
    def __init__(self, agent_id: str, port: int = 9090):
        self.agent_id = agent_id
        self.registry = CollectorRegistry()
        
        # Define metrics
        self.tasks_total = Counter(
            'agent_tasks_total',
            'Total number of tasks processed',
            ['agent_id', 'task_type', 'status'],
            registry=self.registry
        )
        
        self.task_duration = Histogram(
            'agent_task_duration_seconds',
            'Task processing duration in seconds',
            ['agent_id', 'task_type'],
            buckets=[0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300],
            registry=self.registry
        )
        
        self.active_tasks = Gauge(
            'agent_active_tasks',
            'Number of currently active tasks',
            ['agent_id'],
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'agent_memory_usage_bytes',
            'Agent memory usage in bytes',
            ['agent_id'],
            registry=self.registry
        )
        
        self.ai_model_requests = Counter(
            'agent_ai_model_requests_total',
            'Total AI model requests',
            ['agent_id', 'model_provider', 'model_name', 'status'],
            registry=self.registry
        )
        
        self.ai_model_latency = Histogram(
            'agent_ai_model_latency_seconds',
            'AI model request latency',
            ['agent_id', 'model_provider', 'model_name'],
            buckets=[0.1, 0.5, 1, 2, 5, 10, 20, 30],
            registry=self.registry
        )
        
        # Start metrics server
        start_http_server(port, registry=self.registry)
        
        # Start resource monitoring
        self._start_resource_monitoring()
    
    def record_task_start(self, task_type: str):
        """Record task start"""
        self.active_tasks.labels(agent_id=self.agent_id).inc()
        return time.time()
    
    def record_task_completion(self, task_type: str, start_time: float, status: str):
        """Record task completion"""
        duration = time.time() - start_time
        
        self.tasks_total.labels(
            agent_id=self.agent_id,
            task_type=task_type,
            status=status
        ).inc()
        
        self.task_duration.labels(
            agent_id=self.agent_id,
            task_type=task_type
        ).observe(duration)
        
        self.active_tasks.labels(agent_id=self.agent_id).dec()
    
    def record_ai_model_request(self, provider: str, model: str, latency: float, status: str):
        """Record AI model request"""
        self.ai_model_requests.labels(
            agent_id=self.agent_id,
            model_provider=provider,
            model_name=model,
            status=status
        ).inc()
        
        if status == 'success':
            self.ai_model_latency.labels(
                agent_id=self.agent_id,
                model_provider=provider,
                model_name=model
            ).observe(latency)
    
    def _start_resource_monitoring(self):
        """Start background resource monitoring"""
        def monitor_resources():
            import psutil
            while True:
                try:
                    # Update memory usage
                    memory_info = psutil.Process().memory_info()
                    self.memory_usage.labels(agent_id=self.agent_id).set(memory_info.rss)
                    
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    print(f"Error monitoring resources: {e}")
                    time.sleep(30)
        
        thread = threading.Thread(target=monitor_resources, daemon=True)
        thread.start()

# Usage in agent
class MonitoredAgent(AgentBase):
    def __init__(self, config):
        super().__init__(config)
        self.metrics = AgentMetrics(self.agent_id)
    
    async def process_task(self, task):
        task_type = task.get('type', 'unknown')
        start_time = self.metrics.record_task_start(task_type)
        
        try:
            result = await self._process_task_internal(task)
            self.metrics.record_task_completion(task_type, start_time, 'success')
            return result
        except Exception as e:
            self.metrics.record_task_completion(task_type, start_time, 'error')
            raise
    
    async def call_ai_model(self, provider, model, prompt):
        start_time = time.time()
        
        try:
            result = await self.ai_client.complete(provider, model, prompt)
            latency = time.time() - start_time
            self.metrics.record_ai_model_request(provider, model, latency, 'success')
            return result
        except Exception as e:
            latency = time.time() - start_time
            self.metrics.record_ai_model_request(provider, model, latency, 'error')
            raise
```

### 3. Workflow Metrics

```python
# workflow_metrics.py
from prometheus_client import Counter, Histogram, Gauge, Enum

class WorkflowMetrics:
    def __init__(self):
        # Workflow execution metrics
        self.workflow_executions_total = Counter(
            'workflow_executions_total',
            'Total workflow executions',
            ['workflow_id', 'workflow_version', 'status']
        )
        
        self.workflow_duration = Histogram(
            'workflow_duration_seconds',
            'Workflow execution duration',
            ['workflow_id', 'workflow_version'],
            buckets=[1, 5, 10, 30, 60, 300, 600, 1800, 3600]
        )
        
        self.active_workflows = Gauge(
            'active_workflows',
            'Number of currently executing workflows',
            ['workflow_id']
        )
        
        # Node execution metrics
        self.node_executions_total = Counter(
            'workflow_node_executions_total',
            'Total node executions',
            ['workflow_id', 'node_id', 'node_type', 'status']
        )
        
        self.node_duration = Histogram(
            'workflow_node_duration_seconds',
            'Node execution duration',
            ['workflow_id', 'node_id', 'node_type'],
            buckets=[0.1, 0.5, 1, 5, 10, 30, 60, 300]
        )
        
        # Queue metrics
        self.workflow_queue_depth = Gauge(
            'workflow_queue_depth',
            'Number of workflows waiting in queue',
            ['priority']
        )
        
        self.workflow_queue_wait_time = Histogram(
            'workflow_queue_wait_time_seconds',
            'Time workflows spend in queue',
            buckets=[1, 5, 10, 30, 60, 300, 600]
        )
        
        # Resource utilization
        self.workflow_resource_usage = Gauge(
            'workflow_resource_usage',
            'Resource usage by workflows',
            ['resource_type', 'workflow_id']
        )

# Integration with workflow engine
class MonitoredWorkflowEngine:
    def __init__(self):
        self.metrics = WorkflowMetrics()
        
    async def execute_workflow(self, workflow_spec, input_data):
        workflow_id = workflow_spec['metadata']['name']
        workflow_version = workflow_spec['metadata']['version']
        
        start_time = time.time()
        self.metrics.active_workflows.labels(workflow_id=workflow_id).inc()
        
        try:
            result = await self._execute_workflow_internal(workflow_spec, input_data)
            
            duration = time.time() - start_time
            self.metrics.workflow_executions_total.labels(
                workflow_id=workflow_id,
                workflow_version=workflow_version,
                status='success'
            ).inc()
            
            self.metrics.workflow_duration.labels(
                workflow_id=workflow_id,
                workflow_version=workflow_version
            ).observe(duration)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.workflow_executions_total.labels(
                workflow_id=workflow_id,
                workflow_version=workflow_version,
                status='error'
            ).inc()
            
            self.metrics.workflow_duration.labels(
                workflow_id=workflow_id,
                workflow_version=workflow_version
            ).observe(duration)
            
            raise
        finally:
            self.metrics.active_workflows.labels(workflow_id=workflow_id).dec()
    
    async def execute_node(self, workflow_id, node_spec):
        node_id = node_spec['id']
        node_type = node_spec['type']
        
        start_time = time.time()
        
        try:
            result = await self._execute_node_internal(node_spec)
            
            duration = time.time() - start_time
            self.metrics.node_executions_total.labels(
                workflow_id=workflow_id,
                node_id=node_id,
                node_type=node_type,
                status='success'
            ).inc()
            
            self.metrics.node_duration.labels(
                workflow_id=workflow_id,
                node_id=node_id,
                node_type=node_type
            ).observe(duration)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.metrics.node_executions_total.labels(
                workflow_id=workflow_id,
                node_id=node_id,
                node_type=node_type,
                status='error'
            ).inc()
            
            self.metrics.node_duration.labels(
                workflow_id=workflow_id,
                node_id=node_id,
                node_type=node_type
            ).observe(duration)
            
            raise
```

## Logging

### 1. Structured Logging Configuration

```python
# logging_config.py
import structlog
import logging
import sys
from datetime import datetime

def configure_logging(service_name: str, log_level: str = "INFO"):
    """Configure structured logging for the platform"""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper())
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            add_service_context,
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Add service context
    structlog.contextvars.bind_contextvars(
        service=service_name,
        version=get_service_version(),
        environment=get_environment()
    )

def add_service_context(logger, method_name, event_dict):
    """Add service context to log entries"""
    event_dict["service"] = structlog.contextvars.get_contextvars().get("service")
    event_dict["version"] = structlog.contextvars.get_contextvars().get("version")
    event_dict["environment"] = structlog.contextvars.get_contextvars().get("environment")
    return event_dict

# Usage in agents
class LoggingAgent(AgentBase):
    def __init__(self, config):
        super().__init__(config)
        configure_logging(f"agent-{self.agent_id}")
        self.logger = structlog.get_logger(__name__)
    
    async def process_message(self, message):
        # Bind message context
        self.logger = self.logger.bind(
            message_id=message.get('id'),
            message_type=message.get('type'),
            agent_id=self.agent_id
        )
        
        self.logger.info("Processing message started")
        
        try:
            result = await self._process_message_internal(message)
            
            self.logger.info(
                "Message processed successfully",
                processing_time=time.time() - start_time,
                result_size=len(str(result))
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Message processing failed",
                error=str(e),
                error_type=type(e).__name__,
                processing_time=time.time() - start_time,
                exc_info=True
            )
            raise
```

### 2. Fluentd Configuration

```yaml
# fluentd-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: ai-platform
data:
  fluent.conf: |
    <source>
      @type tail
      @id in_tail_container_logs
      path /var/log/containers/*ai-platform*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>
    
    <filter kubernetes.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
      kubernetes_url "#{ENV['KUBERNETES_SERVICE_HOST']}:#{ENV['KUBERNETES_SERVICE_PORT_HTTPS']}"
      verify_ssl "#{ENV['KUBERNETES_VERIFY_SSL'] || true}"
      ca_file "#{ENV['KUBERNETES_CA_FILE']}"
      skip_labels false
      skip_container_metadata false
      skip_master_url false
      skip_namespace_metadata false
    </filter>
    
    <filter kubernetes.**>
      @type grep
      <regexp>
        key $.kubernetes.namespace_name
        pattern ^ai-platform$
      </regexp>
    </filter>
    
    <match kubernetes.**>
      @type elasticsearch
      @id out_es
      @log_level info
      include_tag_key true
      host "#{ENV['ELASTICSEARCH_HOST'] || 'elasticsearch'}"
      port "#{ENV['ELASTICSEARCH_PORT'] || '9200'}"
      scheme "#{ENV['ELASTICSEARCH_SCHEME'] || 'http'}"
      ssl_verify "#{ENV['ELASTICSEARCH_SSL_VERIFY'] || 'true'}"
      reload_connections false
      reconnect_on_error true
      reload_on_failure true
      log_es_400_reason false
      logstash_prefix logstash
      logstash_format true
      index_name platform-logs
      type_name _doc
      <buffer>
        flush_thread_count 8
        flush_interval 5s
        chunk_limit_size 2M
        queue_limit_length 32
        retry_max_interval 30
        retry_forever true
      </buffer>
    </match>
```

### 3. Log Aggregation Patterns

```yaml
# log-patterns.yaml
apiVersion: logging.coreos.com/v1
kind: ClusterLogForwarder
metadata:
  name: platform-logs
  namespace: openshift-logging
spec:
  outputs:
  - name: elasticsearch-platform
    type: elasticsearch
    url: http://elasticsearch.ai-platform.svc.cluster.local:9200
    elasticsearch:
      index: platform-logs-{+yyyy.MM.dd}
      
  - name: s3-archive
    type: cloudwatch
    cloudwatch:
      groupName: /ai-platform/logs
      region: us-west-2
      
  pipelines:
  - name: agent-logs
    inputRefs:
    - application
    filterRefs:
    - platform-filter
    outputRefs:
    - elasticsearch-platform
    - s3-archive
    
  filters:
  - name: platform-filter
    type: json
    json:
      javascript: |
        const log = record.log;
        if (log && log.kubernetes && log.kubernetes.namespace_name === 'ai-platform') {
          // Add additional metadata
          log.platform_version = '1.0.0';
          log.component_type = log.kubernetes.labels['component'] || 'unknown';
          log.agent_type = log.kubernetes.labels['agent-type'] || 'unknown';
          return log;
        }
        return null;
```

## Distributed Tracing

### 1. Jaeger Configuration

```yaml
# jaeger.yaml
apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: platform-jaeger
  namespace: ai-platform
spec:
  strategy: production
  storage:
    type: elasticsearch
    elasticsearch:
      server-urls: http://elasticsearch:9200
      index-prefix: jaeger
  collector:
    maxReplicas: 5
    resources:
      limits:
        cpu: 1
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  query:
    replicas: 3
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi
  agent:
    strategy: DaemonSet
    resources:
      limits:
        cpu: 200m
        memory: 128Mi
      requests:
        cpu: 100m
        memory: 64Mi
```

### 2. OpenTelemetry Integration

```python
# tracing.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.propagate import set_global_textmap

def configure_tracing(service_name: str, jaeger_endpoint: str):
    """Configure OpenTelemetry tracing"""
    
    # Set up tracer provider
    trace.set_tracer_provider(TracerProvider(
        resource=Resource.create({
            "service.name": service_name,
            "service.version": get_service_version(),
            "deployment.environment": get_environment()
        })
    ))
    
    # Configure Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name=jaeger_endpoint,
        agent_port=6831,
    )
    
    # Add span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Auto-instrument libraries
    RequestsInstrumentor().instrument()
    SQLAlchemyInstrumentor().instrument()
    
    return trace.get_tracer(__name__)

class TracedAgent(AgentBase):
    def __init__(self, config):
        super().__init__(config)
        self.tracer = configure_tracing(
            f"agent-{self.agent_id}",
            config.get('jaeger_endpoint', 'jaeger-agent')
        )
    
    async def process_message(self, message):
        with self.tracer.start_as_current_span("process_message") as span:
            # Add span attributes
            span.set_attribute("agent.id", self.agent_id)
            span.set_attribute("message.id", message.get('id'))
            span.set_attribute("message.type", message.get('type'))
            
            try:
                # Process message with child spans
                result = await self._process_with_tracing(message)
                
                span.set_attribute("result.status", "success")
                span.set_attribute("result.size", len(str(result)))
                
                return result
                
            except Exception as e:
                span.set_attribute("result.status", "error")
                span.set_attribute("error.type", type(e).__name__)
                span.set_attribute("error.message", str(e))
                span.record_exception(e)
                raise
    
    async def _process_with_tracing(self, message):
        # Extract data
        with self.tracer.start_as_current_span("extract_data") as span:
            data = await self._extract_data(message)
            span.set_attribute("data.size", len(str(data)))
        
        # Call AI model
        with self.tracer.start_as_current_span("ai_model_call") as span:
            span.set_attribute("ai.provider", "openai")
            span.set_attribute("ai.model", "gpt-4")
            
            ai_result = await self._call_ai_model(data)
            span.set_attribute("ai.tokens_used", ai_result.get('usage', {}).get('total_tokens', 0))
        
        # Store result
        with self.tracer.start_as_current_span("store_result") as span:
            result = await self._store_result(ai_result)
            span.set_attribute("storage.type", "database")
        
        return result
```

### 3. Trace Correlation

```python
# trace_correlation.py
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator

class TracedWorkflowEngine:
    def __init__(self):
        self.tracer = trace.get_tracer(__name__)
        self.propagator = TraceContextTextMapPropagator()
    
    async def execute_workflow(self, workflow_spec, input_data, trace_context=None):
        # Start workflow span
        with self.tracer.start_as_current_span("workflow_execution") as span:
            workflow_id = workflow_spec['metadata']['name']
            span.set_attribute("workflow.id", workflow_id)
            span.set_attribute("workflow.version", workflow_spec['metadata']['version'])
            
            # If trace context provided, link to parent trace
            if trace_context:
                parent_context = self.propagator.extract(trace_context)
                span.set_parent(parent_context)
            
            # Execute nodes with trace propagation
            for node in workflow_spec['nodes']:
                await self._execute_node_with_tracing(node, span.get_span_context())
    
    async def _execute_node_with_tracing(self, node_spec, parent_context):
        with self.tracer.start_as_current_span("node_execution", parent=parent_context) as span:
            span.set_attribute("node.id", node_spec['id'])
            span.set_attribute("node.type", node_spec['type'])
            
            if node_spec['type'] == 'agent_task':
                # Propagate trace to agent
                trace_headers = {}
                self.propagator.inject(trace_headers)
                
                result = await self._call_agent_with_trace(
                    node_spec['agent_type'],
                    node_spec['capability'],
                    trace_headers
                )
                
                return result
```

## Alerting

### 1. AlertManager Configuration

```yaml
# alertmanager.yaml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'smtp-password'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    routes:
    - match:
        component: agent
      receiver: 'agent-critical'
  - match:
      severity: warning
    receiver: 'warning-alerts'

receivers:
- name: 'default'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#platform-alerts'
    title: 'AI Platform Alert'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: 'CRITICAL: AI Platform Alert'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      Severity: {{ .Labels.severity }}
      Instance: {{ .Labels.instance }}
      {{ end }}
  pagerduty_configs:
  - service_key: 'your-pagerduty-service-key'
    description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'agent-critical'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#agent-alerts'
    title: 'Critical Agent Alert'
    text: |
      🚨 *Critical Agent Alert* 🚨
      Agent: {{ .Labels.agent_id }}
      Issue: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
  webhook_configs:
  - url: 'https://platform.company.com/api/alerts/webhook'
    send_resolved: true

- name: 'warning-alerts'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#platform-alerts'
    title: 'Platform Warning'
    text: '⚠️ {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'instance']
```

### 2. Prometheus Alert Rules

```yaml
# platform_rules.yml
groups:
- name: platform.rules
  rules:
  # High-level platform health
  - alert: PlatformDown
    expr: up{job="platform-services"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Platform service {{ $labels.instance }} is down"
      description: "Platform service {{ $labels.instance }} has been down for more than 1 minute"

  - alert: HighErrorRate
    expr: (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])) > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate on {{ $labels.instance }}"
      description: "Error rate is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High latency on {{ $labels.instance }}"
      description: "95th percentile latency is {{ $value }}s on {{ $labels.instance }}"

  # Resource utilization
  - alert: HighCPUUsage
    expr: (100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on {{ $labels.instance }}"
      description: "CPU usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

  - alert: HighMemoryUsage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on {{ $labels.instance }}"
      description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

  - alert: DiskSpaceLow
    expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Disk space low on {{ $labels.instance }}"
      description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

# agent_rules.yml
groups:
- name: agent.rules
  rules:
  # Agent health
  - alert: AgentDown
    expr: up{component="agent"} == 0
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Agent {{ $labels.agent_id }} is down"
      description: "Agent {{ $labels.agent_id }} of type {{ $labels.agent_type }} has been down for more than 2 minutes"

  - alert: AgentHighErrorRate
    expr: (rate(agent_tasks_total{status="error"}[5m]) / rate(agent_tasks_total[5m])) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate for agent {{ $labels.agent_id }}"
      description: "Error rate is {{ $value | humanizePercentage }} for agent {{ $labels.agent_id }}"

  - alert: AgentTaskBacklog
    expr: agent_active_tasks > 50
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High task backlog for agent {{ $labels.agent_id }}"
      description: "Agent {{ $labels.agent_id }} has {{ $value }} active tasks"

  - alert: AgentMemoryLeak
    expr: increase(agent_memory_usage_bytes[1h]) > 1073741824  # 1GB increase in 1 hour
    for: 30m
    labels:
      severity: warning
    annotations:
      summary: "Possible memory leak in agent {{ $labels.agent_id }}"
      description: "Memory usage increased by {{ $value | humanizeBytes }} in the last hour"

  # AI model issues
  - alert: AIModelHighLatency
    expr: histogram_quantile(0.95, rate(agent_ai_model_latency_seconds_bucket[5m])) > 10
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High AI model latency for {{ $labels.model_provider }}/{{ $labels.model_name }}"
      description: "95th percentile latency is {{ $value }}s for {{ $labels.model_provider }}/{{ $labels.model_name }}"

  - alert: AIModelHighErrorRate
    expr: (rate(agent_ai_model_requests_total{status="error"}[5m]) / rate(agent_ai_model_requests_total[5m])) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate for AI model {{ $labels.model_provider }}/{{ $labels.model_name }}"
      description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.model_provider }}/{{ $labels.model_name }}"

# workflow_rules.yml
groups:
- name: workflow.rules
  rules:
  # Workflow execution issues
  - alert: WorkflowHighFailureRate
    expr: (rate(workflow_executions_total{status="error"}[10m]) / rate(workflow_executions_total[10m])) > 0.1
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High failure rate for workflow {{ $labels.workflow_id }}"
      description: "Failure rate is {{ $value | humanizePercentage }} for workflow {{ $labels.workflow_id }}"

  - alert: WorkflowLongExecution
    expr: histogram_quantile(0.95, rate(workflow_duration_seconds_bucket[10m])) > 3600  # 1 hour
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "Long execution time for workflow {{ $labels.workflow_id }}"
      description: "95th percentile execution time is {{ $value | humanizeDuration }} for workflow {{ $labels.workflow_id }}"

  - alert: WorkflowQueueBacklog
    expr: workflow_queue_depth > 100
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High workflow queue depth"
      description: "Workflow queue has {{ $value }} pending workflows"
```

## Dashboards

### 1. Grafana Platform Overview Dashboard

```json
{
  "dashboard": {
    "title": "AI Platform Overview",
    "tags": ["platform", "overview"],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "panels": [
      {
        "title": "Platform Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"platform-services\"}",
            "legendFormat": "{{ instance }}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        }
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total[5m])) by (service)",
            "legendFormat": "{{ service }}"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)",
            "legendFormat": "{{ service }}"
          }
        ],
        "yAxes": [
          {
            "unit": "percentunit",
            "max": 1
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))",
            "legendFormat": "95th percentile - {{ service }}"
          },
          {
            "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))",
            "legendFormat": "50th percentile - {{ service }}"
          }
        ],
        "yAxes": [
          {
            "unit": "s"
          }
        ]
      },
      {
        "title": "Active Agents",
        "type": "stat",
        "targets": [
          {
            "expr": "count(up{component=\"agent\"} == 1)",
            "legendFormat": "Total Active"
          }
        ]
      },
      {
        "title": "Agent Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(agent_tasks_total{status=\"success\"}[5m])) by (agent_type)",
            "legendFormat": "Success - {{ agent_type }}"
          },
          {
            "expr": "sum(rate(agent_tasks_total{status=\"error\"}[5m])) by (agent_type)",
            "legendFormat": "Error - {{ agent_type }}"
          }
        ]
      },
      {
        "title": "Workflow Executions",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(workflow_executions_total{status=\"success\"}[5m]))",
            "legendFormat": "Successful"
          },
          {
            "expr": "sum(rate(workflow_executions_total{status=\"error\"}[5m]))",
            "legendFormat": "Failed"
          }
        ]
      },
      {
        "title": "Resource Utilization",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ],
        "yAxes": [
          {
            "unit": "percent",
            "max": 100
          }
        ]
      }
    ]
  }
}
```

### 2. Agent Performance Dashboard

```json
{
  "dashboard": {
    "title": "Agent Performance",
    "tags": ["agents", "performance"],
    "templating": {
      "list": [
        {
          "name": "agent_type",
          "type": "query",
          "query": "label_values(agent_tasks_total, agent_type)"
        },
        {
          "name": "agent_id",
          "type": "query",
          "query": "label_values(agent_tasks_total{agent_type=\"$agent_type\"}, agent_id)"
        }
      ]
    },
    "panels": [
      {
        "title": "Task Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(agent_tasks_total{agent_id=\"$agent_id\"}[5m])) by (status)",
            "legendFormat": "{{ status }}"
          }
        ]
      },
      {
        "title": "Task Duration",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(agent_task_duration_seconds_bucket{agent_id=\"$agent_id\"}[5m])) by (le))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, sum(rate(agent_task_duration_seconds_bucket{agent_id=\"$agent_id\"}[5m])) by (le))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Active Tasks",
        "type": "graph",
        "targets": [
          {
            "expr": "agent_active_tasks{agent_id=\"$agent_id\"}",
            "legendFormat": "Active Tasks"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "agent_memory_usage_bytes{agent_id=\"$agent_id\"}",
            "legendFormat": "Memory Usage"
          }
        ],
        "yAxes": [
          {
            "unit": "bytes"
          }
        ]
      },
      {
        "title": "AI Model Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(agent_ai_model_latency_seconds_bucket{agent_id=\"$agent_id\"}[5m])) by (le, model_provider, model_name))",
            "legendFormat": "{{ model_provider }}/{{ model_name }}"
          }
        ]
      }
    ]
  }
}
```

## SLI/SLO Monitoring

### 1. Service Level Indicators (SLIs)

```yaml
# sli_config.yaml
slis:
  platform_availability:
    description: "Platform API availability"
    query: "up{job='platform-services'}"
    threshold: 0.999  # 99.9% uptime
    
  agent_success_rate:
    description: "Agent task success rate"
    query: "sum(rate(agent_tasks_total{status='success'}[5m])) / sum(rate(agent_tasks_total[5m]))"
    threshold: 0.99  # 99% success rate
    
  workflow_completion_rate:
    description: "Workflow completion rate"
    query: "sum(rate(workflow_executions_total{status='success'}[5m])) / sum(rate(workflow_executions_total[5m]))"
    threshold: 0.95  # 95% completion rate
    
  api_latency:
    description: "API response time 95th percentile"
    query: "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
    threshold: 1.0  # 1 second
    
  agent_latency:
    description: "Agent task processing time 95th percentile"
    query: "histogram_quantile(0.95, rate(agent_task_duration_seconds_bucket[5m]))"
    threshold: 30.0  # 30 seconds
```

### 2. SLO Alert Rules

```yaml
# slo_rules.yml
groups:
- name: slo.rules
  interval: 30s
  rules:
  # Availability SLO
  - alert: PlatformAvailabilitySLOBreach
    expr: avg_over_time(up{job="platform-services"}[5m]) < 0.999
    for: 2m
    labels:
      severity: critical
      slo: availability
    annotations:
      summary: "Platform availability SLO breach"
      description: "Platform availability is {{ $value | humanizePercentage }}, below SLO of 99.9%"

  # Success Rate SLO
  - alert: AgentSuccessRateSLOBreach
    expr: (sum(rate(agent_tasks_total{status="success"}[5m])) / sum(rate(agent_tasks_total[5m]))) < 0.99
    for: 5m
    labels:
      severity: warning
      slo: success_rate
    annotations:
      summary: "Agent success rate SLO breach"
      description: "Agent success rate is {{ $value | humanizePercentage }}, below SLO of 99%"

  # Latency SLO
  - alert: APILatencySLOBreach
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
    for: 10m
    labels:
      severity: warning
      slo: latency
    annotations:
      summary: "API latency SLO breach"
      description: "API 95th percentile latency is {{ $value }}s, above SLO of 1s"

  # Error Budget Burn Rate
  - alert: HighErrorBudgetBurnRate
    expr: |
      (
        1 - (
          sum(rate(http_requests_total{status!~"5.."}[1h])) /
          sum(rate(http_requests_total[1h]))
        )
      ) > (0.001 * 14.4)  # 14.4x normal burn rate
    for: 2m
    labels:
      severity: critical
      slo: error_budget
    annotations:
      summary: "High error budget burn rate"
      description: "Error budget is burning at {{ $value | humanizePercentage }} rate"
```

## Custom Monitoring Tools

### 1. Health Check Endpoint

```python
# health_check.py
from flask import Flask, jsonify
import asyncio
import time
from datetime import datetime

app = Flask(__name__)

class HealthChecker:
    def __init__(self):
        self.checks = {
            'database': self.check_database,
            'redis': self.check_redis,
            'agents': self.check_agents,
            'ai_models': self.check_ai_models,
            'storage': self.check_storage
        }
    
    async def run_all_checks(self):
        results = {}
        overall_healthy = True
        
        for name, check_func in self.checks.items():
            try:
                result = await check_func()
                results[name] = result
                if not result.get('healthy', False):
                    overall_healthy = False
            except Exception as e:
                results[name] = {
                    'healthy': False,
                    'error': str(e)
                }
                overall_healthy = False
        
        return {
            'healthy': overall_healthy,
            'timestamp': datetime.utcnow().isoformat(),
            'checks': results
        }
    
    async def check_database(self):
        start_time = time.time()
        try:
            # Test database connection
            async with get_db_connection() as conn:
                await conn.fetch("SELECT 1")
            
            return {
                'healthy': True,
                'response_time': time.time() - start_time
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }
    
    async def check_agents(self):
        try:
            # Count active agents
            active_agents = await count_active_agents()
            
            return {
                'healthy': active_agents > 0,
                'active_agents': active_agents,
                'total_agents': await count_total_agents()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }

health_checker = HealthChecker()

@app.route('/health')
async def health():
    result = await health_checker.run_all_checks()
    status_code = 200 if result['healthy'] else 503
    return jsonify(result), status_code

@app.route('/health/live')
def liveness():
    return jsonify({'status': 'alive'}), 200

@app.route('/health/ready')
async def readiness():
    # Quick readiness check
    try:
        # Test critical dependencies
        await quick_db_check()
        return jsonify({'status': 'ready'}), 200
    except Exception as e:
        return jsonify({'status': 'not ready', 'error': str(e)}), 503

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
```

### 2. Performance Profiler

```python
# profiler.py
import cProfile
import pstats
import io
from functools import wraps
import time
import psutil
import threading
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class PerformanceSnapshot:
    timestamp: float
    cpu_percent: float
    memory_usage: int
    active_threads: int
    request_count: int

class PerformanceProfiler:
    def __init__(self):
        self.snapshots: List[PerformanceSnapshot] = []
        self.profiling_enabled = False
        self.profiler = cProfile.Profile()
        
    def start_profiling(self):
        self.profiling_enabled = True
        self.profiler.enable()
        
        # Start background monitoring
        threading.Thread(target=self._monitor_loop, daemon=True).start()
    
    def stop_profiling(self):
        self.profiling_enabled = False
        self.profiler.disable()
    
    def _monitor_loop(self):
        while self.profiling_enabled:
            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                cpu_percent=psutil.cpu_percent(),
                memory_usage=psutil.Process().memory_info().rss,
                active_threads=threading.active_count(),
                request_count=self.get_request_count()
            )
            self.snapshots.append(snapshot)
            
            # Keep only last 1000 snapshots
            if len(self.snapshots) > 1000:
                self.snapshots.pop(0)
            
            time.sleep(5)  # Sample every 5 seconds
    
    def get_profile_stats(self):
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # Top 20 functions
        return s.getvalue()
    
    def get_performance_summary(self):
        if not self.snapshots:
            return {}
        
        recent_snapshots = self.snapshots[-12:]  # Last minute (5s intervals)
        
        return {
            'avg_cpu_percent': sum(s.cpu_percent for s in recent_snapshots) / len(recent_snapshots),
            'current_memory_mb': self.snapshots[-1].memory_usage / 1024 / 1024,
            'active_threads': self.snapshots[-1].active_threads,
            'request_rate': self.calculate_request_rate(recent_snapshots)
        }

def profile_function(func):
    """Decorator to profile individual functions"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        profiler.enable()
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            execution_time = time.time() - start_time
            profiler.disable()
            
            # Log performance data
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(10)
            
            logger.info(
                f"Function {func.__name__} executed",
                execution_time=execution_time,
                profile_stats=s.getvalue()
            )
    
    return wrapper
```

## Best Practices

### 1. Monitoring Strategy
- **Monitor everything**: Applications, infrastructure, and business metrics
- **Use SLIs and SLOs**: Define clear service level objectives
- **Implement proper alerting**: Alert on symptoms, not causes
- **Create meaningful dashboards**: Focus on actionable insights

### 2. Log Management
- **Use structured logging**: JSON format for easy parsing
- **Include correlation IDs**: Track requests across services
- **Implement log retention**: Balance storage costs with debugging needs
- **Centralize logs**: Aggregate logs from all components

### 3. Tracing Implementation
- **Trace critical paths**: Focus on user-facing operations
- **Include business context**: Add meaningful attributes to spans
- **Use sampling**: Reduce overhead while maintaining visibility
- **Correlate with metrics**: Link traces to performance data

### 4. Alert Management
- **Avoid alert fatigue**: Keep alert volume manageable
- **Use severity levels**: Differentiate critical from warning alerts
- **Include runbooks**: Provide clear remediation steps
- **Test alert systems**: Verify alerts reach the right people

## Conclusion

Comprehensive monitoring and observability are essential for operating the AI-Native Agent Platform effectively. The combination of metrics, logs, traces, and alerts provides complete visibility into system behavior and enables proactive issue resolution.

Key recommendations:
- Start with basic monitoring and gradually add sophistication
- Focus on user-facing metrics and SLOs
- Automate as much as possible
- Regularly review and update monitoring configuration
- Train team members on monitoring tools and procedures

For additional information:
- [Troubleshooting Guide](../tutorials/troubleshooting.md)
- [Architecture Overview](../architecture/system-design.md)
- [Kubernetes Deployment](kubernetes.md)