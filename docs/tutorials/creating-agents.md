# Creating Agents - Complete Guide

## Overview

This comprehensive guide covers everything you need to know about creating agents on the AI-Native Agent Platform. From basic concepts to advanced customization, you'll learn how to build powerful, intelligent agents that can solve real-world problems.

## Table of Contents

1. [Agent Fundamentals](#agent-fundamentals)
2. [Agent Architecture](#agent-architecture)
3. [Creating Agents with Templates](#creating-agents-with-templates)
4. [Custom Agent Development](#custom-agent-development)
5. [Agent Configuration](#agent-configuration)
6. [Multi-Language Support](#multi-language-support)
7. [AI Model Integration](#ai-model-integration)
8. [Testing and Validation](#testing-and-validation)
9. [Deployment and Scaling](#deployment-and-scaling)
10. [Best Practices](#best-practices)

## Agent Fundamentals

### What Makes an Agent "Intelligent"?

An intelligent agent on our platform has these characteristics:

1. **Autonomy**: Can operate independently without constant human intervention
2. **Reactivity**: Responds to environmental changes and events
3. **Proactivity**: Takes initiative to achieve goals
4. **Social Ability**: Communicates and coordinates with other agents
5. **Learning**: Improves performance over time

### Agent Lifecycle

```mermaid
graph TD
    A[Design] --> B[Create]
    B --> C[Deploy]
    C --> D[Active]
    D --> E[Monitor]
    E --> F[Update]
    F --> D
    D --> G[Deprecate]
    G --> H[Retire]
```

### Core Components

Every agent consists of:

- **Core Logic**: The main processing capability
- **Communication Interface**: How it talks to other agents
- **State Management**: How it maintains context
- **Configuration**: Settings and parameters
- **Health Monitoring**: Self-diagnosis capabilities

## Agent Architecture

### Standard Agent Structure

```
agent/
├── src/
│   ├── main.py (or Main.java, main.go)
│   ├── config/
│   │   └── agent_config.yaml
│   ├── handlers/
│   │   ├── message_handler.py
│   │   ├── task_handler.py
│   │   └── event_handler.py
│   ├── services/
│   │   ├── ai_service.py
│   │   ├── data_service.py
│   │   └── integration_service.py
│   ├── models/
│   │   ├── request_models.py
│   │   └── response_models.py
│   └── utils/
│       ├── logger.py
│       ├── validator.py
│       └── helpers.py
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
├── docs/
│   ├── README.md
│   ├── API.md
│   └── deployment.md
├── config/
│   ├── development.yaml
│   ├── staging.yaml
│   └── production.yaml
├── Dockerfile
├── requirements.txt (or pom.xml, go.mod)
└── BUILD.bazel
```

### Agent Framework Components

#### Base Agent Class (Python)

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from platform_sdk import AgentBase, Message, Response

class IntelligentAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.capabilities = config.get('capabilities', [])
        self.ai_models = self.initialize_ai_models(config.get('ai_models', []))
        self.integrations = self.initialize_integrations(config.get('integrations', []))
        
    @abstractmethod
    async def process_message(self, message: Message) -> Response:
        """Process incoming messages"""
        pass
    
    @abstractmethod
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific task"""
        pass
    
    async def learn_from_interaction(self, interaction: Dict[str, Any]):
        """Learn from completed interactions"""
        knowledge_data = {
            'agent_id': self.agent_id,
            'interaction_type': interaction.get('type'),
            'performance_metrics': interaction.get('metrics'),
            'outcome': interaction.get('outcome'),
            'timestamp': datetime.utcnow()
        }
        
        await self.knowledge_base.store(knowledge_data)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Return agent health status"""
        return {
            'status': 'healthy',
            'capabilities': self.capabilities,
            'resource_usage': self.get_resource_usage(),
            'last_activity': self.last_activity,
            'uptime': self.get_uptime()
        }
```

#### Agent Communication Interface

```python
class CommunicationInterface:
    def __init__(self, agent_id: str, broker_url: str):
        self.agent_id = agent_id
        self.broker = MessageBroker(broker_url)
        self.message_handlers = {}
        
    async def send_message(self, target_agent: str, message: Dict[str, Any]) -> Response:
        """Send message to another agent"""
        message_envelope = {
            'from': self.agent_id,
            'to': target_agent,
            'timestamp': datetime.utcnow().isoformat(),
            'message_id': str(uuid.uuid4()),
            'content': message
        }
        
        response = await self.broker.send(message_envelope)
        return response
    
    async def publish_event(self, event_type: str, event_data: Dict[str, Any]):
        """Publish an event to the event bus"""
        event = {
            'type': event_type,
            'source': self.agent_id,
            'timestamp': datetime.utcnow().isoformat(),
            'data': event_data
        }
        
        await self.broker.publish(event)
    
    def register_handler(self, message_type: str, handler_func):
        """Register a message handler"""
        self.message_handlers[message_type] = handler_func
    
    async def handle_incoming_message(self, message: Dict[str, Any]):
        """Handle incoming messages"""
        message_type = message.get('type', 'default')
        handler = self.message_handlers.get(message_type)
        
        if handler:
            return await handler(message)
        else:
            return {'status': 'error', 'message': f'No handler for message type: {message_type}'}
```

## Creating Agents with Templates

### Available Templates

The platform provides several pre-built templates:

#### 1. Data Processing Agent Template

Best for: ETL operations, data transformation, batch processing

```yaml
template_id: data-processing-agent
language: python
capabilities:
  - data-transformation
  - batch-processing
  - stream-processing
  - data-validation
default_config:
  batch_size: 1000
  max_memory: 4Gi
  processing_timeout: 300
integrations:
  - postgresql
  - redis
  - kafka
  - s3
```

#### 2. AI/ML Agent Template

Best for: Machine learning inference, model training, data analysis

```yaml
template_id: ai-ml-agent
language: python
capabilities:
  - model-inference
  - data-analysis
  - feature-extraction
  - prediction
default_config:
  model_cache_size: 2Gi
  inference_timeout: 30
  batch_inference: true
ai_models:
  - openai/gpt-4
  - anthropic/claude-2
  - huggingface/bert-base
```

#### 3. Integration Agent Template

Best for: API integrations, webhook handling, external service communication

```yaml
template_id: integration-agent
language: java
capabilities:
  - api-integration
  - webhook-handling
  - data-sync
  - protocol-translation
default_config:
  connection_pool_size: 50
  retry_attempts: 3
  circuit_breaker: true
integrations:
  - rest-api
  - graphql
  - websocket
  - grpc
```

#### 4. System Agent Template

Best for: Infrastructure monitoring, system operations, maintenance tasks

```yaml
template_id: system-agent
language: go
capabilities:
  - system-monitoring
  - resource-management
  - maintenance-tasks
  - alert-handling
default_config:
  monitoring_interval: 30
  alert_threshold: 80
  cleanup_enabled: true
```

### Using Templates

#### Via Dashboard

1. Navigate to **Agents** → **Create New Agent**
2. Select your preferred template
3. Fill in the configuration form:

```yaml
# Agent Configuration
name: customer-data-processor
description: Processes customer data from various sources
template: data-processing-agent
language: python

# Resource Requirements
resources:
  cpu: 2
  memory: 4Gi
  storage: 10Gi
  
# Agent-Specific Configuration
configuration:
  batch_size: 2000
  input_format: json
  output_format: parquet
  validation_rules:
    - required_fields: [customer_id, email, created_at]
    - email_format: true
    - date_validation: true
    
# Integrations
integrations:
  - type: database
    provider: postgresql
    connection: primary-db
  - type: storage
    provider: s3
    bucket: customer-data
    
# AI Model Configuration
ai_models:
  - provider: openai
    model: gpt-3.5-turbo
    purpose: data-quality-analysis
```

#### Via API

```bash
curl -X POST https://api.platform.io/v1/agents \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "customer-data-processor",
    "template_id": "data-processing-agent",
    "language": "python",
    "configuration": {
      "batch_size": 2000,
      "input_format": "json",
      "output_format": "parquet"
    },
    "resources": {
      "cpu": "2",
      "memory": "4Gi",
      "storage": "10Gi"
    },
    "integrations": [
      {
        "type": "database",
        "provider": "postgresql",
        "connection": "primary-db"
      }
    ]
  }'
```

#### Using SDK

```python
from platform_sdk import PlatformClient

client = PlatformClient(api_key="YOUR_API_KEY")

agent_config = {
    "name": "customer-data-processor",
    "template_id": "data-processing-agent",
    "language": "python",
    "configuration": {
        "batch_size": 2000,
        "input_format": "json",
        "output_format": "parquet"
    },
    "resources": {
        "cpu": "2",
        "memory": "4Gi",
        "storage": "10Gi"
    }
}

agent = client.agents.create(agent_config)
print(f"Agent created: {agent.id}")
```

## Custom Agent Development

### Building from Scratch

For maximum flexibility, you can build agents from scratch:

#### Step 1: Create Agent Specification

```yaml
# agent-spec.yaml
apiVersion: agents.platform.io/v1
kind: AgentSpecification
metadata:
  name: custom-document-analyzer
  namespace: document-processing
spec:
  language: python
  version: 1.0.0
  capabilities:
    - document-parsing
    - content-analysis
    - entity-extraction
    - sentiment-analysis
  resources:
    cpu: 4
    memory: 8Gi
    storage: 20Gi
  dependencies:
    - pandas>=1.5.0
    - spacy>=3.4.0
    - transformers>=4.20.0
    - boto3>=1.24.0
  environment:
    PYTHONPATH: /app/src
    LOG_LEVEL: INFO
    MAX_WORKERS: 4
  security:
    run_as_user: 1000
    read_only_root_fs: true
    capabilities_drop:
      - ALL
```

#### Step 2: Implement Agent Logic

```python
# src/document_analyzer_agent.py
import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime

import spacy
import pandas as pd
from transformers import pipeline
from platform_sdk import AgentBase, Message, Response

class DocumentAnalyzerAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.nlp = spacy.load("en_core_web_sm")
        self.sentiment_analyzer = pipeline("sentiment-analysis")
        self.entity_extractor = pipeline("ner", aggregation_strategy="simple")
        
    async def process_message(self, message: Message) -> Response:
        """Main message processing entry point"""
        try:
            message_type = message.get_type()
            
            if message_type == "analyze_document":
                return await self.analyze_document(message.get_content())
            elif message_type == "batch_analyze":
                return await self.batch_analyze(message.get_content())
            else:
                return Response.error(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logging.error(f"Error processing message: {str(e)}")
            return Response.error(str(e))
    
    async def analyze_document(self, content: Dict[str, Any]) -> Response:
        """Analyze a single document"""
        document_text = content.get("text", "")
        document_id = content.get("document_id")
        
        if not document_text:
            return Response.error("No text content provided")
        
        # Perform analysis
        analysis_result = await self._perform_analysis(document_text)
        
        # Store results
        result = {
            "document_id": document_id,
            "analysis": analysis_result,
            "timestamp": datetime.utcnow().isoformat(),
            "agent_id": self.agent_id
        }
        
        # Learn from this interaction
        await self.learn_from_interaction({
            "type": "document_analysis",
            "document_length": len(document_text),
            "processing_time": analysis_result.get("processing_time"),
            "outcome": "success"
        })
        
        return Response.success(result)
    
    async def _perform_analysis(self, text: str) -> Dict[str, Any]:
        """Perform comprehensive document analysis"""
        start_time = datetime.utcnow()
        
        # NLP processing
        doc = self.nlp(text)
        
        # Extract entities
        entities = []
        for ent in doc.ents:
            entities.append({
                "text": ent.text,
                "label": ent.label_,
                "start": ent.start_char,
                "end": ent.end_char,
                "confidence": getattr(ent, 'confidence', 1.0)
            })
        
        # Sentiment analysis
        sentiment_result = self.sentiment_analyzer(text[:512])  # Limit to model's max length
        sentiment = {
            "label": sentiment_result[0]["label"],
            "score": sentiment_result[0]["score"]
        }
        
        # Extract key statistics
        stats = {
            "word_count": len(doc),
            "sentence_count": len(list(doc.sents)),
            "character_count": len(text),
            "unique_entities": len(set([ent["text"] for ent in entities]))
        }
        
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return {
            "entities": entities,
            "sentiment": sentiment,
            "statistics": stats,
            "processing_time": processing_time,
            "language": doc.lang_
        }
    
    async def batch_analyze(self, content: Dict[str, Any]) -> Response:
        """Analyze multiple documents in batch"""
        documents = content.get("documents", [])
        batch_size = content.get("batch_size", 10)
        
        results = []
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_results = await asyncio.gather(*[
                self._perform_analysis(doc["text"]) 
                for doc in batch
            ])
            
            for j, result in enumerate(batch_results):
                results.append({
                    "document_id": batch[j].get("document_id"),
                    "analysis": result
                })
        
        return Response.success({
            "batch_id": content.get("batch_id"),
            "processed_count": len(results),
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def get_capabilities(self) -> List[str]:
        """Return agent capabilities"""
        return [
            "document-parsing",
            "content-analysis", 
            "entity-extraction",
            "sentiment-analysis",
            "batch-processing"
        ]

# Agent entry point
async def main():
    config = load_agent_config()
    agent = DocumentAnalyzerAgent(config)
    await agent.start()

if __name__ == "__main__":
    asyncio.run(main())
```

#### Step 3: Create Dockerfile

```dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY src/ ./src/
COPY config/ ./config/

# Create non-root user
RUN useradd -u 1000 -m agent && chown -R agent:agent /app
USER agent

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8080/health')"

# Start the agent
CMD ["python", "src/document_analyzer_agent.py"]
```

#### Step 4: Build Configuration

```python
# BUILD.bazel
load("@rules_python//python:defs.bzl", "py_binary", "py_library")
load("@io_bazel_rules_docker//python3:image.bzl", "py3_image")

py_library(
    name = "document_analyzer_lib",
    srcs = [
        "src/document_analyzer_agent.py",
    ],
    deps = [
        "//platform_sdk:sdk",
        "@pip//spacy",
        "@pip//transformers",
        "@pip//pandas",
    ],
)

py_binary(
    name = "document_analyzer",
    srcs = ["src/document_analyzer_agent.py"],
    deps = [":document_analyzer_lib"],
    main = "src/document_analyzer_agent.py",
)

py3_image(
    name = "document_analyzer_image",
    srcs = ["src/document_analyzer_agent.py"],
    deps = [":document_analyzer_lib"],
    main = "src/document_analyzer_agent.py",
    base = "@python3_base//image",
)
```

#### Step 5: Deploy Custom Agent

```bash
# Build the agent
bazel build //agents/document-analyzer:document_analyzer_image

# Push to registry
bazel run //agents/document-analyzer:document_analyzer_image

# Deploy using platform API
curl -X POST https://api.platform.io/v1/agent-factory/deploy \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_spec": "@agent-spec.yaml",
    "container_image": "registry.platform.io/agents/document-analyzer:latest",
    "deployment_config": {
      "environment": "production",
      "replicas": 3
    }
  }'
```

## Agent Configuration

### Configuration Hierarchy

Agent configuration follows this hierarchy (highest to lowest priority):

1. Runtime environment variables
2. Command-line arguments
3. Agent-specific configuration files
4. Template defaults
5. Platform defaults

### Configuration Schema

```yaml
# agent-config.yaml
agent:
  id: ${AGENT_ID}
  name: document-analyzer
  version: 1.0.0
  description: "Analyzes documents for entities and sentiment"
  
capabilities:
  - document-parsing
  - entity-extraction
  - sentiment-analysis
  
resources:
  cpu:
    min: "1"
    max: "4"
    default: "2"
  memory:
    min: "2Gi"
    max: "8Gi"
    default: "4Gi"
  storage:
    size: "10Gi"
    type: "ssd"
    
performance:
  batch_size: 100
  max_workers: 4
  timeout_seconds: 300
  cache_size_mb: 512
  
integrations:
  database:
    type: postgresql
    host: ${DB_HOST}
    port: ${DB_PORT}
    database: ${DB_NAME}
    ssl_mode: require
    
  storage:
    type: s3
    bucket: ${STORAGE_BUCKET}
    region: ${AWS_REGION}
    
  monitoring:
    metrics_enabled: true
    tracing_enabled: true
    log_level: INFO
    
ai_models:
  - provider: openai
    model: gpt-4
    api_key: ${OPENAI_API_KEY}
    max_tokens: 2048
    temperature: 0.1
    
  - provider: huggingface
    model: bert-base-uncased
    cache_dir: /app/model_cache
    
security:
  encryption:
    enabled: true
    algorithm: AES-256-GCM
    
  authentication:
    type: oauth2
    client_id: ${OAUTH_CLIENT_ID}
    client_secret: ${OAUTH_CLIENT_SECRET}
    
  permissions:
    - read:documents
    - write:analysis_results
    - execute:ai_models
    
health_checks:
  liveness:
    endpoint: /health/live
    interval_seconds: 30
    timeout_seconds: 5
    
  readiness:
    endpoint: /health/ready
    interval_seconds: 10
    timeout_seconds: 3
    initial_delay_seconds: 30
```

### Dynamic Configuration

Agents can update their configuration at runtime:

```python
class ConfigurableAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.config_manager = ConfigManager(config)
        
    async def update_configuration(self, new_config: Dict[str, Any]):
        """Update agent configuration dynamically"""
        try:
            # Validate new configuration
            validated_config = self.config_manager.validate(new_config)
            
            # Apply configuration changes
            changes = self.config_manager.apply_changes(validated_config)
            
            # Restart necessary components
            for component, change_type in changes.items():
                if change_type == "restart_required":
                    await self.restart_component(component)
                elif change_type == "reload_required":
                    await self.reload_component(component)
            
            # Update health status
            await self.update_health_status()
            
            return {"status": "success", "changes_applied": len(changes)}
            
        except Exception as e:
            logging.error(f"Configuration update failed: {str(e)}")
            return {"status": "error", "message": str(e)}
```

## Multi-Language Support

### Java Agents

Java agents excel at enterprise integration and high-performance processing:

```java
// JavaDocumentProcessor.java
@Component
public class JavaDocumentProcessor extends AgentBase {
    
    @Autowired
    private AIModelService aiModelService;
    
    @Autowired
    private IntegrationService integrationService;
    
    @Override
    public CompletableFuture<Response> processMessage(Message message) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String messageType = message.getType();
                
                switch (messageType) {
                    case "process_document":
                        return processDocument(message.getContent());
                    case "batch_process":
                        return batchProcess(message.getContent());
                    default:
                        return Response.error("Unknown message type: " + messageType);
                }
            } catch (Exception e) {
                log.error("Error processing message", e);
                return Response.error(e.getMessage());
            }
        });
    }
    
    private Response processDocument(Map<String, Object> content) {
        String documentId = (String) content.get("documentId");
        String documentText = (String) content.get("text");
        
        // Validate input
        if (documentText == null || documentText.isEmpty()) {
            return Response.error("No document text provided");
        }
        
        // Process with AI model
        AIModelRequest request = AIModelRequest.builder()
            .text(documentText)
            .model("gpt-4")
            .maxTokens(2048)
            .build();
            
        AIModelResponse aiResponse = aiModelService.process(request);
        
        // Store results
        ProcessingResult result = ProcessingResult.builder()
            .documentId(documentId)
            .analysis(aiResponse.getContent())
            .timestamp(Instant.now())
            .agentId(getAgentId())
            .build();
            
        // Save to database
        integrationService.saveResult(result);
        
        return Response.success(result);
    }
    
    @Override
    public List<String> getCapabilities() {
        return Arrays.asList(
            "document-processing",
            "enterprise-integration",
            "high-throughput-processing"
        );
    }
}
```

### Go Agents

Go agents are ideal for system-level operations and network services:

```go
// main.go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "time"
    
    "github.com/ai-platform/sdk-go/agent"
    "github.com/ai-platform/sdk-go/types"
)

type SystemMonitorAgent struct {
    agent.BaseAgent
    metricsCollector *MetricsCollector
    alertManager     *AlertManager
}

func NewSystemMonitorAgent(config *agent.Config) *SystemMonitorAgent {
    return &SystemMonitorAgent{
        BaseAgent:        agent.NewBaseAgent(config),
        metricsCollector: NewMetricsCollector(),
        alertManager:     NewAlertManager(),
    }
}

func (s *SystemMonitorAgent) ProcessMessage(ctx context.Context, msg *types.Message) (*types.Response, error) {
    switch msg.Type {
    case "monitor_system":
        return s.monitorSystem(ctx, msg.Content)
    case "check_alerts":
        return s.checkAlerts(ctx, msg.Content)
    case "get_metrics":
        return s.getMetrics(ctx, msg.Content)
    default:
        return types.ErrorResponse(fmt.Sprintf("Unknown message type: %s", msg.Type)), nil
    }
}

func (s *SystemMonitorAgent) monitorSystem(ctx context.Context, content map[string]interface{}) (*types.Response, error) {
    // Collect system metrics
    metrics, err := s.metricsCollector.CollectSystemMetrics()
    if err != nil {
        return types.ErrorResponse(err.Error()), nil
    }
    
    // Check thresholds and generate alerts
    alerts := s.alertManager.CheckThresholds(metrics)
    
    // Store metrics in time series database
    if err := s.storeMetrics(metrics); err != nil {
        log.Printf("Failed to store metrics: %v", err)
    }
    
    result := map[string]interface{}{
        "metrics": metrics,
        "alerts":  alerts,
        "timestamp": time.Now().Unix(),
        "agent_id": s.GetAgentID(),
    }
    
    return types.SuccessResponse(result), nil
}

func (s *SystemMonitorAgent) GetCapabilities() []string {
    return []string{
        "system-monitoring",
        "resource-tracking",
        "alert-generation",
        "metrics-collection",
    }
}

func main() {
    config := &agent.Config{
        AgentID:     os.Getenv("AGENT_ID"),
        BrokerURL:   os.Getenv("BROKER_URL"),
        LogLevel:    "INFO",
    }
    
    agent := NewSystemMonitorAgent(config)
    
    if err := agent.Start(context.Background()); err != nil {
        log.Fatalf("Failed to start agent: %v", err)
    }
}
```

## AI Model Integration

### Connecting to AI Providers

Agents can seamlessly integrate with multiple AI model providers:

```python
# ai_service.py
from typing import Dict, Any, List, Optional
from platform_sdk.ai import AIModelClient

class AIService:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_client = AIModelClient(config)
        self.model_router = self._setup_model_router()
        
    def _setup_model_router(self):
        """Configure AI model routing based on task requirements"""
        routing_rules = {
            'text_generation': {
                'primary': 'openai/gpt-4',
                'fallback': 'anthropic/claude-2',
                'cost_optimization': 'openai/gpt-3.5-turbo'
            },
            'text_analysis': {
                'primary': 'anthropic/claude-2',
                'fallback': 'openai/gpt-4'
            },
            'embedding': {
                'primary': 'openai/text-embedding-ada-002',
                'fallback': 'huggingface/sentence-transformers'
            },
            'image_analysis': {
                'primary': 'openai/gpt-4-vision',
                'fallback': 'google/gemini-pro-vision'
            }
        }
        
        return ModelRouter(routing_rules)
    
    async def generate_text(self, prompt: str, task_type: str = 'text_generation', **kwargs) -> Dict[str, Any]:
        """Generate text using appropriate AI model"""
        model = self.model_router.select_model(task_type, kwargs.get('requirements', {}))
        
        request = {
            'model': model,
            'prompt': prompt,
            'max_tokens': kwargs.get('max_tokens', 1000),
            'temperature': kwargs.get('temperature', 0.7),
            'stream': kwargs.get('stream', False)
        }
        
        try:
            response = await self.model_client.complete(request)
            return {
                'status': 'success',
                'content': response.content,
                'model_used': model,
                'usage': response.usage,
                'cost': response.cost
            }
        except Exception as e:
            # Try fallback model
            fallback_model = self.model_router.get_fallback(task_type)
            if fallback_model and fallback_model != model:
                request['model'] = fallback_model
                response = await self.model_client.complete(request)
                return {
                    'status': 'success',
                    'content': response.content,
                    'model_used': fallback_model,
                    'usage': response.usage,
                    'cost': response.cost,
                    'fallback_used': True
                }
            else:
                return {
                    'status': 'error',
                    'error': str(e),
                    'model_used': model
                }
    
    async def analyze_text(self, text: str, analysis_types: List[str]) -> Dict[str, Any]:
        """Perform various text analysis tasks"""
        results = {}
        
        for analysis_type in analysis_types:
            if analysis_type == 'sentiment':
                results['sentiment'] = await self._analyze_sentiment(text)
            elif analysis_type == 'entities':
                results['entities'] = await self._extract_entities(text)
            elif analysis_type == 'summary':
                results['summary'] = await self._summarize_text(text)
            elif analysis_type == 'topics':
                results['topics'] = await self._extract_topics(text)
        
        return results
    
    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        prompt = f"""
        Analyze the sentiment of the following text. Provide:
        1. Overall sentiment (positive, negative, neutral)
        2. Confidence score (0-1)
        3. Brief explanation
        
        Text: {text}
        
        Response format:
        {{
            "sentiment": "positive|negative|neutral",
            "confidence": 0.95,
            "explanation": "Brief explanation of the sentiment analysis"
        }}
        """
        
        response = await self.generate_text(prompt, task_type='text_analysis', temperature=0.1)
        
        if response['status'] == 'success':
            try:
                import json
                sentiment_data = json.loads(response['content'])
                return sentiment_data
            except json.JSONDecodeError:
                return {'error': 'Failed to parse sentiment analysis response'}
        else:
            return response
```

### Model Performance Optimization

```python
class ModelPerformanceOptimizer:
    def __init__(self):
        self.performance_history = {}
        self.cost_tracker = CostTracker()
        
    async def optimize_model_selection(self, task_requirements: Dict[str, Any]) -> str:
        """Select optimal model based on performance history and requirements"""
        available_models = self._get_available_models(task_requirements['type'])
        
        # Score models based on requirements
        model_scores = {}
        for model in available_models:
            score = self._calculate_model_score(model, task_requirements)
            model_scores[model] = score
        
        # Select best performing model
        best_model = max(model_scores, key=model_scores.get)
        
        # Log decision for learning
        await self._log_model_selection(best_model, task_requirements, model_scores)
        
        return best_model
    
    def _calculate_model_score(self, model: str, requirements: Dict[str, Any]) -> float:
        """Calculate composite score for model selection"""
        performance_weight = requirements.get('performance_weight', 0.4)
        cost_weight = requirements.get('cost_weight', 0.3)
        latency_weight = requirements.get('latency_weight', 0.3)
        
        # Get historical performance metrics
        history = self.performance_history.get(model, {})
        
        performance_score = history.get('avg_quality_score', 0.5)
        cost_score = 1.0 - (history.get('avg_cost', 0.5) / history.get('max_cost', 1.0))
        latency_score = 1.0 - (history.get('avg_latency', 0.5) / history.get('max_latency', 1.0))
        
        composite_score = (
            performance_score * performance_weight +
            cost_score * cost_weight +
            latency_score * latency_weight
        )
        
        return composite_score
```

## Testing and Validation

### Unit Testing Framework

```python
# tests/test_document_analyzer.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from src.document_analyzer_agent import DocumentAnalyzerAgent

class TestDocumentAnalyzerAgent:
    @pytest.fixture
    def agent_config(self):
        return {
            'agent_id': 'test-agent-123',
            'capabilities': ['document-parsing', 'entity-extraction'],
            'ai_models': [
                {'provider': 'openai', 'model': 'gpt-4'}
            ]
        }
    
    @pytest.fixture
    def agent(self, agent_config):
        return DocumentAnalyzerAgent(agent_config)
    
    @pytest.mark.asyncio
    async def test_analyze_document_success(self, agent):
        """Test successful document analysis"""
        # Arrange
        test_content = {
            'document_id': 'doc-123',
            'text': 'This is a test document with some entities like John Smith and New York.'
        }
        
        # Mock AI model responses
        with patch.object(agent, '_perform_analysis') as mock_analysis:
            mock_analysis.return_value = {
                'entities': [
                    {'text': 'John Smith', 'label': 'PERSON'},
                    {'text': 'New York', 'label': 'GPE'}
                ],
                'sentiment': {'label': 'NEUTRAL', 'score': 0.7},
                'processing_time': 0.5
            }
            
            # Act
            response = await agent.analyze_document(test_content)
            
            # Assert
            assert response.status == 'success'
            assert response.data['document_id'] == 'doc-123'
            assert len(response.data['analysis']['entities']) == 2
            mock_analysis.assert_called_once_with(test_content['text'])
    
    @pytest.mark.asyncio
    async def test_analyze_document_empty_text(self, agent):
        """Test document analysis with empty text"""
        # Arrange
        test_content = {
            'document_id': 'doc-123',
            'text': ''
        }
        
        # Act
        response = await agent.analyze_document(test_content)
        
        # Assert
        assert response.status == 'error'
        assert 'No text content provided' in response.error_message
    
    @pytest.mark.asyncio
    async def test_batch_analyze(self, agent):
        """Test batch document analysis"""
        # Arrange
        test_content = {
            'batch_id': 'batch-123',
            'documents': [
                {'document_id': 'doc-1', 'text': 'First document'},
                {'document_id': 'doc-2', 'text': 'Second document'}
            ],
            'batch_size': 2
        }
        
        # Mock AI model responses
        with patch.object(agent, '_perform_analysis') as mock_analysis:
            mock_analysis.return_value = {
                'entities': [],
                'sentiment': {'label': 'NEUTRAL', 'score': 0.5},
                'processing_time': 0.1
            }
            
            # Act
            response = await agent.batch_analyze(test_content)
            
            # Assert
            assert response.status == 'success'
            assert response.data['processed_count'] == 2
            assert len(response.data['results']) == 2
            assert mock_analysis.call_count == 2
```

### Integration Testing

```python
# tests/test_integration.py
import pytest
import asyncio
from platform_sdk.testing import TestHarness, MockMessageBroker

class TestAgentIntegration:
    @pytest.fixture
    def test_harness(self):
        return TestHarness({
            'mock_broker': True,
            'mock_ai_models': True,
            'mock_database': True
        })
    
    @pytest.mark.asyncio
    async def test_end_to_end_document_processing(self, test_harness):
        """Test complete document processing workflow"""
        # Arrange
        agent = await test_harness.create_agent('document-analyzer')
        
        # Simulate document upload
        document_message = {
            'type': 'analyze_document',
            'content': {
                'document_id': 'test-doc-001',
                'text': 'Contract between John Doe and ACME Corp for $10,000.'
            }
        }
        
        # Act
        response = await test_harness.send_message(agent.id, document_message)
        
        # Assert
        assert response.status == 'success'
        assert 'John Doe' in str(response.data['analysis']['entities'])
        assert 'ACME Corp' in str(response.data['analysis']['entities'])
        
        # Verify message was logged in knowledge base
        knowledge_entries = await test_harness.get_knowledge_entries(agent.id)
        assert len(knowledge_entries) > 0
        assert knowledge_entries[0]['type'] == 'document_analysis'
```

### Performance Testing

```python
# tests/test_performance.py
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestAgentPerformance:
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, agent):
        """Test agent performance under concurrent load"""
        # Arrange
        num_concurrent_requests = 50
        request_template = {
            'type': 'analyze_document',
            'content': {
                'document_id': 'load-test-{}',
                'text': 'This is a load testing document with various entities and content.'
            }
        }
        
        # Create concurrent requests
        tasks = []
        for i in range(num_concurrent_requests):
            request = request_template.copy()
            request['content']['document_id'] = f'load-test-{i}'
            tasks.append(agent.process_message(request))
        
        # Act
        start_time = time.time()
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Assert
        successful_responses = [r for r in responses if not isinstance(r, Exception)]
        assert len(successful_responses) >= num_concurrent_requests * 0.95  # 95% success rate
        
        total_time = end_time - start_time
        avg_response_time = total_time / num_concurrent_requests
        assert avg_response_time < 2.0  # Average response time under 2 seconds
        
        # Check for resource leaks
        memory_usage = agent.get_memory_usage()
        assert memory_usage < 1024 * 1024 * 1024  # Under 1GB
```

## Deployment and Scaling

### Deployment Configuration

```yaml
# deployment/production.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: document-analyzer-agent
  namespace: agents
  labels:
    app: document-analyzer
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: document-analyzer
  template:
    metadata:
      labels:
        app: document-analyzer
        version: v1.0.0
    spec:
      serviceAccountName: document-analyzer-sa
      containers:
      - name: agent
        image: registry.platform.io/agents/document-analyzer:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        resources:
          requests:
            cpu: 1
            memory: 2Gi
          limits:
            cpu: 4
            memory: 8Gi
        env:
        - name: AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-model-secrets
              key: openai-api-key
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: postgres-url
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: model-cache
          mountPath: /app/model_cache
      volumes:
      - name: config
        configMap:
          name: document-analyzer-config
      - name: model-cache
        emptyDir:
          sizeLimit: 5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: document-analyzer-service
  namespace: agents
spec:
  selector:
    app: document-analyzer
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: document-analyzer-hpa
  namespace: agents
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: document-analyzer-agent
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Auto-Scaling Configuration

```python
# auto_scaler.py
class AgentAutoScaler:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_collector = MetricsCollector()
        self.k8s_client = KubernetesClient()
        
    async def evaluate_scaling_needs(self, agent_id: str) -> Dict[str, Any]:
        """Evaluate if agent needs scaling up or down"""
        metrics = await self.metrics_collector.get_agent_metrics(agent_id)
        
        current_replicas = await self.k8s_client.get_replica_count(agent_id)
        
        # Calculate scaling decision
        scale_up_threshold = self.config.get('scale_up_cpu_threshold', 70)
        scale_down_threshold = self.config.get('scale_down_cpu_threshold', 30)
        
        avg_cpu = metrics.get('avg_cpu_utilization', 0)
        avg_memory = metrics.get('avg_memory_utilization', 0)
        queue_depth = metrics.get('message_queue_depth', 0)
        
        decision = self._make_scaling_decision(
            current_replicas, avg_cpu, avg_memory, queue_depth,
            scale_up_threshold, scale_down_threshold
        )
        
        return {
            'agent_id': agent_id,
            'current_replicas': current_replicas,
            'recommended_replicas': decision['target_replicas'],
            'reason': decision['reason'],
            'metrics': metrics
        }
    
    def _make_scaling_decision(self, current_replicas: int, cpu: float, 
                             memory: float, queue_depth: int,
                             scale_up_threshold: float, scale_down_threshold: float) -> Dict[str, Any]:
        """Make scaling decision based on metrics"""
        max_replicas = self.config.get('max_replicas', 20)
        min_replicas = self.config.get('min_replicas', 2)
        
        if cpu > scale_up_threshold or memory > 85 or queue_depth > 100:
            target_replicas = min(current_replicas * 2, max_replicas)
            return {
                'target_replicas': target_replicas,
                'reason': f'High resource usage: CPU={cpu}%, Memory={memory}%, Queue={queue_depth}'
            }
        elif cpu < scale_down_threshold and memory < 50 and queue_depth < 10:
            target_replicas = max(current_replicas // 2, min_replicas)
            return {
                'target_replicas': target_replicas,
                'reason': f'Low resource usage: CPU={cpu}%, Memory={memory}%, Queue={queue_depth}'
            }
        else:
            return {
                'target_replicas': current_replicas,
                'reason': 'No scaling needed'
            }
```

## Best Practices

### 1. Design Principles

#### Single Responsibility Principle
Each agent should have a clear, focused purpose:

```python
# Good: Focused agent
class EmailProcessorAgent(AgentBase):
    """Processes incoming emails and extracts relevant information"""
    
    def get_capabilities(self):
        return ['email-parsing', 'attachment-extraction', 'spam-detection']

# Avoid: Do-everything agent
class UniversalProcessorAgent(AgentBase):
    """Processes emails, documents, images, videos, and performs ML inference"""
    # This agent tries to do too much
```

#### Idempotency
Ensure agent operations are idempotent:

```python
async def process_document(self, document_id: str, content: str) -> Response:
    """Process document idempotently"""
    # Check if already processed
    existing_result = await self.get_existing_result(document_id)
    if existing_result:
        return Response.success(existing_result)
    
    # Process the document
    result = await self._perform_processing(content)
    
    # Store result with unique key
    await self.store_result(document_id, result)
    
    return Response.success(result)
```

### 2. Error Handling

#### Graceful Degradation

```python
class RobustAgent(AgentBase):
    async def process_with_fallback(self, content: Dict[str, Any]) -> Response:
        """Process with multiple fallback strategies"""
        try:
            # Primary processing method
            return await self.primary_processor(content)
        except AIModelUnavailableError:
            # Fallback to alternative AI model
            return await self.fallback_ai_processor(content)
        except DatabaseUnavailableError:
            # Cache result for later processing
            await self.cache_for_retry(content)
            return Response.partial_success("Cached for later processing")
        except Exception as e:
            # Last resort: basic processing
            logging.error(f"All processing methods failed: {e}")
            return await self.basic_processor(content)
```

#### Circuit Breaker Pattern

```python
class CircuitBreakerAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60,
            expected_exception=ExternalServiceError
        )
    
    async def call_external_service(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Call external service with circuit breaker protection"""
        try:
            return await self.circuit_breaker.call(
                self.external_service.process, data
            )
        except CircuitBreakerOpenError:
            # Service is down, use cached data or alternative
            return await self.get_cached_response(data)
```

### 3. Performance Optimization

#### Connection Pooling

```python
class OptimizedAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.db_pool = self._create_db_pool()
        self.http_session = self._create_http_session()
    
    def _create_db_pool(self):
        return asyncpg.create_pool(
            host=self.config['db_host'],
            port=self.config['db_port'],
            user=self.config['db_user'],
            password=self.config['db_password'],
            database=self.config['db_name'],
            min_size=5,
            max_size=20,
            command_timeout=60
        )
    
    def _create_http_session(self):
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        return aiohttp.ClientSession(connector=connector)
```

#### Caching Strategy

```python
class CachedAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.cache = Redis(host=config['redis_host'])
        self.cache_ttl = config.get('cache_ttl', 3600)
    
    async def expensive_operation(self, input_data: str) -> Dict[str, Any]:
        """Perform expensive operation with caching"""
        cache_key = f"expensive_op:{hashlib.md5(input_data.encode()).hexdigest()}"
        
        # Try cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result:
            return json.loads(cached_result)
        
        # Perform expensive operation
        result = await self._do_expensive_operation(input_data)
        
        # Cache the result
        await self.cache.setex(
            cache_key, 
            self.cache_ttl, 
            json.dumps(result)
        )
        
        return result
```

### 4. Security Best Practices

#### Input Validation

```python
from pydantic import BaseModel, validator
from typing import List, Optional

class DocumentRequest(BaseModel):
    document_id: str
    text: str
    metadata: Optional[Dict[str, Any]] = {}
    
    @validator('document_id')
    def validate_document_id(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Invalid document ID format')
        return v
    
    @validator('text')
    def validate_text_length(cls, v):
        if len(v) > 1000000:  # 1MB limit
            raise ValueError('Text content too large')
        return v

class SecureAgent(AgentBase):
    async def process_document(self, content: Dict[str, Any]) -> Response:
        """Process document with input validation"""
        try:
            # Validate input
            request = DocumentRequest(**content)
            
            # Sanitize input
            sanitized_text = self.sanitize_text(request.text)
            
            # Process with validated and sanitized input
            result = await self._process_validated_document(
                request.document_id, 
                sanitized_text, 
                request.metadata
            )
            
            return Response.success(result)
            
        except ValidationError as e:
            return Response.error(f"Invalid input: {e}")
```

#### Secret Management

```python
class SecureAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.vault_client = VaultClient(config['vault_url'])
        
    async def get_secret(self, secret_path: str) -> str:
        """Retrieve secret from secure vault"""
        try:
            secret = await self.vault_client.get_secret(secret_path)
            return secret['value']
        except VaultError as e:
            logging.error(f"Failed to retrieve secret {secret_path}: {e}")
            raise SecurityError("Secret retrieval failed")
    
    async def initialize_ai_client(self):
        """Initialize AI client with secret management"""
        api_key = await self.get_secret('ai_models/openai_api_key')
        self.ai_client = OpenAIClient(api_key=api_key)
```

### 5. Monitoring and Observability

#### Comprehensive Logging

```python
import structlog
from platform_sdk.monitoring import MetricsCollector

class ObservableAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = structlog.get_logger(__name__)
        self.metrics = MetricsCollector()
        
    async def process_message(self, message: Message) -> Response:
        """Process message with comprehensive observability"""
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Log request start
        self.logger.info(
            "Processing message",
            request_id=request_id,
            message_type=message.get_type(),
            agent_id=self.agent_id
        )
        
        try:
            # Process the message
            response = await self._process_message_internal(message)
            
            # Record success metrics
            processing_time = time.time() - start_time
            self.metrics.histogram('message_processing_duration', processing_time)
            self.metrics.counter('messages_processed_total').inc()
            
            self.logger.info(
                "Message processed successfully",
                request_id=request_id,
                processing_time=processing_time,
                response_size=len(str(response))
            )
            
            return response
            
        except Exception as e:
            # Record error metrics
            processing_time = time.time() - start_time
            self.metrics.counter('messages_failed_total').inc()
            
            self.logger.error(
                "Message processing failed",
                request_id=request_id,
                error=str(e),
                error_type=type(e).__name__,
                processing_time=processing_time
            )
            
            return Response.error(str(e))
```

#### Health Monitoring

```python
class HealthMonitoredAgent(AgentBase):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.health_checks = [
            self.check_database_connection,
            self.check_ai_model_availability,
            self.check_memory_usage,
            self.check_message_queue_health
        ]
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_results = {}
        overall_healthy = True
        
        for check in self.health_checks:
            try:
                result = await check()
                health_results[check.__name__] = result
                if not result.get('healthy', False):
                    overall_healthy = False
            except Exception as e:
                health_results[check.__name__] = {
                    'healthy': False,
                    'error': str(e)
                }
                overall_healthy = False
        
        return {
            'overall_healthy': overall_healthy,
            'checks': health_results,
            'timestamp': datetime.utcnow().isoformat(),
            'agent_id': self.agent_id,
            'uptime_seconds': self.get_uptime_seconds()
        }
    
    async def check_database_connection(self) -> Dict[str, Any]:
        """Check database connectivity"""
        try:
            start_time = time.time()
            await self.db_pool.fetch("SELECT 1")
            response_time = time.time() - start_time
            
            return {
                'healthy': True,
                'response_time_ms': response_time * 1000,
                'connection_pool_size': len(self.db_pool._con_holders)
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }
```

## Conclusion

Creating effective agents on the AI-Native Agent Platform requires understanding both the technical capabilities and the design principles that make agents intelligent and reliable. By following the patterns and practices outlined in this guide, you can build agents that are:

- **Intelligent**: Capable of autonomous decision-making and learning
- **Reliable**: Robust error handling and graceful degradation
- **Scalable**: Efficient resource usage and horizontal scaling
- **Secure**: Proper input validation and secret management
- **Observable**: Comprehensive monitoring and logging

Remember to start simple, test thoroughly, and iterate based on real-world usage patterns. The platform provides powerful tools and abstractions, but the key to success is thoughtful design and implementation.

## Next Steps

1. **Practice**: Build several agents using different templates
2. **Experiment**: Try different AI model integrations
3. **Optimize**: Profile and optimize your agents for performance
4. **Scale**: Deploy agents in production and monitor their behavior
5. **Learn**: Study the platform's self-improving capabilities

For more advanced topics, see:
- [Workflow Design Tutorial](workflow-design.md)
- [AI Model Integration Guide](../api/ai-models.md)
- [Deployment Guide](../deployment/)
- [Architecture Documentation](../architecture/)