{"metadata": {"name": "AI-Native Agent Platform Dependency Graph", "version": "1.0.0", "description": "Complete dependency mapping for the AI-Native Agent Platform", "generated_at": "2024-01-15T00:00:00Z", "graph_type": "directed_acyclic_graph"}, "nodes": {"shared_common": {"id": "shared_common", "name": "Shared Common Libraries", "type": "library", "layer": 1, "languages": ["go", "java", "python", "javascript"], "description": "Common utilities and shared functionality", "location": "shared/common/", "build_system": "bazel", "dependencies": [], "provides": ["authentication utilities", "configuration management", "database utilities", "HTTP utilities", "logging framework", "metrics collection", "distributed tracing", "cryptographic functions"]}, "shared_proto": {"id": "shared_proto", "name": "Protocol Buffer Definitions", "type": "schema", "layer": 1, "languages": ["proto"], "description": "Shared protocol buffer definitions for inter-service communication", "location": "shared/proto/", "build_system": "bazel", "dependencies": [], "provides": ["gRPC service definitions", "message schemas", "API contracts"]}, "infrastructure_k8s": {"id": "infrastructure_k8s", "name": "Kubernetes Infrastructure", "type": "infrastructure", "layer": 0, "languages": ["yaml"], "description": "Kubernetes manifests and configurations", "location": "infrastructure/kubernetes/", "build_system": "kustomize", "dependencies": [], "provides": ["kubernetes manifests", "network policies", "RBAC configurations", "storage configurations"]}, "infrastructure_helm": {"id": "infrastructure_helm", "name": "<PERSON><PERSON>", "type": "infrastructure", "layer": 0, "languages": ["yaml"], "description": "Helm charts for deployment automation", "location": "infrastructure/helm-charts/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["deployment automation", "configuration templating", "environment management"]}, "platform_auth": {"id": "platform_auth", "name": "Authentication Service", "type": "service", "layer": 2, "languages": ["go"], "description": "Authentication and authorization service", "location": "platform/auth-service/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto"], "provides": ["user authentication", "JWT token management", "RBAC enforcement", "OAuth integration"], "resources": {"cpu": "500m", "memory": "1Gi", "replicas": 3}, "endpoints": ["/api/v1/auth/login", "/api/v1/auth/logout", "/api/v1/auth/validate", "/api/v1/auth/refresh"]}, "platform_api_gateway": {"id": "platform_api_gateway", "name": "API Gateway", "type": "service", "layer": 2, "languages": ["go"], "description": "Central API gateway for request routing and cross-cutting concerns", "location": "platform/api-gateway/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth"], "provides": ["request routing", "rate limiting", "authentication middleware", "response caching", "load balancing"], "resources": {"cpu": "1", "memory": "2Gi", "replicas": 5}, "endpoints": ["/api/v1/*", "/health", "/metrics"]}, "platform_dashboard": {"id": "platform_dashboard", "name": "Dashboard", "type": "service", "layer": 2, "languages": ["typescript", "javascript"], "description": "Web-based user interface for platform management", "location": "platform/dashboard/", "build_system": "bazel", "dependencies": ["platform_api_gateway"], "provides": ["web interface", "agent management UI", "workflow designer", "monitoring dashboards", "user management"], "resources": {"cpu": "500m", "memory": "1Gi", "replicas": 3}}, "platform_workflow_engine": {"id": "platform_workflow_engine", "name": "Workflow Engine", "type": "service", "layer": 2, "languages": ["java"], "description": "Workflow definition and validation service", "location": "platform/workflow-engine/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth"], "provides": ["workflow definition", "workflow validation", "workflow versioning", "template management"], "resources": {"cpu": "1", "memory": "2Gi", "replicas": 3}, "endpoints": ["/api/v1/workflows", "/api/v1/workflows/{id}", "/api/v1/templates"]}, "platform_data_service": {"id": "platform_data_service", "name": "Data Service", "type": "service", "layer": 2, "languages": ["python"], "description": "Data management and persistence service", "location": "platform/data-service/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth"], "provides": ["data persistence", "data validation", "data transformation", "backup management"], "resources": {"cpu": "1", "memory": "4Gi", "replicas": 3}, "endpoints": ["/api/v1/data", "/api/v1/data/{dataset}", "/api/v1/backup"]}, "meta_agent_factory": {"id": "meta_agent_factory", "name": "Agent Factory", "type": "meta_agent", "layer": 3, "languages": ["java"], "description": "Agent creation, templating, and deployment management", "location": "meta-agents/agent-factory/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth", "platform_api_gateway", "platform_workflow_engine"], "provides": ["agent creation", "template management", "deployment orchestration", "resource calculation", "lifecycle management"], "resources": {"cpu": "2", "memory": "4Gi", "replicas": 3}, "endpoints": ["/api/v1/agents", "/api/v1/agents/{id}", "/api/v1/templates", "/api/v1/deployments"]}, "meta_task_orchestrator": {"id": "meta_task_orchestrator", "name": "Task Orchestrator", "type": "meta_agent", "layer": 3, "languages": ["go"], "description": "Workflow execution and task scheduling orchestration", "location": "meta-agents/task-orchestrator/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth", "platform_workflow_engine", "meta_agent_factory"], "provides": ["workflow execution", "task scheduling", "dependency resolution", "state management", "event handling"], "resources": {"cpu": "4", "memory": "8Gi", "replicas": 5}, "endpoints": ["/api/v1/executions", "/api/v1/executions/{id}", "/api/v1/tasks", "/api/v1/events"]}, "meta_resource_manager": {"id": "meta_resource_manager", "name": "Resource Manager", "type": "meta_agent", "layer": 3, "languages": ["python"], "description": "Resource allocation, scaling, and optimization management", "location": "meta-agents/resource-manager/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth", "meta_agent_factory", "meta_task_orchestrator"], "provides": ["resource allocation", "auto-scaling", "resource optimization", "metrics collection", "policy enforcement"], "resources": {"cpu": "2", "memory": "4Gi", "replicas": 3}, "endpoints": ["/api/v1/resources", "/api/v1/scaling", "/api/v1/metrics", "/api/v1/policies"]}, "meta_communication_broker": {"id": "meta_communication_broker", "name": "Communication Broker", "type": "meta_agent", "layer": 3, "languages": ["go"], "description": "Inter-agent communication and message routing", "location": "meta-agents/communication-broker/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth", "meta_agent_factory"], "provides": ["message routing", "service discovery", "protocol translation", "message queuing", "event streaming"], "resources": {"cpu": "2", "memory": "4Gi", "replicas": 5}, "endpoints": ["/api/v1/messages", "/api/v1/discovery", "/api/v1/routing"]}, "meta_lifecycle_manager": {"id": "meta_lifecycle_manager", "name": "Lifecycle Manager", "type": "meta_agent", "layer": 3, "languages": ["java"], "description": "Agent lifecycle management and health monitoring", "location": "meta-agents/lifecycle-manager/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "platform_auth", "meta_agent_factory", "meta_resource_manager"], "provides": ["lifecycle management", "health monitoring", "upgrade management", "rollback capabilities", "maintenance scheduling"], "resources": {"cpu": "1", "memory": "2Gi", "replicas": 3}, "endpoints": ["/api/v1/lifecycle", "/api/v1/health", "/api/v1/maintenance"]}, "agent_runtime_python": {"id": "agent_runtime_python", "name": "Python Agent Runtime", "type": "runtime", "layer": 4, "languages": ["python"], "description": "Runtime environment for Python-based agents", "location": "agents/runtime/python/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "meta_communication_broker", "meta_lifecycle_manager"], "provides": ["agent runtime", "lifecycle management", "communication layer", "health monitoring", "metrics reporting"], "resources": {"cpu": "500m", "memory": "1Gi", "replicas": "variable"}}, "agent_runtime_java": {"id": "agent_runtime_java", "name": "Java Agent Runtime", "type": "runtime", "layer": 4, "languages": ["java"], "description": "Runtime environment for Java-based agents", "location": "agents/runtime/java/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "meta_communication_broker", "meta_lifecycle_manager"], "provides": ["agent runtime", "lifecycle management", "communication layer", "health monitoring", "metrics reporting"], "resources": {"cpu": "500m", "memory": "2Gi", "replicas": "variable"}}, "agent_runtime_go": {"id": "agent_runtime_go", "name": "Go Agent Runtime", "type": "runtime", "layer": 4, "languages": ["go"], "description": "Runtime environment for Go-based agents", "location": "agents/runtime/go/", "build_system": "bazel", "dependencies": ["shared_common", "shared_proto", "meta_communication_broker", "meta_lifecycle_manager"], "provides": ["agent runtime", "lifecycle management", "communication layer", "health monitoring", "metrics reporting"], "resources": {"cpu": "250m", "memory": "512Mi", "replicas": "variable"}}, "agent_template_python": {"id": "agent_template_python", "name": "Python Agent Templates", "type": "template", "layer": 5, "languages": ["python"], "description": "Reusable templates for Python agents", "location": "agents/templates/python/", "build_system": "bazel", "dependencies": ["agent_runtime_python", "shared_common"], "provides": ["base agent classes", "common handlers", "utility functions", "configuration templates"]}, "agent_template_java": {"id": "agent_template_java", "name": "Java Agent Templates", "type": "template", "layer": 5, "languages": ["java"], "description": "Reusable templates for Java agents", "location": "agents/templates/java/", "build_system": "bazel", "dependencies": ["agent_runtime_java", "shared_common"], "provides": ["base agent classes", "common handlers", "utility functions", "configuration templates"]}, "agent_template_go": {"id": "agent_template_go", "name": "Go Agent Templates", "type": "template", "layer": 5, "languages": ["go"], "description": "Reusable templates for Go agents", "location": "agents/templates/go/", "build_system": "bazel", "dependencies": ["agent_runtime_go", "shared_common"], "provides": ["base agent classes", "common handlers", "utility functions", "configuration templates"]}, "monitoring_prometheus": {"id": "monitoring_prometheus", "name": "Prometheus Monitoring", "type": "monitoring", "layer": 1, "languages": ["yaml"], "description": "Prometheus monitoring and metrics collection", "location": "infrastructure/monitoring/prometheus/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["metrics collection", "alerting", "time series database", "service discovery"], "resources": {"cpu": "2", "memory": "8Gi", "replicas": 1}}, "monitoring_grafana": {"id": "monitoring_grafana", "name": "Grafana Dashboards", "type": "monitoring", "layer": 1, "languages": ["yaml", "json"], "description": "Grafana dashboards and visualization", "location": "infrastructure/monitoring/grafana/", "build_system": "helm", "dependencies": ["monitoring_prometheus"], "provides": ["data visualization", "dashboards", "alerts visualization", "reporting"], "resources": {"cpu": "500m", "memory": "2Gi", "replicas": 1}}, "monitoring_jaeger": {"id": "monitoring_jaeger", "name": "Jaeger Tracing", "type": "monitoring", "layer": 1, "languages": ["yaml"], "description": "Distributed tracing with <PERSON><PERSON><PERSON>", "location": "infrastructure/monitoring/jaeger/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["distributed tracing", "trace collection", "trace analysis", "performance monitoring"], "resources": {"cpu": "1", "memory": "4Gi", "replicas": 1}}, "data_postgresql": {"id": "data_postgresql", "name": "PostgreSQL Database", "type": "data", "layer": 1, "languages": ["sql"], "description": "Primary relational database", "location": "infrastructure/data/postgresql/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["relational data storage", "ACID transactions", "data consistency", "backup and recovery"], "resources": {"cpu": "2", "memory": "8Gi", "storage": "100Gi", "replicas": 3}}, "data_redis": {"id": "data_redis", "name": "<PERSON><PERSON>", "type": "data", "layer": 1, "languages": ["redis"], "description": "In-memory cache and session store", "location": "infrastructure/data/redis/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["caching", "session storage", "pub/sub messaging", "temporary data storage"], "resources": {"cpu": "500m", "memory": "4Gi", "replicas": 3}}, "data_elasticsearch": {"id": "data_elasticsearch", "name": "Elasticsearch", "type": "data", "layer": 1, "languages": ["elasticsearch"], "description": "Search and analytics engine", "location": "infrastructure/data/elasticsearch/", "build_system": "helm", "dependencies": ["infrastructure_k8s"], "provides": ["full-text search", "log aggregation", "analytics", "data indexing"], "resources": {"cpu": "2", "memory": "8Gi", "storage": "500Gi", "replicas": 3}}}, "edges": [{"from": "shared_common", "to": "platform_auth", "type": "dependency", "description": "Authentication service uses common utilities"}, {"from": "shared_proto", "to": "platform_auth", "type": "dependency", "description": "Authentication service uses protocol definitions"}, {"from": "shared_common", "to": "platform_api_gateway", "type": "dependency", "description": "API Gateway uses common utilities"}, {"from": "shared_proto", "to": "platform_api_gateway", "type": "dependency", "description": "API Gateway uses protocol definitions"}, {"from": "platform_auth", "to": "platform_api_gateway", "type": "dependency", "description": "API Gateway integrates with authentication"}, {"from": "platform_api_gateway", "to": "platform_dashboard", "type": "dependency", "description": "Dashboard communicates through API Gateway"}, {"from": "shared_common", "to": "platform_workflow_engine", "type": "dependency", "description": "Workflow engine uses common utilities"}, {"from": "shared_proto", "to": "platform_workflow_engine", "type": "dependency", "description": "Workflow engine uses protocol definitions"}, {"from": "platform_auth", "to": "platform_workflow_engine", "type": "dependency", "description": "Workflow engine requires authentication"}, {"from": "shared_common", "to": "platform_data_service", "type": "dependency", "description": "Data service uses common utilities"}, {"from": "shared_proto", "to": "platform_data_service", "type": "dependency", "description": "Data service uses protocol definitions"}, {"from": "platform_auth", "to": "platform_data_service", "type": "dependency", "description": "Data service requires authentication"}, {"from": "shared_common", "to": "meta_agent_factory", "type": "dependency", "description": "Agent Factory uses common utilities"}, {"from": "shared_proto", "to": "meta_agent_factory", "type": "dependency", "description": "Agent Factory uses protocol definitions"}, {"from": "platform_auth", "to": "meta_agent_factory", "type": "dependency", "description": "Agent Factory requires authentication"}, {"from": "platform_api_gateway", "to": "meta_agent_factory", "type": "dependency", "description": "Agent Factory communicates through API Gateway"}, {"from": "platform_workflow_engine", "to": "meta_agent_factory", "type": "dependency", "description": "Agent Factory integrates with workflow engine"}, {"from": "shared_common", "to": "meta_task_orchestrator", "type": "dependency", "description": "Task Orchestrator uses common utilities"}, {"from": "shared_proto", "to": "meta_task_orchestrator", "type": "dependency", "description": "Task Orchestrator uses protocol definitions"}, {"from": "platform_auth", "to": "meta_task_orchestrator", "type": "dependency", "description": "Task Orchestrator requires authentication"}, {"from": "platform_workflow_engine", "to": "meta_task_orchestrator", "type": "dependency", "description": "Task Orchestrator executes workflows"}, {"from": "meta_agent_factory", "to": "meta_task_orchestrator", "type": "dependency", "description": "Task Orchestrator manages agent tasks"}, {"from": "shared_common", "to": "meta_resource_manager", "type": "dependency", "description": "Resource Manager uses common utilities"}, {"from": "shared_proto", "to": "meta_resource_manager", "type": "dependency", "description": "Resource Manager uses protocol definitions"}, {"from": "platform_auth", "to": "meta_resource_manager", "type": "dependency", "description": "Resource Manager requires authentication"}, {"from": "meta_agent_factory", "to": "meta_resource_manager", "type": "dependency", "description": "Resource Manager allocates resources for agents"}, {"from": "meta_task_orchestrator", "to": "meta_resource_manager", "type": "dependency", "description": "Resource Manager manages orchestrator resources"}, {"from": "shared_common", "to": "meta_communication_broker", "type": "dependency", "description": "Communication Broker uses common utilities"}, {"from": "shared_proto", "to": "meta_communication_broker", "type": "dependency", "description": "Communication Broker uses protocol definitions"}, {"from": "platform_auth", "to": "meta_communication_broker", "type": "dependency", "description": "Communication Broker requires authentication"}, {"from": "meta_agent_factory", "to": "meta_communication_broker", "type": "dependency", "description": "Communication Broker facilitates agent communication"}, {"from": "shared_common", "to": "meta_lifecycle_manager", "type": "dependency", "description": "Lifecycle Manager uses common utilities"}, {"from": "shared_proto", "to": "meta_lifecycle_manager", "type": "dependency", "description": "Lifecycle Manager uses protocol definitions"}, {"from": "platform_auth", "to": "meta_lifecycle_manager", "type": "dependency", "description": "Lifecycle Manager requires authentication"}, {"from": "meta_agent_factory", "to": "meta_lifecycle_manager", "type": "dependency", "description": "Lifecycle Manager manages agent lifecycle"}, {"from": "meta_resource_manager", "to": "meta_lifecycle_manager", "type": "dependency", "description": "Lifecycle Manager coordinates with resource management"}, {"from": "shared_common", "to": "agent_runtime_python", "type": "dependency", "description": "Python runtime uses common utilities"}, {"from": "shared_proto", "to": "agent_runtime_python", "type": "dependency", "description": "Python runtime uses protocol definitions"}, {"from": "meta_communication_broker", "to": "agent_runtime_python", "type": "dependency", "description": "Python runtime uses communication broker"}, {"from": "meta_lifecycle_manager", "to": "agent_runtime_python", "type": "dependency", "description": "Python runtime managed by lifecycle manager"}, {"from": "shared_common", "to": "agent_runtime_java", "type": "dependency", "description": "Java runtime uses common utilities"}, {"from": "shared_proto", "to": "agent_runtime_java", "type": "dependency", "description": "Java runtime uses protocol definitions"}, {"from": "meta_communication_broker", "to": "agent_runtime_java", "type": "dependency", "description": "Java runtime uses communication broker"}, {"from": "meta_lifecycle_manager", "to": "agent_runtime_java", "type": "dependency", "description": "Java runtime managed by lifecycle manager"}, {"from": "shared_common", "to": "agent_runtime_go", "type": "dependency", "description": "Go runtime uses common utilities"}, {"from": "shared_proto", "to": "agent_runtime_go", "type": "dependency", "description": "Go runtime uses protocol definitions"}, {"from": "meta_communication_broker", "to": "agent_runtime_go", "type": "dependency", "description": "Go runtime uses communication broker"}, {"from": "meta_lifecycle_manager", "to": "agent_runtime_go", "type": "dependency", "description": "Go runtime managed by lifecycle manager"}, {"from": "agent_runtime_python", "to": "agent_template_python", "type": "dependency", "description": "Python templates use Python runtime"}, {"from": "shared_common", "to": "agent_template_python", "type": "dependency", "description": "Python templates use common utilities"}, {"from": "agent_runtime_java", "to": "agent_template_java", "type": "dependency", "description": "Java templates use Java runtime"}, {"from": "shared_common", "to": "agent_template_java", "type": "dependency", "description": "Java templates use common utilities"}, {"from": "agent_runtime_go", "to": "agent_template_go", "type": "dependency", "description": "Go templates use Go runtime"}, {"from": "shared_common", "to": "agent_template_go", "type": "dependency", "description": "Go templates use common utilities"}, {"from": "infrastructure_k8s", "to": "infrastructure_helm", "type": "dependency", "description": "Helm charts build on Kubernetes manifests"}, {"from": "infrastructure_k8s", "to": "monitoring_prometheus", "type": "dependency", "description": "Prometheus deployed on Kubernetes"}, {"from": "monitoring_prometheus", "to": "monitoring_grafana", "type": "dependency", "description": "Grafana uses Prometheus as data source"}, {"from": "infrastructure_k8s", "to": "monitoring_jaeger", "type": "dependency", "description": "Jaeger deployed on Kubernetes"}, {"from": "infrastructure_k8s", "to": "data_postgresql", "type": "dependency", "description": "PostgreSQL deployed on Kubernetes"}, {"from": "infrastructure_k8s", "to": "data_redis", "type": "dependency", "description": "Redis deployed on Kubernetes"}, {"from": "infrastructure_k8s", "to": "data_elasticsearch", "type": "dependency", "description": "Elasticsearch deployed on Kubernetes"}], "layers": {"0": {"name": "Infrastructure Layer", "description": "Base infrastructure components", "components": ["infrastructure_k8s", "infrastructure_helm"]}, "1": {"name": "Data and Monitoring Layer", "description": "Data storage and monitoring components", "components": ["shared_common", "shared_proto", "data_postgresql", "data_redis", "data_elasticsearch", "monitoring_prometheus", "monitoring_grafana", "monitoring_jaeger"]}, "2": {"name": "Platform Services Layer", "description": "Core platform services", "components": ["platform_auth", "platform_api_gateway", "platform_dashboard", "platform_workflow_engine", "platform_data_service"]}, "3": {"name": "Meta-Agent <PERSON><PERSON>", "description": "Platform meta-agents for management and orchestration", "components": ["meta_agent_factory", "meta_task_orchestrator", "meta_resource_manager", "meta_communication_broker", "meta_lifecycle_manager"]}, "4": {"name": "Agent Runtime Layer", "description": "Runtime environments for different languages", "components": ["agent_runtime_python", "agent_runtime_java", "agent_runtime_go"]}, "5": {"name": "Agent Template <PERSON><PERSON>", "description": "Reusable agent templates and SDKs", "components": ["agent_template_python", "agent_template_java", "agent_template_go"]}}, "build_graph": {"description": "Build order and dependencies for the entire platform", "build_order": [["shared_common", "shared_proto", "infrastructure_k8s"], ["infrastructure_helm", "data_postgresql", "data_redis", "data_elasticsearch", "monitoring_prometheus", "monitoring_jaeger"], ["monitoring_grafana", "platform_auth"], ["platform_api_gateway", "platform_workflow_engine", "platform_data_service"], ["platform_dashboard", "meta_agent_factory", "meta_communication_broker"], ["meta_task_orchestrator", "meta_resource_manager", "meta_lifecycle_manager"], ["agent_runtime_python", "agent_runtime_java", "agent_runtime_go"], ["agent_template_python", "agent_template_java", "agent_template_go"]]}, "deployment_dependencies": {"description": "Runtime deployment dependencies", "critical_path": ["infrastructure_k8s", "data_postgresql", "data_redis", "platform_auth", "platform_api_gateway", "meta_agent_factory", "meta_task_orchestrator", "agent_runtime_python"], "optional_components": ["monitoring_grafana", "monitoring_jaeger", "data_elasticsearch", "platform_dashboard"]}}