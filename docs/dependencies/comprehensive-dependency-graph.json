{"metadata": {"name": "ai-native-agent-platform-dependency-graph", "version": "1.0.0", "description": "Complete dependency graph for AI-Native Agent Platform", "generated": "2025-07-12", "types": {"dependencies": ["build_dependency", "runtime_dependency", "compile_dependency", "test_dependency", "deployment_dependency", "communication_dependency", "data_dependency", "configuration_dependency"], "relationships": ["depends_on", "provides_to", "communicates_with", "deploys_with", "inherits_from", "configures", "monitors", "generates", "validates"]}}, "nodes": {"root": {"id": "ai-native-agent-platform", "type": "project_root", "path": "/", "description": "Root project node", "build_system": "bazel", "languages": ["java", "python", "go", "typescript"]}, "build_system": {"workspace": {"id": "workspace", "type": "build_config", "path": "/WORKSPACE", "description": "Bazel workspace configuration"}, "root_build": {"id": "root_build", "type": "build_config", "path": "/BUILD", "description": "Root BUILD file"}, "bazelrc": {"id": "bazelrc", "type": "build_config", "path": "/.bazelrc", "description": "Bazel configuration"}}, "frontend_layer": {"web_ui": {"id": "web_ui", "type": "frontend_service", "path": "/src/frontend/web-ui", "language": "typescript", "framework": "react", "description": "Main web UI application", "ports": [3000], "environment": "browser"}, "mobile_app": {"id": "mobile_app", "type": "frontend_service", "path": "/src/frontend/mobile-app", "language": "typescript", "framework": "react-native", "description": "Mobile application", "environment": "mobile"}, "workflow_designer": {"id": "workflow_designer", "type": "frontend_service", "path": "/src/frontend/workflow-designer", "language": "typescript", "framework": "n8n", "description": "Visual workflow designer", "ports": [5678], "environment": "browser"}}, "meta_agents": {"agent_factory": {"id": "agent_factory", "type": "meta_agent_service", "path": "/src/meta-agents/agent-factory", "language": "java", "framework": "spring_boot", "description": "Dynamic agent generation service", "ports": [8080], "database": "postgresql", "capabilities": ["agent_generation", "code_generation", "testing", "deployment"]}, "task_orchestrator": {"id": "task_orchestrator", "type": "meta_agent_service", "path": "/src/meta-agents/task-orchestrator", "language": "java", "framework": "spring_boot", "description": "Workflow and task management", "ports": [8081], "database": "postgresql", "capabilities": ["workflow_execution", "task_distribution", "scheduling"]}, "resource_manager": {"id": "resource_manager", "type": "meta_agent_service", "path": "/src/meta-agents/resource-manager", "language": "go", "description": "Resource allocation and management", "ports": [8082], "database": "postgresql", "capabilities": ["resource_allocation", "load_balancing", "auto_scaling"]}, "communication_broker": {"id": "communication_broker", "type": "meta_agent_service", "path": "/src/meta-agents/communication-broker", "language": "go", "description": "Agent-to-agent communication", "ports": [8083, 9083], "protocols": ["a2a", "websocket", "grpc"], "capabilities": ["message_routing", "protocol_translation", "load_balancing"]}, "security_monitor": {"id": "security_monitor", "type": "meta_agent_service", "path": "/src/meta-agents/security-monitor", "language": "go", "description": "Security monitoring and enforcement", "ports": [8084], "capabilities": ["threat_detection", "policy_enforcement", "audit_logging"]}, "knowledge_base": {"id": "knowledge_base", "type": "meta_agent_service", "path": "/src/meta-agents/knowledge-base", "language": "python", "framework": "<PERSON><PERSON><PERSON>", "description": "AI-powered knowledge management", "ports": [8085], "database": "postgresql", "vector_db": "pgvector", "capabilities": ["knowledge_storage", "semantic_search", "learning"]}, "discovery_registry": {"id": "discovery_registry", "type": "meta_agent_service", "path": "/src/meta-agents/discovery-registry", "language": "go", "description": "Service discovery and agent registry", "ports": [8086], "database": "etcd", "capabilities": ["service_discovery", "health_monitoring", "capability_matching"]}}, "agent_runtimes": {"java_agent_common": {"id": "java_agent_common", "type": "agent_library", "path": "/src/agents/java/common", "language": "java", "description": "Shared Java agent framework", "artifact_type": "library"}, "python_agent_common": {"id": "python_agent_common", "type": "agent_library", "path": "/src/agents/python/common", "language": "python", "description": "Shared Python agent framework", "artifact_type": "library"}, "go_agent_common": {"id": "go_agent_common", "type": "agent_library", "path": "/src/agents/go/common", "language": "go", "description": "Shared Go agent framework", "artifact_type": "library"}, "java_enterprise_agents": {"id": "java_enterprise_agents", "type": "agent_group", "path": "/src/agents/java/enterprise-integration", "language": "java", "description": "Enterprise integration agents"}, "python_ml_agents": {"id": "python_ml_agents", "type": "agent_group", "path": "/src/agents/python/ml-agents", "language": "python", "description": "Machine learning agents"}, "go_system_agents": {"id": "go_system_agents", "type": "agent_group", "path": "/src/agents/go/system-agents", "language": "go", "description": "System-level agents"}}, "ai_model_layer": {"ai_model_router": {"id": "ai_model_router", "type": "ai_service", "path": "/src/ai-models/router", "language": "python", "framework": "<PERSON><PERSON><PERSON>", "description": "AI model routing and load balancing", "ports": [8090], "capabilities": ["model_routing", "load_balancing", "fallback_handling"]}, "gemini_provider": {"id": "gemini_provider", "type": "ai_provider", "path": "/src/ai-models/providers/gemini", "language": "python", "description": "Google Gemini API integration"}, "claude_provider": {"id": "claude_provider", "type": "ai_provider", "path": "/src/ai-models/providers/claude", "language": "python", "description": "Anthropic Claude API integration"}, "openai_provider": {"id": "openai_provider", "type": "ai_provider", "path": "/src/ai-models/providers/openai", "language": "python", "description": "OpenAI API integration"}}, "infrastructure_services": {"api_gateway": {"id": "api_gateway", "type": "infrastructure_service", "path": "/src/infrastructure/api-gateway", "language": "go", "description": "API gateway and routing", "ports": [8000, 8443], "capabilities": ["routing", "authentication", "rate_limiting", "ssl_termination"]}, "event_store": {"id": "event_store", "type": "infrastructure_service", "path": "/src/infrastructure/event-store", "language": "go", "description": "Event sourcing system", "ports": [8091], "database": "postgresql", "capabilities": ["event_storage", "event_streaming", "projections"]}, "mcp_servers": {"id": "mcp_servers", "type": "infrastructure_service", "path": "/src/infrastructure/mcp-servers", "language": "python", "description": "Model Context Protocol servers", "ports": [8092, 8093, 8094, 8095], "capabilities": ["context_management", "protocol_handling"]}}, "databases": {"postgresql": {"id": "postgresql", "type": "database", "description": "Primary relational database", "ports": [5432], "persistent": true}, "redis": {"id": "redis", "type": "cache_database", "description": "Cache and session storage", "ports": [6379], "persistent": false}, "etcd": {"id": "etcd", "type": "key_value_database", "description": "Distributed configuration storage", "ports": [2379, 2380], "persistent": true}}, "monitoring": {"prometheus": {"id": "prometheus", "type": "monitoring_service", "description": "Metrics collection and storage", "ports": [9090], "capabilities": ["metrics_collection", "alerting"]}, "grafana": {"id": "grafana", "type": "monitoring_service", "description": "Metrics visualization and dashboards", "ports": [3001], "capabilities": ["visualization", "dashboards", "alerting"]}, "jaeger": {"id": "jaeger", "type": "monitoring_service", "description": "Distributed tracing", "ports": [14268, 16686], "capabilities": ["distributed_tracing", "performance_monitoring"]}}, "external_services": {"google_adk": {"id": "google_adk", "type": "external_service", "description": "Google Agent Development Kit", "external": true}, "google_a2a": {"id": "google_a2a", "type": "external_protocol", "description": "Google Agent-to-Agent Protocol", "external": true}, "n8n_platform": {"id": "n8n_platform", "type": "external_service", "description": "n8n workflow automation platform", "external": true}}}, "dependencies": {"build_dependencies": [{"from": "root_build", "to": "workspace", "type": "build_dependency", "relationship": "depends_on", "description": "Root BUILD depends on WORKSPACE configuration"}, {"from": "web_ui", "to": "root_build", "type": "build_dependency", "relationship": "depends_on", "description": "Web UI build depends on root BUILD"}, {"from": "agent_factory", "to": "java_agent_common", "type": "build_dependency", "relationship": "depends_on", "description": "Agent Factory depends on Java agent common library"}, {"from": "java_enterprise_agents", "to": "java_agent_common", "type": "build_dependency", "relationship": "depends_on", "description": "Java enterprise agents depend on common library"}, {"from": "python_ml_agents", "to": "python_agent_common", "type": "build_dependency", "relationship": "depends_on", "description": "Python ML agents depend on common library"}, {"from": "go_system_agents", "to": "go_agent_common", "type": "build_dependency", "relationship": "depends_on", "description": "Go system agents depend on common library"}], "runtime_dependencies": [{"from": "web_ui", "to": "api_gateway", "type": "runtime_dependency", "relationship": "communicates_with", "protocol": "https", "description": "Web UI communicates with API gateway"}, {"from": "api_gateway", "to": "agent_factory", "type": "runtime_dependency", "relationship": "routes_to", "protocol": "http", "description": "API gateway routes requests to agent factory"}, {"from": "agent_factory", "to": "postgresql", "type": "runtime_dependency", "relationship": "depends_on", "protocol": "postgresql", "description": "Agent factory stores data in PostgreSQL"}, {"from": "agent_factory", "to": "communication_broker", "type": "runtime_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Agent factory communicates via broker"}, {"from": "task_orchestrator", "to": "communication_broker", "type": "runtime_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Task orchestrator communicates via broker"}, {"from": "resource_manager", "to": "communication_broker", "type": "runtime_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Resource manager communicates via broker"}, {"from": "knowledge_base", "to": "ai_model_router", "type": "runtime_dependency", "relationship": "depends_on", "protocol": "http", "description": "Knowledge base uses AI models via router"}, {"from": "ai_model_router", "to": "gemini_provider", "type": "runtime_dependency", "relationship": "routes_to", "protocol": "https", "description": "Router can route to Gemini provider"}, {"from": "ai_model_router", "to": "claude_provider", "type": "runtime_dependency", "relationship": "routes_to", "protocol": "https", "description": "Router can route to Claude provider"}, {"from": "ai_model_router", "to": "openai_provider", "type": "runtime_dependency", "relationship": "routes_to", "protocol": "https", "description": "Router can route to OpenAI provider"}, {"from": "discovery_registry", "to": "etcd", "type": "runtime_dependency", "relationship": "depends_on", "protocol": "etcd", "description": "Discovery registry stores data in etcd"}, {"from": "communication_broker", "to": "redis", "type": "runtime_dependency", "relationship": "depends_on", "protocol": "redis", "description": "Communication broker uses Redis for message queuing"}], "communication_dependencies": [{"from": "agent_factory", "to": "task_orchestrator", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "message_types": ["agent_created", "agent_deployment_request"], "description": "Agent factory notifies orchestrator of new agents"}, {"from": "task_orchestrator", "to": "resource_manager", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "message_types": ["resource_request", "task_allocation"], "description": "Orchestrator requests resources for tasks"}, {"from": "security_monitor", "to": "agent_factory", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "message_types": ["security_violation", "agent_suspension"], "description": "Security monitor can suspend agents"}, {"from": "java_enterprise_agents", "to": "communication_broker", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Java agents communicate via broker"}, {"from": "python_ml_agents", "to": "communication_broker", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Python agents communicate via broker"}, {"from": "go_system_agents", "to": "communication_broker", "type": "communication_dependency", "relationship": "communicates_with", "protocol": "a2a", "description": "Go agents communicate via broker"}], "data_dependencies": [{"from": "agent_factory", "to": "event_store", "type": "data_dependency", "relationship": "publishes_to", "event_types": ["agent_created", "agent_updated", "agent_deleted"], "description": "Agent factory publishes events to event store"}, {"from": "task_orchestrator", "to": "event_store", "type": "data_dependency", "relationship": "publishes_to", "event_types": ["workflow_started", "task_completed", "workflow_finished"], "description": "Task orchestrator publishes workflow events"}, {"from": "knowledge_base", "to": "event_store", "type": "data_dependency", "relationship": "subscribes_to", "event_types": ["agent_created", "workflow_finished", "learning_event"], "description": "Knowledge base learns from platform events"}, {"from": "security_monitor", "to": "event_store", "type": "data_dependency", "relationship": "subscribes_to", "event_types": ["*"], "description": "Security monitor audits all events"}], "monitoring_dependencies": [{"from": "prometheus", "to": "agent_factory", "type": "monitoring_dependency", "relationship": "monitors", "metrics": ["agent_creation_rate", "agent_success_rate", "response_time"], "description": "Prometheus monitors agent factory metrics"}, {"from": "prometheus", "to": "task_orchestrator", "type": "monitoring_dependency", "relationship": "monitors", "metrics": ["workflow_execution_time", "task_failure_rate", "queue_depth"], "description": "Prometheus monitors orchestrator metrics"}, {"from": "prometheus", "to": "resource_manager", "type": "monitoring_dependency", "relationship": "monitors", "metrics": ["cpu_utilization", "memory_usage", "allocation_efficiency"], "description": "Prometheus monitors resource usage"}, {"from": "prometheus", "to": "communication_broker", "type": "monitoring_dependency", "relationship": "monitors", "metrics": ["message_throughput", "latency", "connection_count"], "description": "Prometheus monitors communication metrics"}, {"from": "grafana", "to": "prometheus", "type": "monitoring_dependency", "relationship": "visualizes", "description": "Grafana visualizes Prometheus metrics"}, {"from": "jaeger", "to": "agent_factory", "type": "monitoring_dependency", "relationship": "traces", "description": "Jaeger traces agent factory requests"}, {"from": "jaeger", "to": "communication_broker", "type": "monitoring_dependency", "relationship": "traces", "description": "<PERSON><PERSON><PERSON> traces inter-agent communications"}], "deployment_dependencies": [{"from": "agent_factory", "to": "postgresql", "type": "deployment_dependency", "relationship": "requires", "description": "Agent factory requires PostgreSQL to be deployed first"}, {"from": "communication_broker", "to": "redis", "type": "deployment_dependency", "relationship": "requires", "description": "Communication broker requires Redis"}, {"from": "discovery_registry", "to": "etcd", "type": "deployment_dependency", "relationship": "requires", "description": "Discovery registry requires etcd"}, {"from": "web_ui", "to": "api_gateway", "type": "deployment_dependency", "relationship": "requires", "description": "Web UI requires API gateway"}, {"from": "api_gateway", "to": "agent_factory", "type": "deployment_dependency", "relationship": "requires", "description": "API gateway requires meta agents"}, {"from": "grafana", "to": "prometheus", "type": "deployment_dependency", "relationship": "requires", "description": "Grafana requires Prometheus"}], "configuration_dependencies": [{"from": "agent_factory", "to": "mcp_servers", "type": "configuration_dependency", "relationship": "configures", "description": "Agent factory configures MCP servers for new agents"}, {"from": "security_monitor", "to": "agent_factory", "type": "configuration_dependency", "relationship": "configures", "description": "Security monitor configures security policies for agents"}, {"from": "resource_manager", "to": "java_enterprise_agents", "type": "configuration_dependency", "relationship": "configures", "description": "Resource manager configures resource limits for Java agents"}, {"from": "resource_manager", "to": "python_ml_agents", "type": "configuration_dependency", "relationship": "configures", "description": "Resource manager configures resource limits for Python agents"}, {"from": "resource_manager", "to": "go_system_agents", "type": "configuration_dependency", "relationship": "configures", "description": "Resource manager configures resource limits for Go agents"}], "external_dependencies": [{"from": "communication_broker", "to": "google_a2a", "type": "external_dependency", "relationship": "implements", "description": "Communication broker implements Google A2A protocol"}, {"from": "agent_factory", "to": "google_adk", "type": "external_dependency", "relationship": "uses", "description": "Agent factory uses Google ADK for agent development"}, {"from": "workflow_designer", "to": "n8n_platform", "type": "external_dependency", "relationship": "extends", "description": "Workflow designer extends n8n platform"}, {"from": "gemini_provider", "to": "google_gemini_api", "type": "external_dependency", "relationship": "calls", "description": "Gemini provider calls Google Gemini API", "external_endpoint": "https://api.gemini.google.com"}, {"from": "claude_provider", "to": "anthropic_claude_api", "type": "external_dependency", "relationship": "calls", "description": "Claude provider calls Anthropic Claude API", "external_endpoint": "https://api.anthropic.com"}, {"from": "openai_provider", "to": "openai_api", "type": "external_dependency", "relationship": "calls", "description": "OpenAI provider calls OpenAI API", "external_endpoint": "https://api.openai.com"}]}, "dependency_groups": {"meta_agent_cluster": {"nodes": ["agent_factory", "task_orchestrator", "resource_manager", "communication_broker", "security_monitor", "knowledge_base", "discovery_registry"], "description": "Core meta-agent services that manage the platform"}, "agent_runtime_cluster": {"nodes": ["java_agent_common", "python_agent_common", "go_agent_common", "java_enterprise_agents", "python_ml_agents", "go_system_agents"], "description": "Agent runtime environments and implementations"}, "ai_model_cluster": {"nodes": ["ai_model_router", "gemini_provider", "claude_provider", "openai_provider"], "description": "AI model integration and routing services"}, "infrastructure_cluster": {"nodes": ["api_gateway", "event_store", "mcp_servers"], "description": "Core infrastructure services"}, "data_cluster": {"nodes": ["postgresql", "redis", "etcd"], "description": "Data storage and caching services"}, "monitoring_cluster": {"nodes": ["prometheus", "grafana", "jaeger"], "description": "Monitoring and observability services"}, "frontend_cluster": {"nodes": ["web_ui", "mobile_app", "workflow_designer"], "description": "User interface applications"}}, "deployment_order": [{"phase": 1, "name": "Infrastructure Foundation", "nodes": ["postgresql", "redis", "etcd"], "description": "Deploy core data stores first"}, {"phase": 2, "name": "Platform Services", "nodes": ["event_store", "api_gateway", "mcp_servers"], "description": "Deploy core platform infrastructure"}, {"phase": 3, "name": "Meta Agent Core", "nodes": ["discovery_registry", "communication_broker"], "description": "Deploy communication and discovery services"}, {"phase": 4, "name": "Meta Agent Services", "nodes": ["agent_factory", "resource_manager", "security_monitor"], "description": "Deploy core meta-agent services"}, {"phase": 5, "name": "AI and Knowledge", "nodes": ["ai_model_router", "knowledge_base", "task_orchestrator"], "description": "Deploy AI and knowledge management services"}, {"phase": 6, "name": "Agent Runtimes", "nodes": ["java_agent_common", "python_agent_common", "go_agent_common"], "description": "Deploy agent runtime libraries"}, {"phase": 7, "name": "Monitoring", "nodes": ["prometheus", "jaeger", "grafana"], "description": "Deploy monitoring and observability"}, {"phase": 8, "name": "User Interfaces", "nodes": ["web_ui", "workflow_designer", "mobile_app"], "description": "Deploy user-facing applications"}], "validation_rules": {"circular_dependency_check": {"description": "Ensure no circular dependencies exist in the graph", "violations": []}, "missing_dependency_check": {"description": "Identify nodes with unresolved dependencies", "potential_issues": ["External API availability", "Network connectivity requirements", "Resource allocation constraints"]}, "version_compatibility": {"description": "Check version compatibility between dependent components", "languages": {"java": "17+", "python": "3.9+", "go": "1.19+", "node": "18+"}}}, "maintenance_guidelines": {"dependency_updates": {"frequency": "weekly", "automated": true, "security_patches": "immediate"}, "impact_analysis": {"description": "Guidelines for analyzing change impact across dependencies", "critical_paths": ["communication_broker -> all_agents", "api_gateway -> web_ui", "postgresql -> meta_agents", "ai_model_router -> knowledge_base"], "change_categories": {"breaking_change": "Requires coordinated deployment", "feature_addition": "Backward compatible deployment", "bug_fix": "Independent deployment allowed", "security_patch": "Immediate deployment required"}}, "testing_strategy": {"unit_tests": "Per component", "integration_tests": "Per dependency relationship", "end_to_end_tests": "Per user workflow", "performance_tests": "Per critical path"}}}