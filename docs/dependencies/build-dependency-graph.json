{"metadata": {"name": "Build System Dependency Graph", "version": "1.0.0", "description": "Build system dependencies and compilation order for the AI-Native Agent Platform", "generated_at": "2024-01-15T00:00:00Z", "build_system": "bazel"}, "build_targets": {"shared_libraries": {"//shared/common/go:platform_common_go": {"language": "go", "type": "library", "srcs": ["pkg/auth/**/*.go", "pkg/config/**/*.go", "pkg/database/**/*.go", "pkg/http/**/*.go", "pkg/logging/**/*.go", "pkg/metrics/**/*.go", "pkg/tracing/**/*.go", "pkg/utils/**/*.go"], "deps": ["@com_github_gin_gonic_gin//:gin", "@com_github_golang_jwt_jwt_v5//:jwt", "@com_github_prometheus_client_golang//prometheus:prometheus", "@go_opentelemetry_io_otel//:otel", "@org_uber_go_zap//:zap"], "visibility": ["//visibility:public"]}, "//shared/common/java:platform_common_java": {"language": "java", "type": "library", "srcs": ["src/main/java/io/platform/common/**/*.java"], "deps": ["@maven//:org_springframework_spring_core", "@maven//:org_springframework_boot_spring_boot_starter", "@maven//:io_micrometer_micrometer_registry_prometheus", "@maven//:io_opentelemetry_opentelemetry_api"], "visibility": ["//visibility:public"]}, "//shared/common/python:platform_common_python": {"language": "python", "type": "library", "srcs": ["src/platform_common/**/*.py"], "deps": ["@pip//fastapi", "@pip//pydantic", "@pip//structlog", "@pip//prometheus_client", "@pip//opentelemetry_api"], "visibility": ["//visibility:public"]}, "//shared/proto:platform_proto": {"language": "proto", "type": "proto_library", "srcs": ["agent.proto", "workflow.proto", "common.proto", "metrics.proto"], "visibility": ["//visibility:public"]}, "//shared/proto:platform_proto_go": {"language": "go", "type": "go_proto_library", "deps": ["//shared/proto:platform_proto"], "importpath": "github.com/ai-platform/shared/proto", "visibility": ["//visibility:public"]}, "//shared/proto:platform_proto_java": {"language": "java", "type": "java_proto_library", "deps": ["//shared/proto:platform_proto"], "visibility": ["//visibility:public"]}, "//shared/proto:platform_proto_python": {"language": "python", "type": "py_proto_library", "deps": ["//shared/proto:platform_proto"], "visibility": ["//visibility:public"]}}, "platform_services": {"//platform/auth-service:auth_service": {"language": "go", "type": "go_binary", "srcs": ["src/main/go/auth/**/*.go"], "deps": ["//shared/common/go:platform_common_go", "//shared/proto:platform_proto_go", "@com_github_gin_gonic_gin//:gin", "@com_github_golang_jwt_jwt_v5//:jwt", "@com_github_jackc_pgx_v5//:pgx"], "data": ["//platform/auth-service:config"]}, "//platform/api-gateway:api_gateway": {"language": "go", "type": "go_binary", "srcs": ["src/main/go/gateway/**/*.go"], "deps": ["//shared/common/go:platform_common_go", "//shared/proto:platform_proto_go", "//platform/auth-service:auth_client", "@com_github_gin_gonic_gin//:gin", "@com_github_gorilla_mux//:mux"], "data": ["//platform/api-gateway:config"]}, "//platform/workflow-engine:workflow_engine": {"language": "java", "type": "java_binary", "srcs": ["src/main/java/**/*.java"], "deps": ["//shared/common/java:platform_common_java", "//shared/proto:platform_proto_java", "@maven//:org_springframework_boot_spring_boot_starter_web", "@maven//:org_springframework_boot_spring_boot_starter_data_jpa", "@maven//:org_postgresql_postgresql"], "resources": ["src/main/resources/**"], "main_class": "io.platform.workflow.WorkflowEngineApplication"}, "//platform/data-service:data_service": {"language": "python", "type": "py_binary", "srcs": ["src/data_service/**/*.py"], "deps": ["//shared/common/python:platform_common_python", "//shared/proto:platform_proto_python", "@pip//fastapi", "@pip//sqlalchemy", "@pip//asyncpg"], "data": ["//platform/data-service:config"], "main": "src/data_service/main.py"}, "//platform/dashboard:dashboard_backend": {"language": "javascript", "type": "nodejs_binary", "srcs": ["backend/src/**/*.ts"], "deps": ["@npm//express", "@npm//cors", "@npm//helmet", "@npm//jsonwebtoken"], "data": ["backend/package.json", "backend/tsconfig.json"]}, "//platform/dashboard:dashboard_frontend": {"language": "javascript", "type": "react_app", "srcs": ["frontend/src/**/*.tsx", "frontend/src/**/*.ts"], "deps": ["@npm//react", "@npm//react-dom", "@npm//react-router-dom", "@npm//axios", "@npm//mui/material"], "data": ["frontend/package.json", "frontend/public/**/*"]}}, "meta_agents": {"//meta-agents/agent-factory:agent_factory": {"language": "java", "type": "java_binary", "srcs": ["src/main/java/**/*.java"], "deps": ["//shared/common/java:platform_common_java", "//shared/proto:platform_proto_java", "//platform/auth-service:auth_client_java", "@maven//:org_springframework_boot_spring_boot_starter_web", "@maven//:io_kubernetes_client_java", "@maven//:com_fasterxml_jackson_core_jackson_databind"], "resources": ["src/main/resources/**"], "main_class": "io.platform.agentfactory.AgentFactoryApplication"}, "//meta-agents/task-orchestrator:task_orchestrator": {"language": "go", "type": "go_binary", "srcs": ["src/main/go/orchestrator/**/*.go"], "deps": ["//shared/common/go:platform_common_go", "//shared/proto:platform_proto_go", "//platform/workflow-engine:workflow_client_go", "@com_github_gin_gonic_gin//:gin", "@com_github_jackc_pgx_v5//:pgx", "@io_k8s_client_go//kubernetes:kubernetes"], "data": ["//meta-agents/task-orchestrator:config"]}, "//meta-agents/resource-manager:resource_manager": {"language": "python", "type": "py_binary", "srcs": ["src/resource_manager/**/*.py"], "deps": ["//shared/common/python:platform_common_python", "//shared/proto:platform_proto_python", "@pip//kubernetes", "@pip//prometheus_client", "@pip//scikit_learn"], "data": ["//meta-agents/resource-manager:config"], "main": "src/resource_manager/main.py"}, "//meta-agents/communication-broker:communication_broker": {"language": "go", "type": "go_binary", "srcs": ["src/main/go/broker/**/*.go"], "deps": ["//shared/common/go:platform_common_go", "//shared/proto:platform_proto_go", "@com_github_gin_gonic_gin//:gin", "@com_github_streadway_amqp//:amqp", "@com_github_confluentinc_confluent_kafka_go//:confluent_kafka_go"], "data": ["//meta-agents/communication-broker:config"]}, "//meta-agents/lifecycle-manager:lifecycle_manager": {"language": "java", "type": "java_binary", "srcs": ["src/main/java/**/*.java"], "deps": ["//shared/common/java:platform_common_java", "//shared/proto:platform_proto_java", "//meta-agents/agent-factory:agent_factory_client", "@maven//:org_springframework_boot_spring_boot_starter_web", "@maven//:io_kubernetes_client_java"], "resources": ["src/main/resources/**"], "main_class": "io.platform.lifecycle.LifecycleManagerApplication"}}, "agent_runtimes": {"//agents/runtime/python:python_runtime": {"language": "python", "type": "py_library", "srcs": ["src/runtime/**/*.py"], "deps": ["//shared/common/python:platform_common_python", "//shared/proto:platform_proto_python", "@pip//fastapi", "@pip//asyncio", "@pip//grpcio"]}, "//agents/runtime/java:java_runtime": {"language": "java", "type": "java_library", "srcs": ["src/main/java/**/*.java"], "deps": ["//shared/common/java:platform_common_java", "//shared/proto:platform_proto_java", "@maven//:org_springframework_boot_spring_boot_starter", "@maven//:io_grpc_grpc_netty_shaded"], "resources": ["src/main/resources/**"]}, "//agents/runtime/go:go_runtime": {"language": "go", "type": "go_library", "srcs": ["src/runtime/**/*.go"], "deps": ["//shared/common/go:platform_common_go", "//shared/proto:platform_proto_go", "@com_github_gin_gonic_gin//:gin", "@org_golang_google_grpc//:grpc"], "importpath": "github.com/ai-platform/agents/runtime/go"}}, "agent_templates": {"//agents/templates/python:python_templates": {"language": "python", "type": "py_library", "srcs": ["agent_template/src/**/*.py"], "deps": ["//agents/runtime/python:python_runtime", "//shared/common/python:platform_common_python"]}, "//agents/templates/java:java_templates": {"language": "java", "type": "java_library", "srcs": ["agent-template/src/main/java/**/*.java"], "deps": ["//agents/runtime/java:java_runtime", "//shared/common/java:platform_common_java"], "resources": ["agent-template/src/main/resources/**"]}, "//agents/templates/go:go_templates": {"language": "go", "type": "go_library", "srcs": ["agent-template/src/**/*.go"], "deps": ["//agents/runtime/go:go_runtime", "//shared/common/go:platform_common_go"], "importpath": "github.com/ai-platform/agents/templates/go"}}, "docker_images": {"//platform/auth-service:auth_service_image": {"type": "container_image", "base": "@go_base//image", "binary": "//platform/auth-service:auth_service", "repository": "ai-platform/auth-service"}, "//platform/api-gateway:api_gateway_image": {"type": "container_image", "base": "@go_base//image", "binary": "//platform/api-gateway:api_gateway", "repository": "ai-platform/api-gateway"}, "//platform/workflow-engine:workflow_engine_image": {"type": "container_image", "base": "@java_base//image", "binary": "//platform/workflow-engine:workflow_engine", "repository": "ai-platform/workflow-engine"}, "//platform/data-service:data_service_image": {"type": "container_image", "base": "@python_base//image", "binary": "//platform/data-service:data_service", "repository": "ai-platform/data-service"}, "//agents/templates/python:python_agent_image": {"type": "container_image", "base": "@python_base//image", "deps": ["//agents/templates/python:python_templates"], "repository": "ai-platform/python-agent"}, "//agents/templates/java:java_agent_image": {"type": "container_image", "base": "@java_base//image", "deps": ["//agents/templates/java:java_templates"], "repository": "ai-platform/java-agent"}, "//agents/templates/go:go_agent_image": {"type": "container_image", "base": "@go_base//image", "deps": ["//agents/templates/go:go_templates"], "repository": "ai-platform/go-agent"}}}, "build_dependencies": {"layer_0_foundation": ["//shared/proto:platform_proto", "//shared/proto:platform_proto_go", "//shared/proto:platform_proto_java", "//shared/proto:platform_proto_python"], "layer_1_shared_libraries": ["//shared/common/go:platform_common_go", "//shared/common/java:platform_common_java", "//shared/common/python:platform_common_python"], "layer_2_platform_services": ["//platform/auth-service:auth_service", "//platform/data-service:data_service"], "layer_3_core_services": ["//platform/api-gateway:api_gateway", "//platform/workflow-engine:workflow_engine", "//platform/dashboard:dashboard_backend", "//platform/dashboard:dashboard_frontend"], "layer_4_meta_agents": ["//meta-agents/agent-factory:agent_factory", "//meta-agents/communication-broker:communication_broker", "//meta-agents/task-orchestrator:task_orchestrator", "//meta-agents/resource-manager:resource_manager", "//meta-agents/lifecycle-manager:lifecycle_manager"], "layer_5_agent_runtimes": ["//agents/runtime/python:python_runtime", "//agents/runtime/java:java_runtime", "//agents/runtime/go:go_runtime"], "layer_6_agent_templates": ["//agents/templates/python:python_templates", "//agents/templates/java:java_templates", "//agents/templates/go:go_templates"], "layer_7_container_images": ["//platform/auth-service:auth_service_image", "//platform/api-gateway:api_gateway_image", "//platform/workflow-engine:workflow_engine_image", "//platform/data-service:data_service_image", "//agents/templates/python:python_agent_image", "//agents/templates/java:java_agent_image", "//agents/templates/go:go_agent_image"]}, "external_dependencies": {"go_dependencies": {"@com_github_gin_gonic_gin//:gin": "v1.9.1", "@com_github_gorilla_mux//:mux": "v1.8.1", "@com_github_golang_jwt_jwt_v5//:jwt": "v5.2.0", "@com_github_jackc_pgx_v5//:pgx": "v5.5.0", "@com_github_go_redis_redis_v8//:redis": "v8.11.5", "@com_github_prometheus_client_golang//prometheus:prometheus": "v1.17.0", "@go_opentelemetry_io_otel//:otel": "v1.21.0", "@org_uber_go_zap//:zap": "v1.26.0", "@org_golang_google_grpc//:grpc": "v1.59.0", "@io_k8s_client_go//kubernetes:kubernetes": "v0.29.0"}, "java_dependencies": {"@maven//:org_springframework_boot_spring_boot_starter_web": "3.2.0", "@maven//:org_springframework_boot_spring_boot_starter_data_jpa": "3.2.0", "@maven//:org_springframework_boot_spring_boot_starter_actuator": "3.2.0", "@maven//:org_postgresql_postgresql": "42.7.0", "@maven//:io_micrometer_micrometer_registry_prometheus": "1.12.0", "@maven//:io_opentelemetry_opentelemetry_api": "1.32.0", "@maven//:io_grpc_grpc_netty_shaded": "1.59.0", "@maven//:com_fasterxml_jackson_core_jackson_databind": "2.16.0", "@maven//:io_kubernetes_client_java": "19.0.0"}, "python_dependencies": {"@pip//fastapi": "0.104.0", "@pip//uvicorn": "0.24.0", "@pip//pydantic": "2.5.0", "@pip//sqlalchemy": "2.0.0", "@pip//asyncpg": "0.29.0", "@pip//aioredis": "2.0.0", "@pip//prometheus_client": "0.19.0", "@pip//opentelemetry_api": "1.21.0", "@pip//grpcio": "1.59.0", "@pip//kubernetes": "28.1.0"}, "javascript_dependencies": {"@npm//react": "18.2.0", "@npm//react-dom": "18.2.0", "@npm//typescript": "5.3.0", "@npm//express": "4.18.2", "@npm//cors": "2.8.5", "@npm//helmet": "7.1.0", "@npm//jsonwebtoken": "9.0.2"}}, "build_configurations": {"development": {"compilation_mode": "dbg", "strip": "never", "optimization": "none", "test_strategy": "local"}, "staging": {"compilation_mode": "opt", "strip": "sometimes", "optimization": "size", "test_strategy": "remote"}, "production": {"compilation_mode": "opt", "strip": "always", "optimization": "speed", "test_strategy": "remote"}}, "test_dependencies": {"unit_tests": {"//shared/common/go:platform_common_go_test": {"type": "go_test", "srcs": ["**/*_test.go"], "deps": ["//shared/common/go:platform_common_go", "@com_github_stretchr_testify//assert:assert"]}, "//platform/auth-service:auth_service_test": {"type": "go_test", "srcs": ["src/main/go/auth/**/*_test.go"], "deps": ["//platform/auth-service:auth_service_lib", "@com_github_stretchr_testify//assert:assert"]}}, "integration_tests": {"//tests/integration:platform_integration_test": {"type": "py_test", "srcs": ["tests/integration/**/*.py"], "deps": ["@pip//pytest", "@pip//requests", "@pip//kubernetes"], "data": ["//infrastructure/kubernetes:test_manifests"]}}, "e2e_tests": {"//tests/e2e:platform_e2e_test": {"type": "py_test", "srcs": ["tests/e2e/**/*.py"], "deps": ["@pip//pytest", "@pip//selenium", "@pip//requests"], "tags": ["e2e", "requires-docker"]}}}, "performance_optimizations": {"parallel_builds": {"enabled": true, "max_jobs": 8, "remote_cache": true, "local_cache_size": "10GB"}, "incremental_builds": {"enabled": true, "action_cache": true, "repository_cache": true}, "build_stamping": {"enabled": true, "git_revision": true, "build_timestamp": true}}, "ci_cd_integration": {"github_actions": {"build_workflow": ".github/workflows/build.yml", "test_workflow": ".github/workflows/test.yml", "deploy_workflow": ".github/workflows/deploy.yml"}, "build_triggers": ["push_to_main", "pull_request", "tag_creation"], "artifact_publishing": {"container_registry": "ghcr.io/ai-platform", "helm_repository": "https://charts.ai-platform.io"}}}