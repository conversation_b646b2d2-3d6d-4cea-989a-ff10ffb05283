# Platform API Documentation

## Overview

The Platform API provides access to core platform services including user management, system monitoring, configuration, and administrative functions. This API serves as the foundation for platform operations and management.

## Base URL

```
https://api.platform.io/v1/platform
```

## Authentication

Platform APIs support multiple authentication methods:

```http
# User authentication
Authorization: Bearer <user_jwt_token>

# Service authentication
Authorization: Bearer <service_token>
X-Service-ID: <service_identifier>

# Admin authentication
Authorization: Bearer <admin_token>
X-Admin-Role: <admin_role>
```

## Core Platform Services

### 1. User Management

#### Create User

Create a new platform user.

```http
POST /users
```

**Request Body:**
```json
{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "full_name": "<PERSON>",
  "role": "developer",
  "permissions": [
    "agents:create",
    "agents:read",
    "workflows:create",
    "workflows:execute"
  ],
  "profile": {
    "department": "Engineering",
    "team": "AI Platform",
    "preferred_language": "en-US"
  },
  "mfa_enabled": true
}
```

**Response (201 Created):**
```json
{
  "user_id": "user-*********",
  "username": "jane.smith",
  "email": "<EMAIL>",
  "role": "developer",
  "status": "active",
  "created_at": "2024-01-15T11:00:00Z",
  "mfa_setup_required": true,
  "onboarding_status": "pending"
}
```

#### Get User Profile

Retrieve user profile information.

```http
GET /users/{user_id}
```

**Response (200 OK):**
```json
{
  "user_id": "user-*********",
  "username": "jane.smith",
  "email": "<EMAIL>",
  "full_name": "Jane Smith",
  "role": "developer",
  "status": "active",
  "permissions": [
    "agents:create",
    "agents:read",
    "workflows:create",
    "workflows:execute"
  ],
  "profile": {
    "department": "Engineering",
    "team": "AI Platform",
    "preferred_language": "en-US",
    "timezone": "America/Los_Angeles"
  },
  "security": {
    "mfa_enabled": true,
    "last_login": "2024-01-15T10:30:00Z",
    "failed_login_attempts": 0,
    "password_last_changed": "2024-01-01T00:00:00Z"
  },
  "usage_stats": {
    "agents_created": 15,
    "workflows_executed": 127,
    "api_calls_last_30_days": 2456
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Update User Permissions

Modify user permissions and role assignments.

```http
PUT /users/{user_id}/permissions
```

**Request Body:**
```json
{
  "role": "senior_developer",
  "permissions": [
    "agents:create",
    "agents:read",
    "agents:update",
    "agents:delete",
    "workflows:create",
    "workflows:execute",
    "workflows:manage",
    "platform:monitor"
  ],
  "resource_limits": {
    "max_agents": 50,
    "max_workflows": 20,
    "cpu_quota": "100",
    "memory_quota": "200Gi"
  }
}
```

**Response (200 OK):**
```json
{
  "user_id": "user-*********",
  "role": "senior_developer",
  "permissions_updated": true,
  "effective_permissions": [
    "agents:create",
    "agents:read",
    "agents:update",
    "agents:delete",
    "workflows:create",
    "workflows:execute",
    "workflows:manage",
    "platform:monitor"
  ],
  "updated_at": "2024-01-15T11:00:00Z"
}
```

### 2. System Monitoring

#### Get Platform Health

Retrieve overall platform health status.

```http
GET /health
```

**Response (200 OK):**
```json
{
  "overall_status": "healthy",
  "last_updated": "2024-01-15T11:00:00Z",
  "components": {
    "meta_agents": {
      "status": "healthy",
      "services": {
        "agent_factory": "healthy",
        "task_orchestrator": "healthy",
        "resource_manager": "healthy",
        "communication_broker": "healthy",
        "security_monitor": "healthy",
        "knowledge_base": "healthy",
        "discovery_registry": "healthy"
      }
    },
    "infrastructure": {
      "status": "healthy",
      "services": {
        "kubernetes": "healthy",
        "postgresql": "healthy",
        "redis": "healthy",
        "object_storage": "healthy",
        "monitoring": "healthy"
      }
    },
    "ai_models": {
      "status": "healthy",
      "providers": {
        "openai": "healthy",
        "anthropic": "healthy",
        "google": "healthy"
      }
    },
    "agents": {
      "status": "healthy",
      "total_agents": 156,
      "healthy_agents": 154,
      "unhealthy_agents": 2,
      "agent_pools": {
        "java": "healthy",
        "python": "healthy",
        "go": "healthy"
      }
    }
  },
  "performance_metrics": {
    "response_time_p95": "125ms",
    "error_rate": "0.02%",
    "throughput": "1250 req/min",
    "availability": "99.98%"
  }
}
```

#### Get System Metrics

Retrieve detailed system performance metrics.

```http
GET /metrics
```

**Query Parameters:**
- `from` (ISO 8601): Start time for metrics
- `to` (ISO 8601): End time for metrics
- `resolution` (string): Time resolution (1m, 5m, 1h, 1d)
- `metrics` (array): Specific metrics to retrieve

**Response (200 OK):**
```json
{
  "time_range": {
    "from": "2024-01-15T10:00:00Z",
    "to": "2024-01-15T11:00:00Z",
    "resolution": "5m"
  },
  "metrics": {
    "platform_requests": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 1250},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 1340},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 1180}
    ],
    "agent_executions": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 450},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 520},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 480}
    ],
    "workflow_completions": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 85},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 92},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 78}
    ],
    "resource_utilization": {
      "cpu": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 65},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 72},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 68}
      ],
      "memory": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 68},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 71},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 69}
      ]
    },
    "error_rates": {
      "4xx_errors": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 12},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 8},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 15}
      ],
      "5xx_errors": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 2},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 1},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 3}
      ]
    }
  }
}
```

#### Get System Alerts

Retrieve active system alerts and notifications.

```http
GET /alerts
```

**Query Parameters:**
- `severity` (string): Filter by severity (critical, high, medium, low)
- `status` (string): Filter by status (active, acknowledged, resolved)
- `component` (string): Filter by component
- `limit` (integer): Maximum number of alerts to return

**Response (200 OK):**
```json
{
  "alerts": [
    {
      "alert_id": "alert-*********",
      "severity": "medium",
      "status": "active",
      "component": "agent-pool-python",
      "title": "High memory usage in Python agent pool",
      "description": "Python agent pool memory utilization is above 85%",
      "created_at": "2024-01-15T10:45:00Z",
      "last_updated": "2024-01-15T10:50:00Z",
      "metrics": {
        "current_usage": "85.2%",
        "threshold": "85%",
        "trend": "increasing"
      },
      "suggested_actions": [
        "Scale up Python agent pool",
        "Optimize memory usage in Python agents",
        "Review workload distribution"
      ]
    },
    {
      "alert_id": "alert-*********",
      "severity": "low",
      "status": "acknowledged",
      "component": "ai-model-router",
      "title": "Increased latency to OpenAI API",
      "description": "Average response time from OpenAI API increased by 20%",
      "created_at": "2024-01-15T10:30:00Z",
      "acknowledged_at": "2024-01-15T10:35:00Z",
      "acknowledged_by": "user-ops-001"
    }
  ],
  "summary": {
    "total_alerts": 2,
    "by_severity": {
      "critical": 0,
      "high": 0,
      "medium": 1,
      "low": 1
    },
    "by_status": {
      "active": 1,
      "acknowledged": 1,
      "resolved": 0
    }
  }
}
```

### 3. Configuration Management

#### Get Platform Configuration

Retrieve platform configuration settings.

```http
GET /config
```

**Response (200 OK):**
```json
{
  "platform": {
    "version": "1.2.0",
    "environment": "production",
    "region": "us-west-2",
    "cluster_name": "ai-platform-prod"
  },
  "features": {
    "auto_scaling": true,
    "self_healing": true,
    "continuous_learning": true,
    "multi_region": false,
    "gpu_support": true
  },
  "limits": {
    "max_agents_per_user": 100,
    "max_workflows_per_user": 50,
    "max_concurrent_executions": 1000,
    "request_rate_limit": 10000
  },
  "integrations": {
    "ai_providers": ["openai", "anthropic", "google"],
    "storage_backends": ["s3", "gcs"],
    "monitoring_tools": ["grafana", "prometheus", "jaeger"],
    "notification_channels": ["slack", "email", "webhook"]
  },
  "security": {
    "authentication_methods": ["oauth2", "saml", "jwt"],
    "mfa_required": true,
    "session_timeout": 3600,
    "password_policy": {
      "min_length": 12,
      "require_special_chars": true,
      "max_age_days": 90
    }
  }
}
```

#### Update Platform Configuration

Update platform configuration settings.

```http
PUT /config
```

**Request Body:**
```json
{
  "limits": {
    "max_agents_per_user": 150,
    "max_workflows_per_user": 75,
    "request_rate_limit": 15000
  },
  "features": {
    "multi_region": true
  },
  "security": {
    "session_timeout": 7200
  }
}
```

**Response (200 OK):**
```json
{
  "updated_at": "2024-01-15T11:00:00Z",
  "changes": [
    {
      "path": "limits.max_agents_per_user",
      "old_value": 100,
      "new_value": 150
    },
    {
      "path": "limits.max_workflows_per_user",
      "old_value": 50,
      "new_value": 75
    },
    {
      "path": "features.multi_region",
      "old_value": false,
      "new_value": true
    }
  ],
  "restart_required": false
}
```

### 4. Billing and Usage

#### Get Usage Statistics

Retrieve platform usage statistics and billing information.

```http
GET /usage
```

**Query Parameters:**
- `from` (ISO 8601): Start date for usage period
- `to` (ISO 8601): End date for usage period
- `user_id` (string): Filter by specific user
- `breakdown` (string): Breakdown level (user, department, team)

**Response (200 OK):**
```json
{
  "usage_period": {
    "from": "2024-01-01T00:00:00Z",
    "to": "2024-01-31T23:59:59Z"
  },
  "summary": {
    "total_agent_hours": 15420,
    "total_workflow_executions": 8540,
    "total_api_calls": 2456789,
    "total_storage_gb_hours": 125000,
    "total_compute_cost": 2456.78,
    "total_storage_cost": 125.45,
    "total_ai_model_cost": 678.90
  },
  "breakdown_by_service": {
    "agent_compute": {
      "java_agents": {
        "hours": 5200,
        "cost": 832.00
      },
      "python_agents": {
        "hours": 7800,
        "cost": 1248.00
      },
      "go_agents": {
        "hours": 2420,
        "cost": 376.78
      }
    },
    "ai_models": {
      "openai": {
        "requests": 125000,
        "tokens": 15600000,
        "cost": 312.00
      },
      "anthropic": {
        "requests": 89000,
        "tokens": 11200000,
        "cost": 224.00
      },
      "google": {
        "requests": 45000,
        "tokens": 5700000,
        "cost": 142.90
      }
    }
  },
  "top_users": [
    {
      "user_id": "user-*********",
      "username": "jane.smith",
      "agent_hours": 2340,
      "workflow_executions": 1250,
      "cost": 445.67
    }
  ]
}
```

#### Get Cost Optimization Recommendations

Get recommendations for cost optimization.

```http
GET /usage/optimization
```

**Response (200 OK):**
```json
{
  "recommendations": [
    {
      "type": "resource_optimization",
      "priority": "high",
      "potential_savings": 234.56,
      "title": "Optimize Python agent memory allocation",
      "description": "Several Python agents are allocated more memory than they typically use",
      "details": {
        "affected_agents": 12,
        "current_allocation": "8Gi",
        "recommended_allocation": "6Gi",
        "utilization_average": "65%"
      },
      "implementation": {
        "effort": "low",
        "risk": "low",
        "steps": [
          "Update agent deployment configurations",
          "Monitor performance after changes",
          "Rollback if issues occur"
        ]
      }
    },
    {
      "type": "usage_pattern",
      "priority": "medium",
      "potential_savings": 156.78,
      "title": "Implement scheduled shutdown for development agents",
      "description": "Development agents run 24/7 but are only used during business hours",
      "details": {
        "affected_agents": 8,
        "current_uptime": "24h/day",
        "recommended_uptime": "10h/day",
        "business_hours": "8 AM - 6 PM PST"
      }
    }
  ],
  "total_potential_savings": 391.34,
  "savings_percentage": 11.2
}
```

### 5. Audit and Compliance

#### Get Audit Log

Retrieve platform audit logs.

```http
GET /audit
```

**Query Parameters:**
- `from` (ISO 8601): Start time for audit logs
- `to` (ISO 8601): End time for audit logs
- `user_id` (string): Filter by user
- `action` (string): Filter by action type
- `resource` (string): Filter by resource type
- `limit` (integer): Maximum number of entries

**Response (200 OK):**
```json
{
  "audit_entries": [
    {
      "event_id": "audit-*********",
      "timestamp": "2024-01-15T11:00:00Z",
      "user_id": "user-jane-smith",
      "user_ip": "*************",
      "action": "agent.create",
      "resource": {
        "type": "agent",
        "id": "agent-new-processor",
        "name": "document-processor"
      },
      "result": "success",
      "metadata": {
        "user_agent": "Mozilla/5.0...",
        "session_id": "session-*********",
        "request_id": "req-456789123"
      }
    },
    {
      "event_id": "audit-*********",
      "timestamp": "2024-01-15T10:58:00Z",
      "user_id": "user-admin-001",
      "user_ip": "*********",
      "action": "platform.config.update",
      "resource": {
        "type": "configuration",
        "id": "platform-config",
        "name": "Platform Configuration"
      },
      "result": "success",
      "changes": [
        {
          "field": "limits.max_agents_per_user",
          "old_value": 100,
          "new_value": 150
        }
      ]
    }
  ],
  "total_count": 15420,
  "query_metadata": {
    "query_time": "45ms",
    "results_from_cache": false
  }
}
```

#### Generate Compliance Report

Generate compliance reports for various standards.

```http
POST /compliance/reports
```

**Request Body:**
```json
{
  "report_type": "soc2",
  "period": {
    "from": "2024-01-01T00:00:00Z",
    "to": "2024-01-31T23:59:59Z"
  },
  "scope": {
    "include_components": ["platform", "agents", "workflows"],
    "exclude_environments": ["development", "testing"]
  },
  "format": "pdf",
  "delivery": {
    "method": "email",
    "recipients": ["<EMAIL>", "<EMAIL>"]
  }
}
```

**Response (202 Accepted):**
```json
{
  "report_id": "report-*********",
  "status": "generating",
  "estimated_completion": "2024-01-15T11:15:00Z",
  "report_metadata": {
    "type": "soc2",
    "period_covered": "30 days",
    "total_events": 125420,
    "controls_evaluated": 47
  }
}
```

### 6. Integration Management

#### List Integrations

Get available platform integrations.

```http
GET /integrations
```

**Response (200 OK):**
```json
{
  "integrations": [
    {
      "integration_id": "slack-notifications",
      "name": "Slack Notifications",
      "type": "notification",
      "status": "active",
      "configuration": {
        "webhook_url": "https://hooks.slack.com/...",
        "channels": ["#ai-platform", "#alerts"],
        "event_types": ["agent.error", "workflow.failed"]
      },
      "last_used": "2024-01-15T10:45:00Z",
      "usage_stats": {
        "messages_sent": 1250,
        "success_rate": 0.998
      }
    },
    {
      "integration_id": "datadog-monitoring",
      "name": "Datadog Monitoring",
      "type": "monitoring",
      "status": "active",
      "configuration": {
        "api_key": "***masked***",
        "metrics_enabled": true,
        "logs_enabled": true,
        "traces_enabled": true
      },
      "last_sync": "2024-01-15T11:00:00Z"
    }
  ]
}
```

#### Configure Integration

Add or update platform integration.

```http
PUT /integrations/{integration_id}
```

**Request Body:**
```json
{
  "name": "Enhanced Slack Notifications",
  "configuration": {
    "webhook_url": "https://hooks.slack.com/new-webhook",
    "channels": ["#ai-platform", "#alerts", "#performance"],
    "event_types": [
      "agent.error",
      "workflow.failed",
      "resource.threshold_exceeded"
    ],
    "notification_format": "detailed",
    "rate_limit": 10
  },
  "enabled": true
}
```

**Response (200 OK):**
```json
{
  "integration_id": "slack-notifications",
  "status": "updated",
  "configuration_valid": true,
  "test_result": {
    "success": true,
    "test_message_sent": true,
    "response_time": "125ms"
  },
  "updated_at": "2024-01-15T11:00:00Z"
}
```

## Error Handling

Platform API errors provide detailed information for troubleshooting:

```json
{
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "User does not have permission to perform this action",
    "details": {
      "required_permission": "platform:admin",
      "user_permissions": ["agents:create", "workflows:execute"],
      "resource": "platform configuration",
      "action": "update"
    },
    "suggestions": [
      "Contact your administrator to request additional permissions",
      "Use a service account with appropriate permissions"
    ],
    "request_id": "req-*********",
    "timestamp": "2024-01-15T11:00:00Z"
  }
}
```

## Rate Limiting

Platform API rate limits vary by endpoint category:

- **User management**: 100 requests per hour
- **Configuration**: 50 requests per hour
- **Monitoring (read)**: 1000 requests per hour
- **Audit logs**: 200 requests per hour
- **Billing/Usage**: 100 requests per hour

## Webhooks

Platform events can trigger webhooks for external integrations:

### Webhook Events

- `user.created`
- `user.updated`
- `user.deleted`
- `platform.health.changed`
- `alert.created`
- `alert.resolved`
- `config.updated`
- `usage.threshold.exceeded`

### Webhook Payload Example

```json
{
  "event": "alert.created",
  "timestamp": "2024-01-15T11:00:00Z",
  "data": {
    "alert_id": "alert-*********",
    "severity": "high",
    "component": "agent-pool",
    "title": "Agent pool capacity exceeded",
    "description": "Agent pool utilization is above 90%"
  },
  "metadata": {
    "platform_version": "1.2.0",
    "environment": "production",
    "region": "us-west-2"
  }
}
```

## Best Practices

1. **Use Proper Authentication**: Choose appropriate auth method for your use case
2. **Monitor Usage**: Track API usage and optimize for cost efficiency
3. **Handle Rate Limits**: Implement proper backoff and retry logic
4. **Cache Configuration**: Cache platform configuration to reduce API calls
5. **Set Up Webhooks**: Use webhooks for real-time notifications
6. **Implement Health Checks**: Monitor platform health proactively
7. **Review Audit Logs**: Regularly review audit logs for security and compliance