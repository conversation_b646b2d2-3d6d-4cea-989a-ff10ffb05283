# Agent API Documentation

## Overview

The Agent API provides comprehensive endpoints for managing agents throughout their lifecycle. This RESTful API enables creation, configuration, monitoring, and control of agents within the AI-Native Agent Platform.

## Base URL

```
https://api.platform.io/v1
```

## Authentication

All API endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer <jwt_token>
```

## Rate Limiting

- **Standard Users**: 1000 requests per hour
- **Premium Users**: 10000 requests per hour
- **Enterprise**: Custom limits

Rate limit headers are included in all responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1609459200
```

## Endpoints

### 1. Agent Management

#### Create Agent

Create a new agent from a specification.

```http
POST /agents
```

**Request Body:**
```json
{
  "name": "data-processor-agent",
  "description": "Processes and transforms streaming data",
  "language": "python",
  "capabilities": [
    "data-transformation",
    "stream-processing",
    "batch-processing"
  ],
  "resources": {
    "cpu": "2",
    "memory": "4Gi",
    "storage": "10Gi"
  },
  "configuration": {
    "environment": {
      "LOG_LEVEL": "INFO",
      "MAX_BATCH_SIZE": "1000"
    },
    "secrets": {
      "database_url": "vault://secrets/db-connection",
      "api_key": "vault://secrets/external-api"
    }
  },
  "integrations": [
    {
      "type": "database",
      "provider": "postgresql",
      "connection": "primary-db"
    },
    {
      "type": "message_queue",
      "provider": "kafka",
      "topics": ["input-stream", "output-stream"]
    }
  ],
  "ai_models": [
    {
      "provider": "openai",
      "model": "gpt-4",
      "purpose": "data-analysis"
    }
  ],
  "security": {
    "encryption": "required",
    "authentication": "oauth2",
    "permissions": ["read:data", "write:processed"]
  }
}
```

**Response (201 Created):**
```json
{
  "id": "agent-123e4567-e89b-12d3-a456-426614174000",
  "name": "data-processor-agent",
  "status": "creating",
  "version": "1.0.0",
  "created_at": "2024-01-15T10:30:00Z",
  "creation_job_id": "job-456",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "endpoints": {
    "health": "https://agents.platform.io/agent-123/health",
    "metrics": "https://agents.platform.io/agent-123/metrics",
    "logs": "https://agents.platform.io/agent-123/logs"
  }
}
```

#### Get Agent

Retrieve agent information by ID.

```http
GET /agents/{agent_id}
```

**Response (200 OK):**
```json
{
  "id": "agent-123e4567-e89b-12d3-a456-426614174000",
  "name": "data-processor-agent",
  "description": "Processes and transforms streaming data",
  "status": "active",
  "version": "1.0.0",
  "language": "python",
  "capabilities": [
    "data-transformation",
    "stream-processing",
    "batch-processing"
  ],
  "resources": {
    "cpu": {
      "requested": "2",
      "allocated": "2",
      "utilization": "65%"
    },
    "memory": {
      "requested": "4Gi",
      "allocated": "4Gi",
      "utilization": "2.1Gi"
    },
    "storage": {
      "requested": "10Gi",
      "allocated": "10Gi",
      "utilization": "3.2Gi"
    }
  },
  "health": {
    "status": "healthy",
    "last_check": "2024-01-15T10:45:00Z",
    "checks": {
      "database": "healthy",
      "message_queue": "healthy",
      "ai_models": "healthy"
    }
  },
  "metrics": {
    "tasks_processed": 15420,
    "tasks_per_minute": 85.6,
    "success_rate": 0.998,
    "average_response_time": "120ms"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:45:00Z",
  "endpoints": {
    "health": "https://agents.platform.io/agent-123/health",
    "metrics": "https://agents.platform.io/agent-123/metrics",
    "logs": "https://agents.platform.io/agent-123/logs",
    "communication": "wss://comm.platform.io/agent-123"
  }
}
```

#### List Agents

Retrieve a list of agents with filtering and pagination.

```http
GET /agents
```

**Query Parameters:**
- `status` (string): Filter by status (creating, active, error, stopped)
- `language` (string): Filter by programming language
- `capability` (string): Filter by capability
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20, max: 100)
- `sort` (string): Sort field (name, created_at, status)
- `order` (string): Sort order (asc, desc)

**Response (200 OK):**
```json
{
  "agents": [
    {
      "id": "agent-123e4567-e89b-12d3-a456-426614174000",
      "name": "data-processor-agent",
      "status": "active",
      "version": "1.0.0",
      "language": "python",
      "capabilities": ["data-transformation", "stream-processing"],
      "created_at": "2024-01-15T10:30:00Z",
      "health": "healthy",
      "metrics": {
        "tasks_processed": 15420,
        "success_rate": 0.998
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_pages": 5,
    "total_items": 95,
    "has_next": true,
    "has_previous": false
  }
}
```

#### Update Agent

Update agent configuration or trigger version upgrade.

```http
PUT /agents/{agent_id}
```

**Request Body:**
```json
{
  "configuration": {
    "environment": {
      "LOG_LEVEL": "DEBUG",
      "MAX_BATCH_SIZE": "2000"
    }
  },
  "resources": {
    "cpu": "4",
    "memory": "8Gi"
  },
  "version": "1.1.0"
}
```

**Response (200 OK):**
```json
{
  "id": "agent-123e4567-e89b-12d3-a456-426614174000",
  "status": "updating",
  "update_job_id": "job-789",
  "estimated_completion": "2024-01-15T11:05:00Z"
}
```

#### Delete Agent

Delete an agent and clean up all resources.

```http
DELETE /agents/{agent_id}
```

**Query Parameters:**
- `force` (boolean): Force deletion even if agent is active (default: false)
- `preserve_data` (boolean): Keep agent data for archival (default: true)

**Response (202 Accepted):**
```json
{
  "message": "Agent deletion initiated",
  "deletion_job_id": "job-321",
  "estimated_completion": "2024-01-15T11:10:00Z"
}
```

### 2. Agent Control

#### Start Agent

Start a stopped agent.

```http
POST /agents/{agent_id}/start
```

**Response (200 OK):**
```json
{
  "status": "starting",
  "estimated_ready": "2024-01-15T11:02:00Z"
}
```

#### Stop Agent

Stop a running agent gracefully.

```http
POST /agents/{agent_id}/stop
```

**Request Body (Optional):**
```json
{
  "timeout": 300,
  "force": false
}
```

**Response (200 OK):**
```json
{
  "status": "stopping",
  "estimated_stopped": "2024-01-15T11:07:00Z"
}
```

#### Restart Agent

Restart an agent (stop + start).

```http
POST /agents/{agent_id}/restart
```

**Response (200 OK):**
```json
{
  "status": "restarting",
  "estimated_ready": "2024-01-15T11:05:00Z"
}
```

### 3. Agent Communication

#### Send Message

Send a message to an agent.

```http
POST /agents/{agent_id}/messages
```

**Request Body:**
```json
{
  "type": "task",
  "content": {
    "action": "process_data",
    "data": {
      "source": "input-stream",
      "target": "output-stream",
      "transformation": "normalize"
    }
  },
  "priority": "normal",
  "timeout": 30000,
  "callback_url": "https://your-app.com/webhooks/agent-response"
}
```

**Response (200 OK):**
```json
{
  "message_id": "msg-987654321",
  "status": "delivered",
  "estimated_response": "2024-01-15T11:01:00Z"
}
```

#### Get Message Status

Check the status of a sent message.

```http
GET /agents/{agent_id}/messages/{message_id}
```

**Response (200 OK):**
```json
{
  "message_id": "msg-987654321",
  "status": "completed",
  "sent_at": "2024-01-15T11:00:00Z",
  "completed_at": "2024-01-15T11:00:30Z",
  "response": {
    "status": "success",
    "result": {
      "processed_records": 1500,
      "output_location": "output-stream",
      "processing_time": "28.5s"
    }
  }
}
```

### 4. Agent Monitoring

#### Get Agent Health

Get detailed health information for an agent.

```http
GET /agents/{agent_id}/health
```

**Response (200 OK):**
```json
{
  "overall_status": "healthy",
  "last_updated": "2024-01-15T11:00:00Z",
  "checks": {
    "database_connection": {
      "status": "healthy",
      "response_time": "5ms",
      "last_check": "2024-01-15T11:00:00Z"
    },
    "message_queue_connection": {
      "status": "healthy",
      "lag": "0.2s",
      "last_check": "2024-01-15T11:00:00Z"
    },
    "ai_model_availability": {
      "status": "healthy",
      "models": {
        "gpt-4": "available",
        "claude-2": "available"
      },
      "last_check": "2024-01-15T10:59:30Z"
    },
    "resource_usage": {
      "status": "healthy",
      "cpu": "65%",
      "memory": "52%",
      "disk": "32%"
    }
  }
}
```

#### Get Agent Metrics

Retrieve performance metrics for an agent.

```http
GET /agents/{agent_id}/metrics
```

**Query Parameters:**
- `from` (ISO 8601): Start time for metrics
- `to` (ISO 8601): End time for metrics
- `resolution` (string): Time resolution (1m, 5m, 1h, 1d)

**Response (200 OK):**
```json
{
  "agent_id": "agent-123e4567-e89b-12d3-a456-426614174000",
  "time_range": {
    "from": "2024-01-15T10:00:00Z",
    "to": "2024-01-15T11:00:00Z",
    "resolution": "5m"
  },
  "metrics": {
    "tasks_processed": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 450},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 478},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 492}
    ],
    "response_time_p95": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 125},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 118},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 132}
    ],
    "success_rate": [
      {"timestamp": "2024-01-15T10:00:00Z", "value": 0.998},
      {"timestamp": "2024-01-15T10:05:00Z", "value": 0.997},
      {"timestamp": "2024-01-15T10:10:00Z", "value": 0.999}
    ],
    "resource_usage": {
      "cpu": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 62},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 68},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 65}
      ],
      "memory": [
        {"timestamp": "2024-01-15T10:00:00Z", "value": 2048},
        {"timestamp": "2024-01-15T10:05:00Z", "value": 2156},
        {"timestamp": "2024-01-15T10:10:00Z", "value": 2134}
      ]
    }
  }
}
```

#### Get Agent Logs

Retrieve logs for an agent.

```http
GET /agents/{agent_id}/logs
```

**Query Parameters:**
- `from` (ISO 8601): Start time for logs
- `to` (ISO 8601): End time for logs
- `level` (string): Log level filter (DEBUG, INFO, WARN, ERROR)
- `limit` (integer): Maximum number of log entries (default: 100, max: 1000)
- `search` (string): Search term in log messages

**Response (200 OK):**
```json
{
  "logs": [
    {
      "timestamp": "2024-01-15T11:00:00.123Z",
      "level": "INFO",
      "message": "Processing batch of 150 records",
      "context": {
        "batch_id": "batch-456",
        "source": "input-stream",
        "records_count": 150
      }
    },
    {
      "timestamp": "2024-01-15T11:00:02.456Z",
      "level": "DEBUG",
      "message": "Applied transformation: normalize",
      "context": {
        "batch_id": "batch-456",
        "transformation": "normalize",
        "processing_time": "1.2s"
      }
    }
  ],
  "total_count": 2847,
  "has_more": true
}
```

### 5. Agent Templates

#### List Templates

Get available agent templates.

```http
GET /agent-templates
```

**Response (200 OK):**
```json
{
  "templates": [
    {
      "id": "template-data-processor",
      "name": "Data Processing Agent",
      "description": "Template for creating data processing agents",
      "language": "python",
      "version": "1.2.0",
      "capabilities": ["data-transformation", "stream-processing"],
      "parameters": [
        {
          "name": "batch_size",
          "type": "integer",
          "default": 1000,
          "description": "Maximum batch size for processing"
        },
        {
          "name": "transformation_type",
          "type": "string",
          "enum": ["normalize", "aggregate", "filter"],
          "required": true,
          "description": "Type of data transformation"
        }
      ]
    }
  ]
}
```

#### Get Template

Get detailed information about a specific template.

```http
GET /agent-templates/{template_id}
```

**Response (200 OK):**
```json
{
  "id": "template-data-processor",
  "name": "Data Processing Agent",
  "description": "Template for creating data processing agents",
  "language": "python",
  "version": "1.2.0",
  "capabilities": ["data-transformation", "stream-processing"],
  "parameters": [
    {
      "name": "batch_size",
      "type": "integer",
      "default": 1000,
      "min": 1,
      "max": 10000,
      "description": "Maximum batch size for processing"
    }
  ],
  "resources": {
    "cpu": {
      "min": "0.5",
      "max": "8",
      "default": "2"
    },
    "memory": {
      "min": "1Gi",
      "max": "32Gi",
      "default": "4Gi"
    }
  },
  "integrations": [
    {
      "type": "database",
      "required": false,
      "providers": ["postgresql", "mysql", "mongodb"]
    }
  ],
  "code_structure": {
    "entry_point": "src/main.py",
    "configuration": "config/agent.yaml",
    "tests": "tests/",
    "documentation": "docs/"
  }
}
```

## Error Handling

All API errors follow a consistent format:

```json
{
  "error": {
    "code": "AGENT_NOT_FOUND",
    "message": "Agent with ID 'agent-123' not found",
    "details": {
      "agent_id": "agent-123",
      "timestamp": "2024-01-15T11:00:00Z"
    },
    "request_id": "req-987654321"
  }
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `AGENT_NOT_FOUND` | 404 | Agent does not exist |
| `AGENT_CREATION_FAILED` | 400 | Agent creation failed validation |
| `INSUFFICIENT_RESOURCES` | 409 | Not enough resources to create agent |
| `INVALID_CONFIGURATION` | 400 | Agent configuration is invalid |
| `AGENT_NOT_READY` | 409 | Agent is not ready for operation |
| `COMMUNICATION_FAILED` | 502 | Failed to communicate with agent |
| `RATE_LIMIT_EXCEEDED` | 429 | API rate limit exceeded |
| `UNAUTHORIZED` | 401 | Invalid or missing authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `VALIDATION_ERROR` | 422 | Request validation failed |

## SDKs and Libraries

### Python SDK

```python
from platform_sdk import AgentClient

client = AgentClient(
    api_key="your-api-key",
    base_url="https://api.platform.io/v1"
)

# Create an agent
agent = client.agents.create({
    "name": "my-agent",
    "language": "python",
    "capabilities": ["data-processing"]
})

# Send a message
response = client.agents.send_message(
    agent.id,
    {"action": "process", "data": {"items": [1, 2, 3]}}
)

# Monitor health
health = client.agents.get_health(agent.id)
```

### JavaScript SDK

```javascript
import { AgentClient } from '@platform/sdk';

const client = new AgentClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.platform.io/v1'
});

// Create an agent
const agent = await client.agents.create({
  name: 'my-agent',
  language: 'python',
  capabilities: ['data-processing']
});

// Send a message
const response = await client.agents.sendMessage(agent.id, {
  action: 'process',
  data: { items: [1, 2, 3] }
});

// Monitor metrics
const metrics = await client.agents.getMetrics(agent.id, {
  from: '2024-01-15T10:00:00Z',
  to: '2024-01-15T11:00:00Z'
});
```

## Webhooks

The platform can send webhooks for various agent events:

### Webhook Configuration

```http
POST /webhooks
```

**Request Body:**
```json
{
  "url": "https://your-app.com/webhooks/agent-events",
  "events": [
    "agent.created",
    "agent.started",
    "agent.stopped",
    "agent.error",
    "agent.message.received",
    "agent.health.changed"
  ],
  "secret": "webhook-secret-key",
  "active": true
}
```

### Webhook Payload

```json
{
  "event": "agent.created",
  "timestamp": "2024-01-15T11:00:00Z",
  "data": {
    "agent_id": "agent-123e4567-e89b-12d3-a456-426614174000",
    "name": "data-processor-agent",
    "status": "active",
    "version": "1.0.0"
  },
  "signature": "sha256=abc123..."
}
```

## API Versioning

The API uses versioned URLs:
- Current version: `/v1`
- Beta features: `/v1-beta`
- Deprecated versions: `/v0` (supported until 2024-12-31)

Version compatibility is maintained for at least 12 months after a new version is released.

## Best Practices

1. **Use Idempotency Keys**: For creation operations, include an `Idempotency-Key` header
2. **Implement Retry Logic**: Use exponential backoff for retries
3. **Monitor Rate Limits**: Check rate limit headers and implement proper throttling
4. **Use Webhooks**: For real-time updates instead of polling
5. **Cache Responses**: Cache agent metadata and template information
6. **Handle Errors Gracefully**: Implement proper error handling and fallbacks