# Meta-Agent API Documentation

## Overview

The Meta-Agent API provides access to the platform's core orchestration services. These APIs control the higher-level operations of the platform including workflow management, resource allocation, and system coordination.

## Base URL

```
https://api.platform.io/v1/meta
```

## Authentication

Meta-Agent APIs require elevated permissions and use service-to-service authentication:

```http
Authorization: Bearer <service_token>
X-Service-ID: <service_identifier>
```

## Meta-Agent Services

### 1. Agent Factory API

The Agent Factory service handles dynamic agent creation and management.

#### Generate Agent Code

Generate agent code from a specification.

```http
POST /agent-factory/generate
```

**Request Body:**
```json
{
  "specification": {
    "name": "document-processor",
    "language": "java",
    "capabilities": [
      "document-parsing",
      "text-extraction",
      "content-analysis"
    ],
    "integrations": [
      {
        "type": "storage",
        "provider": "s3",
        "configuration": {
          "bucket": "documents",
          "region": "us-west-2"
        }
      }
    ],
    "ai_models": [
      {
        "provider": "openai",
        "model": "gpt-4",
        "purpose": "content-analysis"
      }
    ]
  },
  "template_version": "2.1.0",
  "optimization_level": "performance"
}
```

**Response (200 OK):**
```json
{
  "generation_id": "gen-*********",
  "status": "generating",
  "estimated_completion": "2024-01-15T11:05:00Z",
  "code_structure": {
    "entry_point": "com.platform.DocumentProcessorAgent",
    "packages": ["com.platform.core", "com.platform.integrations"],
    "dependencies": ["spring-boot", "aws-sdk", "openai-java"],
    "tests": "src/test/java/com/platform/"
  }
}
```

#### Get Generation Status

Check the status of agent code generation.

```http
GET /agent-factory/generate/{generation_id}
```

**Response (200 OK):**
```json
{
  "generation_id": "gen-*********",
  "status": "completed",
  "progress": 100,
  "created_at": "2024-01-15T11:00:00Z",
  "completed_at": "2024-01-15T11:04:30Z",
  "artifacts": {
    "source_code": {
      "url": "https://artifacts.platform.io/gen-*********/source.zip",
      "size": "2.5MB",
      "checksum": "sha256:abc123..."
    },
    "container_image": {
      "url": "registry.platform.io/agents/document-processor:gen-*********",
      "size": "450MB",
      "layers": 12
    },
    "documentation": {
      "url": "https://artifacts.platform.io/gen-*********/docs.zip",
      "size": "1.2MB"
    },
    "tests": {
      "url": "https://artifacts.platform.io/gen-*********/tests.zip",
      "coverage": "95%",
      "test_count": 47
    }
  },
  "validation": {
    "security_scan": "passed",
    "quality_score": 8.7,
    "performance_benchmark": "passed",
    "compliance_check": "passed"
  }
}
```

#### Deploy Generated Agent

Deploy a generated agent to the platform.

```http
POST /agent-factory/deploy
```

**Request Body:**
```json
{
  "generation_id": "gen-*********",
  "deployment_config": {
    "environment": "production",
    "replicas": 3,
    "resources": {
      "cpu": "2",
      "memory": "4Gi",
      "storage": "10Gi"
    },
    "auto_scaling": {
      "enabled": true,
      "min_replicas": 2,
      "max_replicas": 10,
      "target_cpu": 70
    }
  }
}
```

**Response (202 Accepted):**
```json
{
  "deployment_id": "deploy-*********",
  "agent_id": "agent-doc-processor-001",
  "status": "deploying",
  "estimated_completion": "2024-01-15T11:10:00Z"
}
```

### 2. Task Orchestrator API

The Task Orchestrator manages workflow execution and task distribution.

#### Create Workflow

Define a new workflow.

```http
POST /orchestrator/workflows
```

**Request Body:**
```json
{
  "name": "document-processing-pipeline",
  "description": "End-to-end document processing workflow",
  "version": "1.0.0",
  "definition": {
    "nodes": [
      {
        "id": "upload-handler",
        "type": "agent",
        "agent_type": "file-processor",
        "configuration": {
          "supported_formats": ["pdf", "docx", "txt"],
          "max_file_size": "100MB"
        }
      },
      {
        "id": "content-extractor",
        "type": "agent",
        "agent_type": "document-processor",
        "configuration": {
          "extract_text": true,
          "extract_metadata": true,
          "ocr_enabled": true
        }
      },
      {
        "id": "ai-analyzer",
        "type": "agent",
        "agent_type": "content-analyzer",
        "configuration": {
          "analysis_types": ["sentiment", "entities", "topics"],
          "confidence_threshold": 0.8
        }
      },
      {
        "id": "result-store",
        "type": "agent",
        "agent_type": "data-store",
        "configuration": {
          "storage_type": "database",
          "retention_days": 365
        }
      }
    ],
    "edges": [
      {
        "from": "upload-handler",
        "to": "content-extractor",
        "condition": "success"
      },
      {
        "from": "content-extractor",
        "to": "ai-analyzer",
        "condition": "success"
      },
      {
        "from": "ai-analyzer",
        "to": "result-store",
        "condition": "success"
      }
    ],
    "error_handling": {
      "retry_policy": {
        "max_retries": 3,
        "backoff": "exponential"
      },
      "failure_actions": ["log", "alert", "rollback"]
    }
  },
  "triggers": [
    {
      "type": "webhook",
      "path": "/workflows/document-processing/trigger"
    },
    {
      "type": "schedule",
      "cron": "0 9 * * MON-FRI"
    }
  ]
}
```

**Response (201 Created):**
```json
{
  "workflow_id": "workflow-*********",
  "name": "document-processing-pipeline",
  "version": "1.0.0",
  "status": "draft",
  "created_at": "2024-01-15T11:00:00Z",
  "endpoints": {
    "webhook": "https://api.platform.io/v1/workflows/workflow-*********/trigger",
    "execution": "https://api.platform.io/v1/workflows/workflow-*********/execute",
    "status": "https://api.platform.io/v1/workflows/workflow-*********/status"
  }
}
```

#### Execute Workflow

Start workflow execution.

```http
POST /orchestrator/workflows/{workflow_id}/execute
```

**Request Body:**
```json
{
  "input_data": {
    "document_url": "https://storage.example.com/document.pdf",
    "metadata": {
      "source": "email_attachment",
      "user_id": "user-123",
      "priority": "high"
    }
  },
  "execution_options": {
    "timeout": 3600,
    "retry_failed_tasks": true,
    "parallel_execution": true
  },
  "callback_url": "https://your-app.com/webhooks/workflow-complete"
}
```

**Response (202 Accepted):**
```json
{
  "execution_id": "exec-*********",
  "workflow_id": "workflow-*********",
  "status": "running",
  "started_at": "2024-01-15T11:00:00Z",
  "estimated_completion": "2024-01-15T11:15:00Z",
  "progress": {
    "completed_tasks": 0,
    "total_tasks": 4,
    "current_task": "upload-handler"
  }
}
```

#### Get Workflow Execution Status

Monitor workflow execution progress.

```http
GET /orchestrator/executions/{execution_id}
```

**Response (200 OK):**
```json
{
  "execution_id": "exec-*********",
  "workflow_id": "workflow-*********",
  "status": "running",
  "started_at": "2024-01-15T11:00:00Z",
  "last_updated": "2024-01-15T11:05:00Z",
  "progress": {
    "completed_tasks": 2,
    "total_tasks": 4,
    "current_task": "ai-analyzer",
    "percentage": 50
  },
  "task_statuses": [
    {
      "task_id": "upload-handler",
      "status": "completed",
      "started_at": "2024-01-15T11:00:00Z",
      "completed_at": "2024-01-15T11:02:00Z",
      "duration": "2m0s",
      "output": {
        "file_size": "2.5MB",
        "format": "pdf",
        "pages": 15
      }
    },
    {
      "task_id": "content-extractor",
      "status": "completed",
      "started_at": "2024-01-15T11:02:00Z",
      "completed_at": "2024-01-15T11:04:30Z",
      "duration": "2m30s",
      "output": {
        "text_length": 15000,
        "metadata": {
          "author": "John Doe",
          "creation_date": "2024-01-10"
        }
      }
    },
    {
      "task_id": "ai-analyzer",
      "status": "running",
      "started_at": "2024-01-15T11:04:30Z",
      "progress": 75,
      "estimated_completion": "2024-01-15T11:06:00Z"
    },
    {
      "task_id": "result-store",
      "status": "pending"
    }
  ]
}
```

### 3. Resource Manager API

The Resource Manager handles allocation and optimization of computational resources.

#### Get Resource Status

Get current resource utilization and availability.

```http
GET /resource-manager/status
```

**Response (200 OK):**
```json
{
  "cluster_status": {
    "total_nodes": 25,
    "available_nodes": 23,
    "total_capacity": {
      "cpu": "500",
      "memory": "2000Gi",
      "storage": "50Ti",
      "gpu": "20"
    },
    "allocated_capacity": {
      "cpu": "320",
      "memory": "1200Gi",
      "storage": "25Ti",
      "gpu": "12"
    },
    "utilization": {
      "cpu": 64,
      "memory": 60,
      "storage": 50,
      "gpu": 60
    }
  },
  "agent_pools": [
    {
      "name": "java-agents",
      "capacity": {
        "min": 5,
        "max": 50,
        "current": 12
      },
      "utilization": 75,
      "pending_requests": 3
    },
    {
      "name": "python-agents",
      "capacity": {
        "min": 10,
        "max": 100,
        "current": 35
      },
      "utilization": 68,
      "pending_requests": 0
    }
  ]
}
```

#### Request Resource Allocation

Request resources for agent deployment or scaling.

```http
POST /resource-manager/allocate
```

**Request Body:**
```json
{
  "request_id": "req-*********",
  "resource_requirements": {
    "cpu": "8",
    "memory": "16Gi",
    "storage": "100Gi",
    "gpu": "1"
  },
  "constraints": {
    "node_selector": {
      "workload-type": "compute-intensive"
    },
    "affinity": {
      "agent_type": "ml-processor"
    },
    "anti_affinity": {
      "agent_id": "agent-conflict-001"
    }
  },
  "priority": "high",
  "timeout": 300
}
```

**Response (202 Accepted):**
```json
{
  "allocation_id": "alloc-*********",
  "status": "allocating",
  "estimated_completion": "2024-01-15T11:03:00Z",
  "queue_position": 2
}
```

#### Get Allocation Status

Check the status of a resource allocation request.

```http
GET /resource-manager/allocations/{allocation_id}
```

**Response (200 OK):**
```json
{
  "allocation_id": "alloc-*********",
  "status": "allocated",
  "allocated_at": "2024-01-15T11:02:30Z",
  "allocation_details": {
    "node_id": "node-compute-05",
    "pod_name": "agent-ml-processor-abc123",
    "ip_address": "*********",
    "resources": {
      "cpu": "8",
      "memory": "16Gi",
      "storage": "100Gi",
      "gpu": "1"
    }
  },
  "expiry": "2024-01-15T15:02:30Z"
}
```

### 4. Communication Broker API

The Communication Broker manages inter-agent messaging and event distribution.

#### Establish Agent Channel

Create a communication channel for an agent.

```http
POST /communication-broker/channels
```

**Request Body:**
```json
{
  "agent_id": "agent-*********",
  "channel_type": "persistent",
  "capabilities": [
    "request-response",
    "publish-subscribe",
    "streaming"
  ],
  "security": {
    "encryption": "required",
    "authentication": "mutual-tls"
  },
  "qos": {
    "delivery_guarantee": "at-least-once",
    "ordering": "strict",
    "durability": true
  }
}
```

**Response (201 Created):**
```json
{
  "channel_id": "channel-*********",
  "agent_id": "agent-*********",
  "endpoints": {
    "websocket": "wss://comm.platform.io/channels/channel-*********",
    "http": "https://comm.platform.io/v1/channels/channel-*********",
    "grpc": "comm.platform.io:443"
  },
  "credentials": {
    "certificate": "-----BEGIN CERTIFICATE-----\n...",
    "private_key": "-----BEGIN PRIVATE KEY-----\n...",
    "ca_certificate": "-----BEGIN CERTIFICATE-----\n..."
  },
  "created_at": "2024-01-15T11:00:00Z"
}
```

#### Route Message

Route a message between agents.

```http
POST /communication-broker/route
```

**Request Body:**
```json
{
  "from_agent": "agent-source-123",
  "to_agent": "agent-target-456",
  "message": {
    "type": "task_request",
    "priority": "normal",
    "content": {
      "action": "process_data",
      "payload": {
        "data_url": "https://storage.platform.io/dataset-001",
        "processing_type": "analytics"
      }
    }
  },
  "routing_options": {
    "delivery_guarantee": "exactly-once",
    "timeout": 30000,
    "retry_policy": {
      "max_retries": 3,
      "backoff": "exponential"
    }
  }
}
```

**Response (200 OK):**
```json
{
  "message_id": "msg-*********",
  "routing_id": "route-*********",
  "status": "delivered",
  "routed_at": "2024-01-15T11:00:00Z",
  "delivery_receipt": {
    "delivered_at": "2024-01-15T11:00:00.150Z",
    "latency": "150ms",
    "path": ["broker-01", "broker-03", "agent-target-456"]
  }
}
```

### 5. Security Monitor API

The Security Monitor provides security enforcement and threat detection.

#### Get Security Status

Get overall security status of the platform.

```http
GET /security-monitor/status
```

**Response (200 OK):**
```json
{
  "overall_status": "secure",
  "last_assessment": "2024-01-15T11:00:00Z",
  "threat_level": "low",
  "active_policies": 47,
  "security_metrics": {
    "authentication_success_rate": 0.998,
    "authorization_violations": 3,
    "blocked_threats": 12,
    "vulnerability_score": 2.1
  },
  "policy_compliance": {
    "gdpr": "compliant",
    "soc2": "compliant",
    "iso27001": "compliant",
    "hipaa": "not_applicable"
  }
}
```

#### Scan Agent

Perform security scan on an agent.

```http
POST /security-monitor/scan
```

**Request Body:**
```json
{
  "target": {
    "type": "agent",
    "id": "agent-*********"
  },
  "scan_types": [
    "vulnerability",
    "compliance",
    "configuration",
    "behavior"
  ],
  "depth": "thorough",
  "callback_url": "https://your-app.com/webhooks/scan-complete"
}
```

**Response (202 Accepted):**
```json
{
  "scan_id": "scan-*********",
  "status": "scanning",
  "started_at": "2024-01-15T11:00:00Z",
  "estimated_completion": "2024-01-15T11:10:00Z",
  "scan_progress": {
    "vulnerability": "in_progress",
    "compliance": "queued",
    "configuration": "queued",
    "behavior": "queued"
  }
}
```

### 6. Knowledge Base API

The Knowledge Base manages platform learning and knowledge storage.

#### Store Knowledge

Store new knowledge or learning from agent interactions.

```http
POST /knowledge-base/store
```

**Request Body:**
```json
{
  "knowledge_type": "agent_performance",
  "source": {
    "agent_id": "agent-*********",
    "event_type": "task_completion",
    "timestamp": "2024-01-15T11:00:00Z"
  },
  "data": {
    "task_type": "data_processing",
    "input_size": "2.5MB",
    "processing_time": "150ms",
    "cpu_usage": "65%",
    "memory_usage": "2.1Gi",
    "success": true,
    "quality_score": 0.95
  },
  "metadata": {
    "version": "1.0.0",
    "environment": "production",
    "tags": ["performance", "optimization"]
  }
}
```

**Response (201 Created):**
```json
{
  "knowledge_id": "knowledge-*********",
  "stored_at": "2024-01-15T11:00:00Z",
  "indexed": true,
  "embeddings_generated": true
}
```

#### Query Knowledge

Query the knowledge base for insights or recommendations.

```http
POST /knowledge-base/query
```

**Request Body:**
```json
{
  "query": {
    "type": "optimization_recommendation",
    "context": {
      "agent_type": "data_processor",
      "current_performance": {
        "avg_processing_time": "200ms",
        "cpu_usage": "80%",
        "memory_usage": "3.5Gi"
      },
      "workload_pattern": "burst_processing"
    }
  },
  "response_format": "recommendations",
  "max_results": 5
}
```

**Response (200 OK):**
```json
{
  "recommendations": [
    {
      "type": "resource_optimization",
      "confidence": 0.92,
      "recommendation": "Increase memory allocation to 4Gi to reduce GC overhead",
      "expected_improvement": {
        "processing_time": "160ms",
        "cpu_usage": "70%"
      },
      "evidence": [
        {
          "knowledge_id": "knowledge-*********",
          "relevance": 0.95,
          "summary": "Similar agent showed 20% improvement with memory increase"
        }
      ]
    },
    {
      "type": "algorithmic_optimization",
      "confidence": 0.87,
      "recommendation": "Use batch processing for similar workload patterns",
      "expected_improvement": {
        "processing_time": "120ms",
        "throughput": "+40%"
      }
    }
  ],
  "query_metadata": {
    "processed_at": "2024-01-15T11:00:00Z",
    "search_time": "45ms",
    "knowledge_sources": 127
  }
}
```

### 7. Discovery Registry API

The Discovery Registry manages service discovery and agent capability registration.

#### Register Service

Register a new service or agent with the discovery registry.

```http
POST /discovery-registry/services
```

**Request Body:**
```json
{
  "service_id": "agent-doc-processor-001",
  "service_name": "document-processor",
  "service_type": "agent",
  "version": "1.0.0",
  "capabilities": [
    "document-parsing",
    "text-extraction",
    "content-analysis"
  ],
  "endpoints": {
    "primary": "https://agents.platform.io/agent-doc-processor-001",
    "health": "https://agents.platform.io/agent-doc-processor-001/health",
    "metrics": "https://agents.platform.io/agent-doc-processor-001/metrics"
  },
  "metadata": {
    "language": "java",
    "deployment": "production",
    "region": "us-west-2",
    "tags": ["document", "ai", "processing"]
  },
  "health_check": {
    "endpoint": "/health",
    "interval": 30,
    "timeout": 5,
    "retries": 3
  }
}
```

**Response (201 Created):**
```json
{
  "registration_id": "reg-*********",
  "service_id": "agent-doc-processor-001",
  "status": "registered",
  "registered_at": "2024-01-15T11:00:00Z",
  "ttl": 3600,
  "next_renewal": "2024-01-15T12:00:00Z"
}
```

#### Discover Services

Discover services by capability or criteria.

```http
GET /discovery-registry/discover
```

**Query Parameters:**
- `capability` (string): Required capability
- `service_type` (string): Filter by service type
- `region` (string): Filter by region
- `health` (string): Filter by health status
- `version` (string): Filter by version

**Response (200 OK):**
```json
{
  "services": [
    {
      "service_id": "agent-doc-processor-001",
      "service_name": "document-processor",
      "capabilities": ["document-parsing", "text-extraction"],
      "endpoints": {
        "primary": "https://agents.platform.io/agent-doc-processor-001"
      },
      "health": "healthy",
      "load": "medium",
      "response_time": "120ms",
      "availability": 0.999
    },
    {
      "service_id": "agent-doc-processor-002",
      "service_name": "document-processor",
      "capabilities": ["document-parsing", "text-extraction"],
      "endpoints": {
        "primary": "https://agents.platform.io/agent-doc-processor-002"
      },
      "health": "healthy",
      "load": "low",
      "response_time": "95ms",
      "availability": 0.998
    }
  ],
  "total_count": 2,
  "response_time": "15ms"
}
```

## Error Handling

Meta-Agent API errors include additional context for debugging:

```json
{
  "error": {
    "code": "RESOURCE_ALLOCATION_FAILED",
    "message": "Insufficient cluster capacity for requested resources",
    "details": {
      "requested": {"cpu": "8", "memory": "16Gi"},
      "available": {"cpu": "4", "memory": "8Gi"},
      "cluster_utilization": 0.85,
      "estimated_wait_time": "5m"
    },
    "suggestions": [
      "Reduce resource requirements",
      "Wait for cluster auto-scaling",
      "Use spot instances"
    ],
    "request_id": "req-*********"
  }
}
```

## Rate Limiting

Meta-Agent APIs have different rate limits based on the operation:

- **Resource operations**: 100 requests per hour
- **Query operations**: 1000 requests per hour
- **Monitoring operations**: 500 requests per hour

## Best Practices

1. **Use Service Tokens**: Always use service-specific authentication
2. **Monitor Resource Usage**: Track allocation efficiency and usage patterns
3. **Implement Circuit Breakers**: Handle service failures gracefully
4. **Cache Discovery Results**: Cache service discovery results to reduce latency
5. **Use Health Checks**: Implement proper health checking for all services
6. **Handle Async Operations**: Use webhooks for long-running operations