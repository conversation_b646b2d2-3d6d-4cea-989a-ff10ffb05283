package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"github.com/ai-platform/platform/data-service/internal/config"
	"github.com/ai-platform/platform/data-service/internal/server"
	"github.com/ai-platform/platform/data-service/internal/storage"
)

var (
	configPath string
	version    = "1.0.0"
	buildTime  = "unknown"
	gitCommit  = "unknown"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "data-service",
		Short: "AI Platform Data Service",
		Long:  "Data abstraction layer for the AI-Native Agent Platform",
		Run:   runServer,
	}

	rootCmd.Flags().StringVarP(&configPath, "config", "c", "", "Path to configuration file")
	rootCmd.AddCommand(versionCmd, migrateCmd, healthCmd)

	if err := rootCmd.Execute(); err != nil {
		log.Fatal(err)
	}
}

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print version information",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("AI Platform Data Service\n")
		fmt.Printf("Version: %s\n", version)
		fmt.Printf("Build Time: %s\n", buildTime)
		fmt.Printf("Git Commit: %s\n", gitCommit)
	},
}

var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "Run database migrations",
	Run:   runMigrations,
}

var healthCmd = &cobra.Command{
	Use:   "health",
	Short: "Check service health",
	Run:   checkHealth,
}

func runServer(cmd *cobra.Command, args []string) {
	// Load configuration
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	if err := cfg.Validate(); err != nil {
		log.Fatalf("Invalid configuration: %v", err)
	}

	// Initialize logger
	logger, err := initLogger(cfg.Logging)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger.Info("Starting AI Platform Data Service",
		zap.String("version", version),
		zap.String("build_time", buildTime),
		zap.String("git_commit", gitCommit),
	)

	// Initialize storage
	ctx := context.Background()
	storageManager, err := initStorage(ctx, cfg, logger)
	if err != nil {
		logger.Fatal("Failed to initialize storage", zap.Error(err))
	}
	defer storageManager.Close()

	// Health check
	healthResults := storageManager.Health(ctx)
	for name, err := range healthResults {
		if err != nil {
			logger.Fatal("Storage health check failed",
				zap.String("storage", name),
				zap.Error(err),
			)
		} else {
			logger.Info("Storage health check passed", zap.String("storage", name))
		}
	}

	// Initialize HTTP server
	srv := server.New(cfg, storageManager, logger)

	// Start server
	go func() {
		addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
		logger.Info("Starting HTTP server", zap.String("address", addr))
		
		if err := srv.Start(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("Server forced to shutdown", zap.Error(err))
	} else {
		logger.Info("Server shutdown complete")
	}
}

func runMigrations(cmd *cobra.Command, args []string) {
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	logger, err := initLogger(cfg.Logging)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	// Run migrations
	logger.Info("Running database migrations...")
	
	// TODO: Implement migration runner
	logger.Info("Migration runner not yet implemented")
}

func checkHealth(cmd *cobra.Command, args []string) {
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	logger, err := initLogger(cfg.Logging)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Initialize storage for health check
	storageManager, err := initStorage(ctx, cfg, logger)
	if err != nil {
		logger.Fatal("Failed to initialize storage", zap.Error(err))
	}
	defer storageManager.Close()

	// Perform health checks
	healthResults := storageManager.Health(ctx)
	allHealthy := true

	for name, err := range healthResults {
		if err != nil {
			logger.Error("Health check failed", zap.String("storage", name), zap.Error(err))
			allHealthy = false
		} else {
			logger.Info("Health check passed", zap.String("storage", name))
		}
	}

	if allHealthy {
		logger.Info("All health checks passed")
		os.Exit(0)
	} else {
		logger.Error("Some health checks failed")
		os.Exit(1)
	}
}

func initLogger(cfg config.LoggingConfig) (*zap.Logger, error) {
	var logger *zap.Logger
	var err error

	if cfg.Format == "console" {
		config := zap.NewDevelopmentConfig()
		config.Level = zap.NewAtomicLevelAt(parseLogLevel(cfg.Level))
		logger, err = config.Build()
	} else {
		config := zap.NewProductionConfig()
		config.Level = zap.NewAtomicLevelAt(parseLogLevel(cfg.Level))
		logger, err = config.Build()
	}

	return logger, err
}

func parseLogLevel(level string) zap.Level {
	switch level {
	case "debug":
		return zap.DebugLevel
	case "info":
		return zap.InfoLevel
	case "warn":
		return zap.WarnLevel
	case "error":
		return zap.ErrorLevel
	default:
		return zap.InfoLevel
	}
}

func initStorage(ctx context.Context, cfg *config.Config, logger *zap.Logger) (*storage.StorageManager, error) {
	// Initialize PostgreSQL
	pgStorage, err := storage.NewPostgreSQLStorage(ctx, cfg.Database, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize PostgreSQL: %w", err)
	}

	// Initialize Redis
	redisStorage, err := storage.NewRedisStorage(ctx, cfg.Redis, logger)
	if err != nil {
		pgStorage.Close()
		return nil, fmt.Errorf("failed to initialize Redis: %w", err)
	}

	// Initialize MongoDB (optional)
	var mongoStorage storage.DocumentStorage
	// mongoStorage, err := storage.NewMongoStorage(ctx, cfg.MongoDB, logger)
	// if err != nil {
	//     logger.Warn("Failed to initialize MongoDB", zap.Error(err))
	// }

	return storage.NewStorageManager(pgStorage, redisStorage, mongoStorage), nil
}