server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  host: "${DB_HOST:localhost}"
  port: "${DB_PORT:5432}"
  database: "${DB_NAME:platform}"
  username: "${DB_USER:platform}"
  password: "${DB_PASSWORD}"
  ssl_mode: "${DB_SSL_MODE:require}"
  max_connections: 25
  min_connections: 5
  connect_timeout: "30s"
  max_conn_lifetime: "1h"
  max_conn_idle_time: "15m"

redis:
  host: "${REDIS_HOST:localhost}"
  port: "${REDIS_PORT:6379}"
  password: "${REDIS_PASSWORD:}"
  database: "${REDIS_DB:0}"
  max_retries: 3
  pool_size: 20
  min_idle_conn: 5
  dial_timeout: "5s"

mongodb:
  uri: "${MONGODB_URI:mongodb://localhost:27017}"
  database: "${MONGODB_DB:platform}"
  max_pool_size: 100
  min_pool_size: 10
  connect_timeout: "10s"
  server_timeout: "30s"

logging:
  level: "${LOG_LEVEL:info}"
  format: "json"

metrics:
  enabled: true
  port: 9090
  path: "/metrics"