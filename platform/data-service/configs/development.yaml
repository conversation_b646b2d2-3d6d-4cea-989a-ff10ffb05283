server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  host: "localhost"
  port: 5432
  database: "platform_dev"
  username: "platform"
  password: "platform123"
  ssl_mode: "disable"
  max_connections: 10
  min_connections: 2
  connect_timeout: "30s"
  max_conn_lifetime: "1h"
  max_conn_idle_time: "15m"

redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  max_retries: 3
  pool_size: 10
  min_idle_conn: 2
  dial_timeout: "5s"

mongodb:
  uri: "mongodb://localhost:27017"
  database: "platform_dev"
  max_pool_size: 50
  min_pool_size: 5
  connect_timeout: "10s"
  server_timeout: "30s"

logging:
  level: "debug"
  format: "console"

metrics:
  enabled: true
  port: 9090
  path: "/metrics"