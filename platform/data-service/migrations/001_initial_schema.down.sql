-- Rollback initial schema

-- Drop triggers
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
DROP TRIGGER IF EXISTS update_workflow_executions_updated_at ON workflow_executions;
DROP TRIGGER IF EXISTS update_workflows_updated_at ON workflows;
DROP TRIGGER IF EXISTS update_agents_updated_at ON agents;

-- Drop function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_metrics_labels;
DROP INDEX IF EXISTS idx_metrics_source;
DROP INDEX IF EXISTS idx_metrics_timestamp;
DROP INDEX IF EXISTS idx_metrics_type;
DROP INDEX IF EXISTS idx_metrics_name;

DROP INDEX IF EXISTS idx_tasks_created_at;
DROP INDEX IF EXISTS idx_tasks_assigned_agent;
DROP INDEX IF EXISTS idx_tasks_status;
DROP INDEX IF EXISTS idx_tasks_execution_id;
DROP INDEX IF EXISTS idx_tasks_workflow_id;

DROP INDEX IF EXISTS idx_workflow_executions_created_at;
DROP INDEX IF EXISTS idx_workflow_executions_status;
DROP INDEX IF EXISTS idx_workflow_executions_workflow_id;

DROP INDEX IF EXISTS idx_workflows_created_at;
DROP INDEX IF EXISTS idx_workflows_name;
DROP INDEX IF EXISTS idx_workflows_status;

DROP INDEX IF EXISTS idx_agents_created_at;
DROP INDEX IF EXISTS idx_agents_language;
DROP INDEX IF EXISTS idx_agents_status;

-- Drop tables (in reverse order of dependencies)
DROP TABLE IF EXISTS metrics;
DROP TABLE IF EXISTS tasks;
DROP TABLE IF EXISTS workflow_executions;
DROP TABLE IF EXISTS workflows;
DROP TABLE IF EXISTS agents;

-- Drop extensions (only if safe to do so)
-- DROP EXTENSION IF EXISTS "uuid-ossp";