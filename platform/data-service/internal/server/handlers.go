package server

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/ai-platform/platform/data-service/internal/storage"
)

// Health check handlers

func (s *Server) handleHealth(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	healthResults := s.storage.Health(ctx)
	allHealthy := true
	
	for _, err := range healthResults {
		if err != nil {
			allHealthy = false
			break
		}
	}

	status := "healthy"
	httpStatus := http.StatusOK
	
	if !allHealthy {
		status = "unhealthy"
		httpStatus = http.StatusServiceUnavailable
	}

	response := gin.H{
		"status":    status,
		"timestamp": time.Now().UTC(),
		"service":   "data-service",
		"version":   "1.0.0",
		"checks":    healthResults,
	}

	c.<PERSON>(httpStatus, response)
}

func (s *Server) handleReady(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{
		"status": "ready",
		"timestamp": time.Now().UTC(),
	})
}

// Agent handlers

func (s *Server) handleListAgents(c *gin.Context) {
	filter := storage.AgentFilter{
		Status:   c.Query("status"),
		Language: c.Query("language"),
		Limit:    parseIntQuery(c, "limit", 50),
		Offset:   parseIntQuery(c, "offset", 0),
	}

	agents, err := s.storage.Primary.ListAgents(c.Request.Context(), filter)
	if err != nil {
		s.logger.Error("Failed to list agents", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list agents"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"agents": agents,
		"filter": filter,
	})
}

func (s *Server) handleCreateAgent(c *gin.Context) {
	var agent storage.Agent
	if err := c.ShouldBindJSON(&agent); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if agent.ID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	err := s.storage.Primary.CreateAgent(c.Request.Context(), &agent)
	if err != nil {
		s.logger.Error("Failed to create agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create agent"})
		return
	}

	s.metrics.StorageOps.WithLabelValues("create", "postgresql", "success").Inc()
	c.JSON(http.StatusCreated, agent)
}

func (s *Server) handleGetAgent(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	agent, err := s.storage.Primary.GetAgent(c.Request.Context(), id)
	if err != nil {
		if err == storage.ErrAgentNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Agent not found"})
			return
		}
		s.logger.Error("Failed to get agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get agent"})
		return
	}

	s.metrics.StorageOps.WithLabelValues("get", "postgresql", "success").Inc()
	c.JSON(http.StatusOK, agent)
}

func (s *Server) handleUpdateAgent(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	var agent storage.Agent
	if err := c.ShouldBindJSON(&agent); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	agent.ID = id
	err := s.storage.Primary.UpdateAgent(c.Request.Context(), &agent)
	if err != nil {
		if err == storage.ErrAgentNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Agent not found"})
			return
		}
		s.logger.Error("Failed to update agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update agent"})
		return
	}

	s.metrics.StorageOps.WithLabelValues("update", "postgresql", "success").Inc()
	c.JSON(http.StatusOK, agent)
}

func (s *Server) handleDeleteAgent(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	err := s.storage.Primary.DeleteAgent(c.Request.Context(), id)
	if err != nil {
		if err == storage.ErrAgentNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Agent not found"})
			return
		}
		s.logger.Error("Failed to delete agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete agent"})
		return
	}

	s.metrics.StorageOps.WithLabelValues("delete", "postgresql", "success").Inc()
	c.JSON(http.StatusOK, gin.H{"message": "Agent deleted successfully"})
}

// Workflow handlers (simplified implementations)

func (s *Server) handleListWorkflows(c *gin.Context) {
	filter := storage.WorkflowFilter{
		Status:  c.Query("status"),
		Version: c.Query("version"),
		Limit:   parseIntQuery(c, "limit", 50),
		Offset:  parseIntQuery(c, "offset", 0),
	}

	workflows, err := s.storage.Primary.ListWorkflows(c.Request.Context(), filter)
	if err != nil {
		s.logger.Error("Failed to list workflows", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list workflows"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"workflows": workflows,
		"filter":    filter,
	})
}

func (s *Server) handleCreateWorkflow(c *gin.Context) {
	var workflow storage.Workflow
	if err := c.ShouldBindJSON(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if workflow.ID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	err := s.storage.Primary.CreateWorkflow(c.Request.Context(), &workflow)
	if err != nil {
		s.logger.Error("Failed to create workflow", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create workflow"})
		return
	}

	s.metrics.StorageOps.WithLabelValues("create", "postgresql", "success").Inc()
	c.JSON(http.StatusCreated, workflow)
}

func (s *Server) handleGetWorkflow(c *gin.Context) {
	id := c.Param("id")
	workflow, err := s.storage.Primary.GetWorkflow(c.Request.Context(), id)
	if err != nil {
		if err == storage.ErrWorkflowNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workflow not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get workflow"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

func (s *Server) handleUpdateWorkflow(c *gin.Context) {
	id := c.Param("id")
	var workflow storage.Workflow
	if err := c.ShouldBindJSON(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	workflow.ID = id
	err := s.storage.Primary.UpdateWorkflow(c.Request.Context(), &workflow)
	if err != nil {
		if err == storage.ErrWorkflowNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workflow not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update workflow"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

func (s *Server) handleDeleteWorkflow(c *gin.Context) {
	id := c.Param("id")
	err := s.storage.Primary.DeleteWorkflow(c.Request.Context(), id)
	if err != nil {
		if err == storage.ErrWorkflowNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workflow not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete workflow"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Workflow deleted successfully"})
}

// Execution handlers (placeholders)

func (s *Server) handleListExecutions(c *gin.Context) {
	// TODO: Implement execution listing
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleCreateExecution(c *gin.Context) {
	// TODO: Implement execution creation
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleGetExecution(c *gin.Context) {
	// TODO: Implement execution retrieval
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleUpdateExecution(c *gin.Context) {
	// TODO: Implement execution update
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

// Task handlers (placeholders)

func (s *Server) handleListTasks(c *gin.Context) {
	// TODO: Implement task listing
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleCreateTask(c *gin.Context) {
	// TODO: Implement task creation
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleGetTask(c *gin.Context) {
	// TODO: Implement task retrieval
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (s *Server) handleUpdateTask(c *gin.Context) {
	// TODO: Implement task update
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

// Metrics handlers

func (s *Server) handleStoreMetrics(c *gin.Context) {
	var metrics []storage.Metric
	if err := c.ShouldBindJSON(&metrics); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	for _, metric := range metrics {
		if err := s.storage.Primary.StoreMetric(c.Request.Context(), &metric); err != nil {
			s.logger.Error("Failed to store metric", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store metrics"})
			return
		}
	}

	s.metrics.StorageOps.WithLabelValues("store", "postgresql", "success").Inc()
	c.JSON(http.StatusCreated, gin.H{
		"message": "Metrics stored successfully",
		"count":   len(metrics),
	})
}

func (s *Server) handleGetMetrics(c *gin.Context) {
	filter := storage.MetricFilter{
		Name:   c.Query("name"),
		Type:   c.Query("type"),
		Source: c.Query("source"),
		Limit:  parseIntQuery(c, "limit", 100),
		Offset: parseIntQuery(c, "offset", 0),
	}

	metrics, err := s.storage.Primary.GetMetrics(c.Request.Context(), filter)
	if err != nil {
		s.logger.Error("Failed to get metrics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"filter":  filter,
	})
}

// Helper functions

func parseIntQuery(c *gin.Context, key string, defaultValue int) int {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}

	return value
}