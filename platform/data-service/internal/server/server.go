package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"

	"github.com/ai-platform/platform/data-service/internal/config"
	"github.com/ai-platform/platform/data-service/internal/storage"
)

// Server represents the HTTP server
type Server struct {
	config         *config.Config
	storage        *storage.StorageManager
	logger         *zap.Logger
	router         *gin.Engine
	httpServer     *http.Server
	metricsServer  *http.Server
	metrics        *Metrics
}

// Metrics holds Prometheus metrics
type Metrics struct {
	RequestDuration *prometheus.HistogramVec
	RequestCount    *prometheus.CounterVec
	ErrorCount      *prometheus.CounterVec
	StorageOps      *prometheus.CounterVec
}

// New creates a new server instance
func New(cfg *config.Config, storage *storage.StorageManager, logger *zap.Logger) *Server {
	if cfg.Logging.Format == "console" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	metrics := initMetrics()

	server := &Server{
		config:  cfg,
		storage: storage,
		logger:  logger,
		router:  router,
		metrics: metrics,
	}

	server.setupMiddleware()
	server.setupRoutes()

	server.httpServer = &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Setup metrics server if enabled
	if cfg.Metrics.Enabled {
		server.setupMetricsServer()
	}

	return server
}

// Start starts the HTTP server
func (s *Server) Start() error {
	// Start metrics server if enabled
	if s.metricsServer != nil {
		go func() {
			addr := fmt.Sprintf(":%d", s.config.Metrics.Port)
			s.logger.Info("Starting metrics server", zap.String("address", addr))
			if err := s.metricsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				s.logger.Error("Metrics server error", zap.Error(err))
			}
		}()
	}

	return s.httpServer.ListenAndServe()
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	// Shutdown metrics server
	if s.metricsServer != nil {
		if err := s.metricsServer.Shutdown(ctx); err != nil {
			s.logger.Error("Failed to shutdown metrics server", zap.Error(err))
		}
	}

	// Shutdown main server
	return s.httpServer.Shutdown(ctx)
}

func (s *Server) setupMiddleware() {
	// Logging middleware
	s.router.Use(gin.LoggerWithWriter(gin.DefaultWriter, "/health", "/metrics"))
	
	// Recovery middleware
	s.router.Use(gin.Recovery())

	// CORS middleware
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Metrics middleware
	s.router.Use(s.metricsMiddleware())
}

func (s *Server) setupRoutes() {
	// Health check
	s.router.GET("/health", s.handleHealth)
	s.router.GET("/ready", s.handleReady)

	// API routes
	api := s.router.Group("/api/v1")
	{
		// Agent routes
		agents := api.Group("/agents")
		{
			agents.GET("", s.handleListAgents)
			agents.POST("", s.handleCreateAgent)
			agents.GET("/:id", s.handleGetAgent)
			agents.PUT("/:id", s.handleUpdateAgent)
			agents.DELETE("/:id", s.handleDeleteAgent)
		}

		// Workflow routes
		workflows := api.Group("/workflows")
		{
			workflows.GET("", s.handleListWorkflows)
			workflows.POST("", s.handleCreateWorkflow)
			workflows.GET("/:id", s.handleGetWorkflow)
			workflows.PUT("/:id", s.handleUpdateWorkflow)
			workflows.DELETE("/:id", s.handleDeleteWorkflow)
		}

		// Execution routes
		executions := api.Group("/executions")
		{
			executions.GET("", s.handleListExecutions)
			executions.POST("", s.handleCreateExecution)
			executions.GET("/:id", s.handleGetExecution)
			executions.PUT("/:id", s.handleUpdateExecution)
		}

		// Task routes
		tasks := api.Group("/tasks")
		{
			tasks.GET("", s.handleListTasks)
			tasks.POST("", s.handleCreateTask)
			tasks.GET("/:id", s.handleGetTask)
			tasks.PUT("/:id", s.handleUpdateTask)
		}

		// Metrics routes
		metricsAPI := api.Group("/metrics")
		{
			metricsAPI.POST("", s.handleStoreMetrics)
			metricsAPI.GET("", s.handleGetMetrics)
		}
	}
}

func (s *Server) setupMetricsServer() {
	mux := http.NewServeMux()
	mux.Handle(s.config.Metrics.Path, promhttp.Handler())

	s.metricsServer = &http.Server{
		Addr:    fmt.Sprintf(":%d", s.config.Metrics.Port),
		Handler: mux,
	}
}

func (s *Server) metricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)
		status := fmt.Sprintf("%d", c.Writer.Status())

		s.metrics.RequestDuration.WithLabelValues(c.Request.Method, c.FullPath()).Observe(duration.Seconds())
		s.metrics.RequestCount.WithLabelValues(c.Request.Method, c.FullPath(), status).Inc()

		if c.Writer.Status() >= 400 {
			s.metrics.ErrorCount.WithLabelValues(c.Request.Method, c.FullPath(), status).Inc()
		}
	}
}

func initMetrics() *Metrics {
	requestDuration := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "path"},
	)

	requestCount := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "path", "status"},
	)

	errorCount := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_errors_total",
			Help: "Total number of HTTP errors",
		},
		[]string{"method", "path", "status"},
	)

	storageOps := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "storage_operations_total",
			Help: "Total number of storage operations",
		},
		[]string{"operation", "storage", "status"},
	)

	prometheus.MustRegister(requestDuration, requestCount, errorCount, storageOps)

	return &Metrics{
		RequestDuration: requestDuration,
		RequestCount:    requestCount,
		ErrorCount:      errorCount,
		StorageOps:      storageOps,
	}
}