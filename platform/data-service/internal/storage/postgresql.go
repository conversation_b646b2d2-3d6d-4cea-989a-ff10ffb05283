package storage

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"github.com/ai-platform/platform/data-service/internal/config"
)

// PostgreSQLStorage implements storage interface for PostgreSQL
type PostgreSQLStorage struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewPostgreSQLStorage creates a new PostgreSQL storage instance
func NewPostgreSQLStorage(ctx context.Context, cfg config.DatabaseConfig, logger *zap.Logger) (*PostgreSQLStorage, error) {
	// Build connection string
	connStr := fmt.Sprintf(
		"host=%s port=%d dbname=%s user=%s password=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.Database, cfg.Username, cfg.Password, cfg.SSLMode,
	)
	
	// Parse connection string to get pool config
	poolConfig, err := pgxpool.ParseConfig(connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse connection string: %w", err)
	}
	
	// Configure connection pool
	poolConfig.MaxConns = int32(cfg.MaxConnections)
	poolConfig.MinConns = int32(cfg.MinConnections)
	poolConfig.MaxConnLifetime = cfg.MaxConnLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnIdleTime
	poolConfig.ConnConfig.ConnectTimeout = cfg.ConnectTimeout
	
	// Create connection pool
	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}
	
	// Test connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}
	
	logger.Info("PostgreSQL connection established",
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("database", cfg.Database),
		zap.Int("max_connections", cfg.MaxConnections),
	)
	
	return &PostgreSQLStorage{
		pool:   pool,
		logger: logger,
	}, nil
}

// Close closes the database connection pool
func (s *PostgreSQLStorage) Close() {
	if s.pool != nil {
		s.pool.Close()
		s.logger.Info("PostgreSQL connection pool closed")
	}
}

// Health checks the database health
func (s *PostgreSQLStorage) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	
	return s.pool.Ping(ctx)
}

// GetPool returns the underlying connection pool
func (s *PostgreSQLStorage) GetPool() *pgxpool.Pool {
	return s.pool
}

// Agent storage operations

// CreateAgent creates a new agent record
func (s *PostgreSQLStorage) CreateAgent(ctx context.Context, agent *Agent) error {
	query := `
		INSERT INTO agents (id, name, description, language, capabilities, status, configuration, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`
	
	now := time.Now()
	_, err := s.pool.Exec(ctx, query,
		agent.ID,
		agent.Name,
		agent.Description,
		agent.Language,
		agent.Capabilities,
		agent.Status,
		agent.Configuration,
		now,
		now,
	)
	
	if err != nil {
		s.logger.Error("Failed to create agent", zap.Error(err), zap.String("agent_id", agent.ID))
		return fmt.Errorf("failed to create agent: %w", err)
	}
	
	agent.CreatedAt = now
	agent.UpdatedAt = now
	
	s.logger.Info("Agent created", zap.String("agent_id", agent.ID), zap.String("name", agent.Name))
	return nil
}

// GetAgent retrieves an agent by ID
func (s *PostgreSQLStorage) GetAgent(ctx context.Context, id string) (*Agent, error) {
	query := `
		SELECT id, name, description, language, capabilities, status, configuration, created_at, updated_at
		FROM agents WHERE id = $1
	`
	
	row := s.pool.QueryRow(ctx, query, id)
	
	var agent Agent
	err := row.Scan(
		&agent.ID,
		&agent.Name,
		&agent.Description,
		&agent.Language,
		&agent.Capabilities,
		&agent.Status,
		&agent.Configuration,
		&agent.CreatedAt,
		&agent.UpdatedAt,
	)
	
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, ErrAgentNotFound
		}
		s.logger.Error("Failed to get agent", zap.Error(err), zap.String("agent_id", id))
		return nil, fmt.Errorf("failed to get agent: %w", err)
	}
	
	return &agent, nil
}

// UpdateAgent updates an existing agent
func (s *PostgreSQLStorage) UpdateAgent(ctx context.Context, agent *Agent) error {
	query := `
		UPDATE agents 
		SET name = $2, description = $3, language = $4, capabilities = $5, 
		    status = $6, configuration = $7, updated_at = $8
		WHERE id = $1
	`
	
	now := time.Now()
	result, err := s.pool.Exec(ctx, query,
		agent.ID,
		agent.Name,
		agent.Description,
		agent.Language,
		agent.Capabilities,
		agent.Status,
		agent.Configuration,
		now,
	)
	
	if err != nil {
		s.logger.Error("Failed to update agent", zap.Error(err), zap.String("agent_id", agent.ID))
		return fmt.Errorf("failed to update agent: %w", err)
	}
	
	if result.RowsAffected() == 0 {
		return ErrAgentNotFound
	}
	
	agent.UpdatedAt = now
	
	s.logger.Info("Agent updated", zap.String("agent_id", agent.ID))
	return nil
}

// DeleteAgent deletes an agent by ID
func (s *PostgreSQLStorage) DeleteAgent(ctx context.Context, id string) error {
	query := `DELETE FROM agents WHERE id = $1`
	
	result, err := s.pool.Exec(ctx, query, id)
	if err != nil {
		s.logger.Error("Failed to delete agent", zap.Error(err), zap.String("agent_id", id))
		return fmt.Errorf("failed to delete agent: %w", err)
	}
	
	if result.RowsAffected() == 0 {
		return ErrAgentNotFound
	}
	
	s.logger.Info("Agent deleted", zap.String("agent_id", id))
	return nil
}

// ListAgents lists agents with optional filtering
func (s *PostgreSQLStorage) ListAgents(ctx context.Context, filter AgentFilter) ([]*Agent, error) {
	query := `
		SELECT id, name, description, language, capabilities, status, configuration, created_at, updated_at
		FROM agents
		WHERE ($1 = '' OR status = $1)
		AND ($2 = '' OR language = $2)
		ORDER BY created_at DESC
		LIMIT $3 OFFSET $4
	`
	
	rows, err := s.pool.Query(ctx, query, filter.Status, filter.Language, filter.Limit, filter.Offset)
	if err != nil {
		s.logger.Error("Failed to list agents", zap.Error(err))
		return nil, fmt.Errorf("failed to list agents: %w", err)
	}
	defer rows.Close()
	
	var agents []*Agent
	for rows.Next() {
		var agent Agent
		err := rows.Scan(
			&agent.ID,
			&agent.Name,
			&agent.Description,
			&agent.Language,
			&agent.Capabilities,
			&agent.Status,
			&agent.Configuration,
			&agent.CreatedAt,
			&agent.UpdatedAt,
		)
		if err != nil {
			s.logger.Error("Failed to scan agent row", zap.Error(err))
			return nil, fmt.Errorf("failed to scan agent: %w", err)
		}
		agents = append(agents, &agent)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating agent rows: %w", err)
	}
	
	return agents, nil
}

// Transaction support
func (s *PostgreSQLStorage) WithTransaction(ctx context.Context, fn func(tx pgx.Tx) error) error {
	return s.pool.BeginFunc(ctx, func(tx pgx.Tx) error {
		return fn(tx)
	})
}

// GetStats returns database statistics
func (s *PostgreSQLStorage) GetStats() map[string]interface{} {
	stats := s.pool.Stat()
	return map[string]interface{}{
		"total_connections":     stats.TotalConns(),
		"acquired_connections":  stats.AcquiredConns(),
		"idle_connections":      stats.IdleConns(),
		"constructed_connections": stats.ConstructingConns(),
		"max_connections":       stats.MaxConns(),
		"acquire_count":         stats.AcquireCount(),
		"acquire_duration":      stats.AcquireDuration().String(),
	}
}