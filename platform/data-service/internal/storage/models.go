package storage

import (
	"errors"
	"time"
)

// Common errors
var (
	ErrAgentNotFound    = errors.New("agent not found")
	ErrWorkflowNotFound = errors.New("workflow not found")
	ErrTaskNotFound     = errors.New("task not found")
	ErrDuplicateID      = errors.New("duplicate ID")
)

// Agent represents an agent in the system
type Agent struct {
	ID            string                 `json:"id" db:"id"`
	Name          string                 `json:"name" db:"name"`
	Description   string                 `json:"description" db:"description"`
	Language      string                 `json:"language" db:"language"`
	Capabilities  []string               `json:"capabilities" db:"capabilities"`
	Status        string                 `json:"status" db:"status"`
	Configuration map[string]interface{} `json:"configuration" db:"configuration"`
	CreatedAt     time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at" db:"updated_at"`
}

// Workflow represents a workflow definition
type Workflow struct {
	ID          string                 `json:"id" db:"id"`
	Name        string                 `json:"name" db:"name"`
	Description string                 `json:"description" db:"description"`
	Version     string                 `json:"version" db:"version"`
	Status      string                 `json:"status" db:"status"`
	Definition  map[string]interface{} `json:"definition" db:"definition"`
	Triggers    []WorkflowTrigger      `json:"triggers" db:"triggers"`
	CreatedAt   time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" db:"updated_at"`
}

// WorkflowTrigger represents a workflow trigger
type WorkflowTrigger struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Configuration map[string]interface{} `json:"configuration"`
	Enabled       bool                   `json:"enabled"`
}

// WorkflowExecution represents a workflow execution instance
type WorkflowExecution struct {
	ID         string                 `json:"id" db:"id"`
	WorkflowID string                 `json:"workflow_id" db:"workflow_id"`
	Status     string                 `json:"status" db:"status"`
	InputData  map[string]interface{} `json:"input_data" db:"input_data"`
	OutputData map[string]interface{} `json:"output_data" db:"output_data"`
	Progress   ExecutionProgress      `json:"progress" db:"progress"`
	StartedAt  time.Time              `json:"started_at" db:"started_at"`
	CompletedAt *time.Time            `json:"completed_at" db:"completed_at"`
	CreatedAt  time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at" db:"updated_at"`
}

// ExecutionProgress represents workflow execution progress
type ExecutionProgress struct {
	CompletedTasks int     `json:"completed_tasks"`
	TotalTasks     int     `json:"total_tasks"`
	CurrentTask    string  `json:"current_task"`
	Percentage     float64 `json:"percentage"`
}

// Task represents a task within a workflow
type Task struct {
	ID            string                 `json:"id" db:"id"`
	WorkflowID    string                 `json:"workflow_id" db:"workflow_id"`
	ExecutionID   string                 `json:"execution_id" db:"execution_id"`
	NodeID        string                 `json:"node_id" db:"node_id"`
	Status        string                 `json:"status" db:"status"`
	AssignedAgent string                 `json:"assigned_agent" db:"assigned_agent"`
	Input         map[string]interface{} `json:"input" db:"input"`
	Output        map[string]interface{} `json:"output" db:"output"`
	Error         string                 `json:"error" db:"error"`
	RetryCount    int                    `json:"retry_count" db:"retry_count"`
	StartedAt     *time.Time             `json:"started_at" db:"started_at"`
	CompletedAt   *time.Time             `json:"completed_at" db:"completed_at"`
	CreatedAt     time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at" db:"updated_at"`
}

// Metric represents a system metric
type Metric struct {
	ID        string                 `json:"id" db:"id"`
	Name      string                 `json:"name" db:"name"`
	Type      string                 `json:"type" db:"type"`
	Value     float64                `json:"value" db:"value"`
	Labels    map[string]string      `json:"labels" db:"labels"`
	Timestamp time.Time              `json:"timestamp" db:"timestamp"`
	Source    string                 `json:"source" db:"source"`
}

// Filter types for queries
type AgentFilter struct {
	Status   string `json:"status"`
	Language string `json:"language"`
	Limit    int    `json:"limit"`
	Offset   int    `json:"offset"`
}

type WorkflowFilter struct {
	Status  string `json:"status"`
	Version string `json:"version"`
	Limit   int    `json:"limit"`
	Offset  int    `json:"offset"`
}

type ExecutionFilter struct {
	WorkflowID string     `json:"workflow_id"`
	Status     string     `json:"status"`
	From       *time.Time `json:"from"`
	To         *time.Time `json:"to"`
	Limit      int        `json:"limit"`
	Offset     int        `json:"offset"`
}

type TaskFilter struct {
	WorkflowID    string `json:"workflow_id"`
	ExecutionID   string `json:"execution_id"`
	Status        string `json:"status"`
	AssignedAgent string `json:"assigned_agent"`
	Limit         int    `json:"limit"`
	Offset        int    `json:"offset"`
}

type MetricFilter struct {
	Name   string     `json:"name"`
	Type   string     `json:"type"`
	Source string     `json:"source"`
	From   *time.Time `json:"from"`
	To     *time.Time `json:"to"`
	Limit  int        `json:"limit"`
	Offset int        `json:"offset"`
}

// Constants for status values
const (
	// Agent statuses
	AgentStatusCreating = "creating"
	AgentStatusActive   = "active"
	AgentStatusBusy     = "busy"
	AgentStatusStopped  = "stopped"
	AgentStatusError    = "error"

	// Workflow statuses
	WorkflowStatusDraft    = "draft"
	WorkflowStatusActive   = "active"
	WorkflowStatusInactive = "inactive"

	// Execution statuses
	ExecutionStatusPending   = "pending"
	ExecutionStatusRunning   = "running"
	ExecutionStatusCompleted = "completed"
	ExecutionStatusFailed    = "failed"
	ExecutionStatusCancelled = "cancelled"

	// Task statuses
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"

	// Metric types
	MetricTypeCounter   = "counter"
	MetricTypeGauge     = "gauge"
	MetricTypeHistogram = "histogram"
	MetricTypeSummary   = "summary"
)