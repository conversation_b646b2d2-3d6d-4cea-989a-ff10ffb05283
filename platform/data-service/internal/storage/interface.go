package storage

import (
	"context"
	"time"
)

// Storage defines the interface for data storage operations
type Storage interface {
	// Agent operations
	CreateAgent(ctx context.Context, agent *Agent) error
	GetAgent(ctx context.Context, id string) (*Agent, error)
	UpdateAgent(ctx context.Context, agent *Agent) error
	DeleteAgent(ctx context.Context, id string) error
	ListAgents(ctx context.Context, filter AgentFilter) ([]*Agent, error)

	// Workflow operations
	CreateWorkflow(ctx context.Context, workflow *Workflow) error
	GetWorkflow(ctx context.Context, id string) (*Workflow, error)
	UpdateWorkflow(ctx context.Context, workflow *Workflow) error
	DeleteWorkflow(ctx context.Context, id string) error
	ListWorkflows(ctx context.Context, filter WorkflowFilter) ([]*Workflow, error)

	// Execution operations
	CreateExecution(ctx context.Context, execution *WorkflowExecution) error
	GetExecution(ctx context.Context, id string) (*WorkflowExecution, error)
	UpdateExecution(ctx context.Context, execution *WorkflowExecution) error
	ListExecutions(ctx context.Context, filter ExecutionFilter) ([]*WorkflowExecution, error)

	// Task operations
	CreateTask(ctx context.Context, task *Task) error
	GetTask(ctx context.Context, id string) (*Task, error)
	UpdateTask(ctx context.Context, task *Task) error
	ListTasks(ctx context.Context, filter TaskFilter) ([]*Task, error)

	// Metrics operations
	StoreMetric(ctx context.Context, metric *Metric) error
	GetMetrics(ctx context.Context, filter MetricFilter) ([]*Metric, error)

	// Health and utility
	Health(ctx context.Context) error
	Close()
}

// CacheStorage defines the interface for caching operations
type CacheStorage interface {
	// Basic cache operations
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string, dest interface{}) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error

	// Session management
	CreateSession(ctx context.Context, sessionID string, userID string, data map[string]interface{}, ttl time.Duration) error
	GetSession(ctx context.Context, sessionID string) (map[string]interface{}, error)
	UpdateSession(ctx context.Context, sessionID string, data map[string]interface{}) error
	DeleteSession(ctx context.Context, sessionID string) error

	// Pub/Sub operations
	Publish(ctx context.Context, channel string, message interface{}) error

	// List operations
	ListPush(ctx context.Context, key string, value interface{}) error
	ListPop(ctx context.Context, key string, dest interface{}) error

	// Health and utility
	Health(ctx context.Context) error
	Close() error
}

// DocumentStorage defines the interface for document storage operations
type DocumentStorage interface {
	// Document operations
	CreateDocument(ctx context.Context, collection string, doc interface{}) (string, error)
	GetDocument(ctx context.Context, collection string, id string, dest interface{}) error
	UpdateDocument(ctx context.Context, collection string, id string, update interface{}) error
	DeleteDocument(ctx context.Context, collection string, id string) error
	FindDocuments(ctx context.Context, collection string, filter interface{}, dest interface{}) error

	// Index operations
	CreateIndex(ctx context.Context, collection string, index interface{}) error
	DropIndex(ctx context.Context, collection string, name string) error

	// Health and utility
	Health(ctx context.Context) error
	Close() error
}

// StorageManager manages multiple storage backends
type StorageManager struct {
	Primary  Storage
	Cache    CacheStorage
	Document DocumentStorage
}

// NewStorageManager creates a new storage manager
func NewStorageManager(primary Storage, cache CacheStorage, document DocumentStorage) *StorageManager {
	return &StorageManager{
		Primary:  primary,
		Cache:    cache,
		Document: document,
	}
}

// Health checks all storage backends
func (sm *StorageManager) Health(ctx context.Context) map[string]error {
	results := make(map[string]error)
	
	if sm.Primary != nil {
		results["primary"] = sm.Primary.Health(ctx)
	}
	
	if sm.Cache != nil {
		results["cache"] = sm.Cache.Health(ctx)
	}
	
	if sm.Document != nil {
		results["document"] = sm.Document.Health(ctx)
	}
	
	return results
}

// Close closes all storage connections
func (sm *StorageManager) Close() {
	if sm.Primary != nil {
		sm.Primary.Close()
	}
	
	if sm.Cache != nil {
		sm.Cache.Close()
	}
	
	if sm.Document != nil {
		sm.Document.Close()
	}
}