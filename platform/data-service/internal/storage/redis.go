package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"

	"github.com/ai-platform/platform/data-service/internal/config"
)

// RedisStorage implements caching and session storage using Redis
type RedisStorage struct {
	client *redis.Client
	logger *zap.Logger
}

// NewRedisStorage creates a new Redis storage instance
func NewRedisStorage(ctx context.Context, cfg config.RedisConfig, logger *zap.Logger) (*RedisStorage, error) {
	addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	
	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     cfg.Password,
		DB:           cfg.Database,
		MaxRetries:   cfg.MaxRetries,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConn,
		DialTimeout:  cfg.DialTimeout,
	})
	
	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}
	
	logger.Info("Redis connection established",
		zap.String("addr", addr),
		zap.Int("database", cfg.Database),
		zap.Int("pool_size", cfg.PoolSize),
	)
	
	return &RedisStorage{
		client: client,
		logger: logger,
	}, nil
}

// Close closes the Redis connection
func (r *RedisStorage) Close() error {
	if r.client != nil {
		err := r.client.Close()
		r.logger.Info("Redis connection closed")
		return err
	}
	return nil
}

// Health checks Redis health
func (r *RedisStorage) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	
	return r.client.Ping(ctx).Err()
}

// Cache operations

// Set stores a value in cache with expiration
func (r *RedisStorage) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	
	err = r.client.Set(ctx, key, data, expiration).Err()
	if err != nil {
		r.logger.Error("Failed to set cache key", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to set cache key: %w", err)
	}
	
	return nil
}

// Get retrieves a value from cache
func (r *RedisStorage) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheKeyNotFound
		}
		r.logger.Error("Failed to get cache key", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to get cache key: %w", err)
	}
	
	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		return fmt.Errorf("failed to unmarshal cached value: %w", err)
	}
	
	return nil
}

// Delete removes a key from cache
func (r *RedisStorage) Delete(ctx context.Context, key string) error {
	err := r.client.Del(ctx, key).Err()
	if err != nil {
		r.logger.Error("Failed to delete cache key", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to delete cache key: %w", err)
	}
	
	return nil
}

// Exists checks if a key exists in cache
func (r *RedisStorage) Exists(ctx context.Context, key string) (bool, error) {
	result, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		r.logger.Error("Failed to check key existence", zap.Error(err), zap.String("key", key))
		return false, fmt.Errorf("failed to check key existence: %w", err)
	}
	
	return result > 0, nil
}

// Expire sets expiration time for a key
func (r *RedisStorage) Expire(ctx context.Context, key string, expiration time.Duration) error {
	err := r.client.Expire(ctx, key, expiration).Err()
	if err != nil {
		r.logger.Error("Failed to set key expiration", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to set key expiration: %w", err)
	}
	
	return nil
}

// Session management

// CreateSession creates a new session
func (r *RedisStorage) CreateSession(ctx context.Context, sessionID string, userID string, data map[string]interface{}, ttl time.Duration) error {
	sessionData := map[string]interface{}{
		"user_id":    userID,
		"created_at": time.Now().Unix(),
		"data":       data,
	}
	
	key := fmt.Sprintf("session:%s", sessionID)
	return r.Set(ctx, key, sessionData, ttl)
}

// GetSession retrieves session data
func (r *RedisStorage) GetSession(ctx context.Context, sessionID string) (map[string]interface{}, error) {
	key := fmt.Sprintf("session:%s", sessionID)
	var sessionData map[string]interface{}
	
	err := r.Get(ctx, key, &sessionData)
	if err != nil {
		return nil, err
	}
	
	return sessionData, nil
}

// UpdateSession updates session data
func (r *RedisStorage) UpdateSession(ctx context.Context, sessionID string, data map[string]interface{}) error {
	key := fmt.Sprintf("session:%s", sessionID)
	
	// Get existing session
	var sessionData map[string]interface{}
	err := r.Get(ctx, key, &sessionData)
	if err != nil {
		return err
	}
	
	// Update data
	sessionData["data"] = data
	sessionData["updated_at"] = time.Now().Unix()
	
	// Get remaining TTL
	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to get TTL: %w", err)
	}
	
	return r.Set(ctx, key, sessionData, ttl)
}

// DeleteSession removes a session
func (r *RedisStorage) DeleteSession(ctx context.Context, sessionID string) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return r.Delete(ctx, key)
}

// Pub/Sub operations

// Publish publishes a message to a channel
func (r *RedisStorage) Publish(ctx context.Context, channel string, message interface{}) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}
	
	err = r.client.Publish(ctx, channel, data).Err()
	if err != nil {
		r.logger.Error("Failed to publish message", zap.Error(err), zap.String("channel", channel))
		return fmt.Errorf("failed to publish message: %w", err)
	}
	
	return nil
}

// Subscribe subscribes to channels
func (r *RedisStorage) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return r.client.Subscribe(ctx, channels...)
}

// List operations

// ListPush adds an item to the beginning of a list
func (r *RedisStorage) ListPush(ctx context.Context, key string, value interface{}) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	
	err = r.client.LPush(ctx, key, data).Err()
	if err != nil {
		r.logger.Error("Failed to push to list", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to push to list: %w", err)
	}
	
	return nil
}

// ListPop removes and returns an item from the beginning of a list
func (r *RedisStorage) ListPop(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.LPop(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheKeyNotFound
		}
		r.logger.Error("Failed to pop from list", zap.Error(err), zap.String("key", key))
		return fmt.Errorf("failed to pop from list: %w", err)
	}
	
	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		return fmt.Errorf("failed to unmarshal list item: %w", err)
	}
	
	return nil
}

// GetStats returns Redis statistics
func (r *RedisStorage) GetStats(ctx context.Context) map[string]interface{} {
	stats := r.client.PoolStats()
	
	info, _ := r.client.Info(ctx).Result()
	
	return map[string]interface{}{
		"pool_hits":        stats.Hits,
		"pool_misses":      stats.Misses,
		"pool_timeouts":    stats.Timeouts,
		"pool_total_conns": stats.TotalConns,
		"pool_idle_conns":  stats.IdleConns,
		"pool_stale_conns": stats.StaleConns,
		"server_info":      info,
	}
}

// Cache errors
var (
	ErrCacheKeyNotFound = fmt.Errorf("cache key not found")
)