package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

// Workflow storage operations

// CreateWorkflow creates a new workflow record
func (s *PostgreSQLStorage) CreateWorkflow(ctx context.Context, workflow *Workflow) error {
	triggersJSON, err := json.Marshal(workflow.Triggers)
	if err != nil {
		return fmt.Errorf("failed to marshal triggers: %w", err)
	}

	definitionJSON, err := json.Marshal(workflow.Definition)
	if err != nil {
		return fmt.Errorf("failed to marshal definition: %w", err)
	}

	query := `
		INSERT INTO workflows (id, name, description, version, status, definition, triggers, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	now := time.Now()
	_, err = s.pool.Exec(ctx, query,
		workflow.ID,
		workflow.Name,
		workflow.Description,
		workflow.Version,
		workflow.Status,
		definitionJSON,
		triggersJSON,
		now,
		now,
	)

	if err != nil {
		s.logger.Error("Failed to create workflow", zap.Error(err), zap.String("workflow_id", workflow.ID))
		return fmt.Errorf("failed to create workflow: %w", err)
	}

	workflow.CreatedAt = now
	workflow.UpdatedAt = now

	s.logger.Info("Workflow created", zap.String("workflow_id", workflow.ID), zap.String("name", workflow.Name))
	return nil
}

// GetWorkflow retrieves a workflow by ID
func (s *PostgreSQLStorage) GetWorkflow(ctx context.Context, id string) (*Workflow, error) {
	query := `
		SELECT id, name, description, version, status, definition, triggers, created_at, updated_at
		FROM workflows WHERE id = $1
	`

	row := s.pool.QueryRow(ctx, query, id)

	var workflow Workflow
	var definitionJSON, triggersJSON []byte

	err := row.Scan(
		&workflow.ID,
		&workflow.Name,
		&workflow.Description,
		&workflow.Version,
		&workflow.Status,
		&definitionJSON,
		&triggersJSON,
		&workflow.CreatedAt,
		&workflow.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, ErrWorkflowNotFound
		}
		s.logger.Error("Failed to get workflow", zap.Error(err), zap.String("workflow_id", id))
		return nil, fmt.Errorf("failed to get workflow: %w", err)
	}

	// Unmarshal JSON fields
	if err := json.Unmarshal(definitionJSON, &workflow.Definition); err != nil {
		return nil, fmt.Errorf("failed to unmarshal definition: %w", err)
	}

	if err := json.Unmarshal(triggersJSON, &workflow.Triggers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal triggers: %w", err)
	}

	return &workflow, nil
}

// UpdateWorkflow updates an existing workflow
func (s *PostgreSQLStorage) UpdateWorkflow(ctx context.Context, workflow *Workflow) error {
	triggersJSON, err := json.Marshal(workflow.Triggers)
	if err != nil {
		return fmt.Errorf("failed to marshal triggers: %w", err)
	}

	definitionJSON, err := json.Marshal(workflow.Definition)
	if err != nil {
		return fmt.Errorf("failed to marshal definition: %w", err)
	}

	query := `
		UPDATE workflows 
		SET name = $2, description = $3, version = $4, status = $5, 
		    definition = $6, triggers = $7, updated_at = $8
		WHERE id = $1
	`

	now := time.Now()
	result, err := s.pool.Exec(ctx, query,
		workflow.ID,
		workflow.Name,
		workflow.Description,
		workflow.Version,
		workflow.Status,
		definitionJSON,
		triggersJSON,
		now,
	)

	if err != nil {
		s.logger.Error("Failed to update workflow", zap.Error(err), zap.String("workflow_id", workflow.ID))
		return fmt.Errorf("failed to update workflow: %w", err)
	}

	if result.RowsAffected() == 0 {
		return ErrWorkflowNotFound
	}

	workflow.UpdatedAt = now

	s.logger.Info("Workflow updated", zap.String("workflow_id", workflow.ID))
	return nil
}

// DeleteWorkflow deletes a workflow by ID
func (s *PostgreSQLStorage) DeleteWorkflow(ctx context.Context, id string) error {
	query := `DELETE FROM workflows WHERE id = $1`

	result, err := s.pool.Exec(ctx, query, id)
	if err != nil {
		s.logger.Error("Failed to delete workflow", zap.Error(err), zap.String("workflow_id", id))
		return fmt.Errorf("failed to delete workflow: %w", err)
	}

	if result.RowsAffected() == 0 {
		return ErrWorkflowNotFound
	}

	s.logger.Info("Workflow deleted", zap.String("workflow_id", id))
	return nil
}

// ListWorkflows lists workflows with optional filtering
func (s *PostgreSQLStorage) ListWorkflows(ctx context.Context, filter WorkflowFilter) ([]*Workflow, error) {
	query := `
		SELECT id, name, description, version, status, definition, triggers, created_at, updated_at
		FROM workflows
		WHERE ($1 = '' OR status = $1)
		AND ($2 = '' OR version = $2)
		ORDER BY created_at DESC
		LIMIT $3 OFFSET $4
	`

	rows, err := s.pool.Query(ctx, query, filter.Status, filter.Version, filter.Limit, filter.Offset)
	if err != nil {
		s.logger.Error("Failed to list workflows", zap.Error(err))
		return nil, fmt.Errorf("failed to list workflows: %w", err)
	}
	defer rows.Close()

	var workflows []*Workflow
	for rows.Next() {
		var workflow Workflow
		var definitionJSON, triggersJSON []byte

		err := rows.Scan(
			&workflow.ID,
			&workflow.Name,
			&workflow.Description,
			&workflow.Version,
			&workflow.Status,
			&definitionJSON,
			&triggersJSON,
			&workflow.CreatedAt,
			&workflow.UpdatedAt,
		)
		if err != nil {
			s.logger.Error("Failed to scan workflow row", zap.Error(err))
			return nil, fmt.Errorf("failed to scan workflow: %w", err)
		}

		// Unmarshal JSON fields
		if err := json.Unmarshal(definitionJSON, &workflow.Definition); err != nil {
			return nil, fmt.Errorf("failed to unmarshal definition: %w", err)
		}

		if err := json.Unmarshal(triggersJSON, &workflow.Triggers); err != nil {
			return nil, fmt.Errorf("failed to unmarshal triggers: %w", err)
		}

		workflows = append(workflows, &workflow)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating workflow rows: %w", err)
	}

	return workflows, nil
}

// Execution storage operations

// CreateExecution creates a new execution record
func (s *PostgreSQLStorage) CreateExecution(ctx context.Context, execution *WorkflowExecution) error {
	inputJSON, err := json.Marshal(execution.InputData)
	if err != nil {
		return fmt.Errorf("failed to marshal input data: %w", err)
	}

	outputJSON, err := json.Marshal(execution.OutputData)
	if err != nil {
		return fmt.Errorf("failed to marshal output data: %w", err)
	}

	progressJSON, err := json.Marshal(execution.Progress)
	if err != nil {
		return fmt.Errorf("failed to marshal progress: %w", err)
	}

	query := `
		INSERT INTO workflow_executions (id, workflow_id, status, input_data, output_data, progress, started_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	now := time.Now()
	_, err = s.pool.Exec(ctx, query,
		execution.ID,
		execution.WorkflowID,
		execution.Status,
		inputJSON,
		outputJSON,
		progressJSON,
		execution.StartedAt,
		now,
		now,
	)

	if err != nil {
		s.logger.Error("Failed to create execution", zap.Error(err), zap.String("execution_id", execution.ID))
		return fmt.Errorf("failed to create execution: %w", err)
	}

	execution.CreatedAt = now
	execution.UpdatedAt = now

	s.logger.Info("Execution created", zap.String("execution_id", execution.ID))
	return nil
}

// GetExecution retrieves an execution by ID
func (s *PostgreSQLStorage) GetExecution(ctx context.Context, id string) (*WorkflowExecution, error) {
	query := `
		SELECT id, workflow_id, status, input_data, output_data, progress, started_at, completed_at, created_at, updated_at
		FROM workflow_executions WHERE id = $1
	`

	row := s.pool.QueryRow(ctx, query, id)

	var execution WorkflowExecution
	var inputJSON, outputJSON, progressJSON []byte

	err := row.Scan(
		&execution.ID,
		&execution.WorkflowID,
		&execution.Status,
		&inputJSON,
		&outputJSON,
		&progressJSON,
		&execution.StartedAt,
		&execution.CompletedAt,
		&execution.CreatedAt,
		&execution.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("execution not found")
		}
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}

	// Unmarshal JSON fields
	if err := json.Unmarshal(inputJSON, &execution.InputData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal input data: %w", err)
	}

	if err := json.Unmarshal(outputJSON, &execution.OutputData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal output data: %w", err)
	}

	if err := json.Unmarshal(progressJSON, &execution.Progress); err != nil {
		return nil, fmt.Errorf("failed to unmarshal progress: %w", err)
	}

	return &execution, nil
}

// UpdateExecution updates an existing execution
func (s *PostgreSQLStorage) UpdateExecution(ctx context.Context, execution *WorkflowExecution) error {
	inputJSON, err := json.Marshal(execution.InputData)
	if err != nil {
		return fmt.Errorf("failed to marshal input data: %w", err)
	}

	outputJSON, err := json.Marshal(execution.OutputData)
	if err != nil {
		return fmt.Errorf("failed to marshal output data: %w", err)
	}

	progressJSON, err := json.Marshal(execution.Progress)
	if err != nil {
		return fmt.Errorf("failed to marshal progress: %w", err)
	}

	query := `
		UPDATE workflow_executions 
		SET status = $2, input_data = $3, output_data = $4, progress = $5, 
		    completed_at = $6, updated_at = $7
		WHERE id = $1
	`

	now := time.Now()
	result, err := s.pool.Exec(ctx, query,
		execution.ID,
		execution.Status,
		inputJSON,
		outputJSON,
		progressJSON,
		execution.CompletedAt,
		now,
	)

	if err != nil {
		return fmt.Errorf("failed to update execution: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("execution not found")
	}

	execution.UpdatedAt = now
	return nil
}

// ListExecutions lists executions with optional filtering
func (s *PostgreSQLStorage) ListExecutions(ctx context.Context, filter ExecutionFilter) ([]*WorkflowExecution, error) {
	query := `
		SELECT id, workflow_id, status, input_data, output_data, progress, started_at, completed_at, created_at, updated_at
		FROM workflow_executions
		WHERE ($1 = '' OR workflow_id = $1)
		AND ($2 = '' OR status = $2)
		AND ($3::timestamp IS NULL OR created_at >= $3)
		AND ($4::timestamp IS NULL OR created_at <= $4)
		ORDER BY created_at DESC
		LIMIT $5 OFFSET $6
	`

	rows, err := s.pool.Query(ctx, query, filter.WorkflowID, filter.Status, filter.From, filter.To, filter.Limit, filter.Offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list executions: %w", err)
	}
	defer rows.Close()

	var executions []*WorkflowExecution
	for rows.Next() {
		var execution WorkflowExecution
		var inputJSON, outputJSON, progressJSON []byte

		err := rows.Scan(
			&execution.ID,
			&execution.WorkflowID,
			&execution.Status,
			&inputJSON,
			&outputJSON,
			&progressJSON,
			&execution.StartedAt,
			&execution.CompletedAt,
			&execution.CreatedAt,
			&execution.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan execution: %w", err)
		}

		// Unmarshal JSON fields
		if err := json.Unmarshal(inputJSON, &execution.InputData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal input data: %w", err)
		}

		if err := json.Unmarshal(outputJSON, &execution.OutputData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal output data: %w", err)
		}

		if err := json.Unmarshal(progressJSON, &execution.Progress); err != nil {
			return nil, fmt.Errorf("failed to unmarshal progress: %w", err)
		}

		executions = append(executions, &execution)
	}

	return executions, rows.Err()
}

// Task and Metrics storage operations (simplified implementations)

func (s *PostgreSQLStorage) CreateTask(ctx context.Context, task *Task) error {
	// TODO: Implement task creation
	return fmt.Errorf("not implemented")
}

func (s *PostgreSQLStorage) GetTask(ctx context.Context, id string) (*Task, error) {
	// TODO: Implement task retrieval
	return nil, fmt.Errorf("not implemented")
}

func (s *PostgreSQLStorage) UpdateTask(ctx context.Context, task *Task) error {
	// TODO: Implement task update
	return fmt.Errorf("not implemented")
}

func (s *PostgreSQLStorage) ListTasks(ctx context.Context, filter TaskFilter) ([]*Task, error) {
	// TODO: Implement task listing
	return nil, fmt.Errorf("not implemented")
}

func (s *PostgreSQLStorage) StoreMetric(ctx context.Context, metric *Metric) error {
	labelsJSON, err := json.Marshal(metric.Labels)
	if err != nil {
		return fmt.Errorf("failed to marshal labels: %w", err)
	}

	query := `
		INSERT INTO metrics (id, name, type, value, labels, timestamp, source)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err = s.pool.Exec(ctx, query,
		metric.ID,
		metric.Name,
		metric.Type,
		metric.Value,
		labelsJSON,
		metric.Timestamp,
		metric.Source,
	)

	if err != nil {
		return fmt.Errorf("failed to store metric: %w", err)
	}

	return nil
}

func (s *PostgreSQLStorage) GetMetrics(ctx context.Context, filter MetricFilter) ([]*Metric, error) {
	query := `
		SELECT id, name, type, value, labels, timestamp, source
		FROM metrics
		WHERE ($1 = '' OR name = $1)
		AND ($2 = '' OR type = $2)
		AND ($3 = '' OR source = $3)
		AND ($4::timestamp IS NULL OR timestamp >= $4)
		AND ($5::timestamp IS NULL OR timestamp <= $5)
		ORDER BY timestamp DESC
		LIMIT $6 OFFSET $7
	`

	rows, err := s.pool.Query(ctx, query,
		filter.Name, filter.Type, filter.Source,
		filter.From, filter.To,
		filter.Limit, filter.Offset,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get metrics: %w", err)
	}
	defer rows.Close()

	var metrics []*Metric
	for rows.Next() {
		var metric Metric
		var labelsJSON []byte

		err := rows.Scan(
			&metric.ID,
			&metric.Name,
			&metric.Type,
			&metric.Value,
			&labelsJSON,
			&metric.Timestamp,
			&metric.Source,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan metric: %w", err)
		}

		if err := json.Unmarshal(labelsJSON, &metric.Labels); err != nil {
			return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
		}

		metrics = append(metrics, &metric)
	}

	return metrics, rows.Err()
}