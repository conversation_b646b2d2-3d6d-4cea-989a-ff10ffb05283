syntax = "proto3";

package ai.platform.v1;

option go_package = "github.com/ai-platform/shared/proto/v1";
option java_package = "io.platform.proto.v1";
option java_outer_classname = "AgentProto";

import "common.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";

// Agent service definition
service AgentService {
  // Agent lifecycle management
  rpc CreateAgent(CreateAgentRequest) returns (CreateAgentResponse);
  rpc GetAgent(GetAgentRequest) returns (GetAgentResponse);
  rpc ListAgents(ListAgentsRequest) returns (ListAgentsResponse);
  rpc UpdateAgent(UpdateAgentRequest) returns (UpdateAgentResponse);
  rpc DeleteAgent(DeleteAgentRequest) returns (DeleteAgentResponse);
  
  // Agent control
  rpc StartAgent(StartAgentRequest) returns (StartAgentResponse);
  rpc StopAgent(StopAgentRequest) returns (StopAgentResponse);
  rpc RestartAgent(RestartAgentRequest) returns (RestartAgentResponse);
  
  // Task execution
  rpc ExecuteTask(ExecuteTaskRequest) returns (stream ExecuteTaskResponse);
  rpc GetTaskStatus(GetTaskStatusRequest) returns (GetTaskStatusResponse);
  rpc CancelTask(CancelTaskRequest) returns (CancelTaskResponse);
  
  // Communication
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  rpc ReceiveMessages(ReceiveMessagesRequest) returns (stream MessageResponse);
  
  // Health and monitoring
  rpc GetHealth(GetHealthRequest) returns (GetHealthResponse);
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse);
  rpc GetLogs(GetLogsRequest) returns (stream LogEntry);
}

// Agent status enum
enum AgentStatus {
  AGENT_STATUS_UNSPECIFIED = 0;
  AGENT_STATUS_CREATING = 1;
  AGENT_STATUS_STARTING = 2;
  AGENT_STATUS_ACTIVE = 3;
  AGENT_STATUS_BUSY = 4;
  AGENT_STATUS_STOPPING = 5;
  AGENT_STATUS_STOPPED = 6;
  AGENT_STATUS_ERROR = 7;
  AGENT_STATUS_UPDATING = 8;
  AGENT_STATUS_RETIRING = 9;
}

// Agent language enum
enum AgentLanguage {
  AGENT_LANGUAGE_UNSPECIFIED = 0;
  AGENT_LANGUAGE_PYTHON = 1;
  AGENT_LANGUAGE_JAVA = 2;
  AGENT_LANGUAGE_GO = 3;
  AGENT_LANGUAGE_JAVASCRIPT = 4;
}

// Agent definition
message Agent {
  string id = 1;
  string name = 2;
  string description = 3;
  AgentLanguage language = 4;
  repeated string capabilities = 5;
  ResourceRequirements resources = 6;
  AgentStatus status = 7;
  map<string, string> configuration = 8;
  repeated Integration integrations = 9;
  repeated AIModel ai_models = 10;
  SecurityConfig security = 11;
  Metadata metadata = 12;
  string template_id = 13;
  string version = 14;
  AgentEndpoints endpoints = 15;
}

// Agent integration configuration
message Integration {
  string type = 1;        // database, message_queue, api, etc.
  string provider = 2;    // postgresql, kafka, rest, etc.
  map<string, string> configuration = 3;
  bool enabled = 4;
}

// AI model configuration
message AIModel {
  string provider = 1;    // openai, anthropic, google, etc.
  string model = 2;       // gpt-4, claude-2, gemini-pro, etc.
  string purpose = 3;     // data-analysis, text-generation, etc.
  map<string, string> configuration = 4;
  bool enabled = 5;
}

// Security configuration
message SecurityConfig {
  bool encryption_required = 1;
  string authentication_method = 2;
  repeated string permissions = 3;
  map<string, string> security_policies = 4;
}

// Agent endpoints
message AgentEndpoints {
  string health = 1;
  string metrics = 2;
  string logs = 3;
  string communication = 4;
  string tasks = 5;
}

// Create agent request
message CreateAgentRequest {
  string name = 1;
  string description = 2;
  AgentLanguage language = 3;
  repeated string capabilities = 4;
  ResourceRequirements resources = 5;
  map<string, string> configuration = 6;
  repeated Integration integrations = 7;
  repeated AIModel ai_models = 8;
  SecurityConfig security = 9;
  string template_id = 10;
}

// Create agent response
message CreateAgentResponse {
  Agent agent = 1;
  string creation_job_id = 2;
  google.protobuf.Timestamp estimated_completion = 3;
  Error error = 4;
}

// Get agent request
message GetAgentRequest {
  string agent_id = 1;
}

// Get agent response
message GetAgentResponse {
  Agent agent = 1;
  Error error = 2;
}

// List agents request
message ListAgentsRequest {
  AgentStatus status = 1;
  AgentLanguage language = 2;
  string capability = 3;
  int32 page = 4;
  int32 limit = 5;
  string sort = 6;
  string order = 7;
}

// List agents response
message ListAgentsResponse {
  repeated Agent agents = 1;
  PageInfo page_info = 2;
  Error error = 3;
}

// Update agent request
message UpdateAgentRequest {
  string agent_id = 1;
  map<string, string> configuration = 2;
  ResourceRequirements resources = 3;
  string version = 4;
}

// Update agent response
message UpdateAgentResponse {
  Agent agent = 1;
  string update_job_id = 2;
  google.protobuf.Timestamp estimated_completion = 3;
  Error error = 4;
}

// Delete agent request
message DeleteAgentRequest {
  string agent_id = 1;
  bool force = 2;
  bool preserve_data = 3;
}

// Delete agent response
message DeleteAgentResponse {
  string message = 1;
  string deletion_job_id = 2;
  google.protobuf.Timestamp estimated_completion = 3;
  Error error = 4;
}

// Start agent request
message StartAgentRequest {
  string agent_id = 1;
}

// Start agent response
message StartAgentResponse {
  AgentStatus status = 1;
  google.protobuf.Timestamp estimated_ready = 2;
  Error error = 3;
}

// Stop agent request
message StopAgentRequest {
  string agent_id = 1;
  google.protobuf.Duration timeout = 2;
  bool force = 3;
}

// Stop agent response
message StopAgentResponse {
  AgentStatus status = 1;
  google.protobuf.Timestamp estimated_stopped = 2;
  Error error = 3;
}

// Restart agent request
message RestartAgentRequest {
  string agent_id = 1;
}

// Restart agent response
message RestartAgentResponse {
  AgentStatus status = 1;
  google.protobuf.Timestamp estimated_ready = 2;
  Error error = 3;
}

// Task execution request
message ExecuteTaskRequest {
  string agent_id = 1;
  string task_id = 2;
  string task_type = 3;
  map<string, string> payload = 4;
  Priority priority = 5;
  google.protobuf.Duration timeout = 6;
  string callback_url = 7;
}

// Task execution response
message ExecuteTaskResponse {
  string task_id = 1;
  Status status = 2;
  map<string, string> result = 3;
  Error error = 4;
  float progress = 5;
  google.protobuf.Timestamp timestamp = 6;
}

// Get task status request
message GetTaskStatusRequest {
  string agent_id = 1;
  string task_id = 2;
}

// Get task status response
message GetTaskStatusResponse {
  string task_id = 1;
  Status status = 2;
  google.protobuf.Timestamp sent_at = 3;
  google.protobuf.Timestamp completed_at = 4;
  map<string, string> result = 5;
  Error error = 6;
}

// Cancel task request
message CancelTaskRequest {
  string agent_id = 1;
  string task_id = 2;
}

// Cancel task response
message CancelTaskResponse {
  string message = 1;
  Error error = 2;
}

// Send message request
message SendMessageRequest {
  string from_agent_id = 1;
  string to_agent_id = 2;
  string message_type = 3;
  map<string, string> content = 4;
  Priority priority = 5;
  google.protobuf.Duration timeout = 6;
}

// Send message response
message SendMessageResponse {
  string message_id = 1;
  Status status = 2;
  google.protobuf.Timestamp estimated_response = 3;
  Error error = 4;
}

// Receive messages request
message ReceiveMessagesRequest {
  string agent_id = 1;
  repeated string message_types = 2;
}

// Message response
message MessageResponse {
  string message_id = 1;
  string from_agent_id = 2;
  string message_type = 3;
  map<string, string> content = 4;
  Priority priority = 5;
  google.protobuf.Timestamp timestamp = 6;
}

// Get health request
message GetHealthRequest {
  string agent_id = 1;
}

// Get health response
message GetHealthResponse {
  string overall_status = 1;
  google.protobuf.Timestamp last_updated = 2;
  map<string, HealthCheck> checks = 3;
  Error error = 4;
}

// Get metrics request
message GetMetricsRequest {
  string agent_id = 1;
  google.protobuf.Timestamp from = 2;
  google.protobuf.Timestamp to = 3;
  string resolution = 4;
}

// Get metrics response
message GetMetricsResponse {
  string agent_id = 1;
  TimeRange time_range = 2;
  AgentMetrics metrics = 3;
  Error error = 4;
}

// Time range
message TimeRange {
  google.protobuf.Timestamp from = 1;
  google.protobuf.Timestamp to = 2;
  string resolution = 3;
}

// Agent metrics
message AgentMetrics {
  repeated MetricPoint tasks_processed = 1;
  repeated MetricPoint response_time_p95 = 2;
  repeated MetricPoint success_rate = 3;
  ResourceMetrics resource_usage = 4;
}

// Metric point
message MetricPoint {
  google.protobuf.Timestamp timestamp = 1;
  double value = 2;
}

// Resource metrics
message ResourceMetrics {
  repeated MetricPoint cpu = 1;
  repeated MetricPoint memory = 2;
  repeated MetricPoint storage = 3;
  repeated MetricPoint network = 4;
}

// Get logs request
message GetLogsRequest {
  string agent_id = 1;
  google.protobuf.Timestamp from = 2;
  google.protobuf.Timestamp to = 3;
  string level = 4;
  int32 limit = 5;
  string search = 6;
}

// Log entry
message LogEntry {
  google.protobuf.Timestamp timestamp = 1;
  string level = 2;
  string message = 3;
  map<string, string> context = 4;
}