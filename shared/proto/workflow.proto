syntax = "proto3";

package ai.platform.v1;

option go_package = "github.com/ai-platform/shared/proto/v1";
option java_package = "io.platform.proto.v1";
option java_outer_classname = "WorkflowProto";

import "common.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";

// Workflow service definition
service WorkflowService {
  // Workflow management
  rpc CreateWorkflow(CreateWorkflowRequest) returns (CreateWorkflowResponse);
  rpc GetWorkflow(GetWorkflowRequest) returns (GetWorkflowResponse);
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse);
  rpc UpdateWorkflow(UpdateWorkflowRequest) returns (UpdateWorkflowResponse);
  rpc DeleteWorkflow(DeleteWorkflowRequest) returns (DeleteWorkflowResponse);
  
  // Workflow execution
  rpc ExecuteWorkflow(ExecuteWorkflowRequest) returns (ExecuteWorkflowResponse);
  rpc GetExecution(GetExecutionRequest) returns (GetExecutionResponse);
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse);
  rpc CancelExecution(CancelExecutionRequest) returns (CancelExecutionResponse);
  
  // Workflow monitoring
  rpc GetExecutionStatus(GetExecutionStatusRequest) returns (GetExecutionStatusResponse);
  rpc GetExecutionLogs(GetExecutionLogsRequest) returns (stream LogEntry);
}

// Workflow status enum
enum WorkflowStatus {
  WORKFLOW_STATUS_UNSPECIFIED = 0;
  WORKFLOW_STATUS_DRAFT = 1;
  WORKFLOW_STATUS_ACTIVE = 2;
  WORKFLOW_STATUS_INACTIVE = 3;
  WORKFLOW_STATUS_DEPRECATED = 4;
}

// Execution status enum
enum ExecutionStatus {
  EXECUTION_STATUS_UNSPECIFIED = 0;
  EXECUTION_STATUS_PENDING = 1;
  EXECUTION_STATUS_RUNNING = 2;
  EXECUTION_STATUS_COMPLETED = 3;
  EXECUTION_STATUS_FAILED = 4;
  EXECUTION_STATUS_CANCELLED = 5;
  EXECUTION_STATUS_TIMEOUT = 6;
}

// Node type enum
enum NodeType {
  NODE_TYPE_UNSPECIFIED = 0;
  NODE_TYPE_TASK = 1;
  NODE_TYPE_DECISION = 2;
  NODE_TYPE_PARALLEL = 3;
  NODE_TYPE_LOOP = 4;
  NODE_TYPE_SUBPROCESS = 5;
}

// Trigger type enum
enum TriggerType {
  TRIGGER_TYPE_UNSPECIFIED = 0;
  TRIGGER_TYPE_MANUAL = 1;
  TRIGGER_TYPE_WEBHOOK = 2;
  TRIGGER_TYPE_SCHEDULE = 3;
  TRIGGER_TYPE_EVENT = 4;
}

// Workflow definition
message Workflow {
  string id = 1;
  string name = 2;
  string description = 3;
  string version = 4;
  WorkflowStatus status = 5;
  WorkflowDefinition definition = 6;
  repeated Trigger triggers = 7;
  Metadata metadata = 8;
}

// Workflow definition
message WorkflowDefinition {
  repeated WorkflowNode nodes = 1;
  repeated WorkflowEdge edges = 2;
  ErrorHandling error_handling = 3;
  map<string, string> variables = 4;
  google.protobuf.Duration default_timeout = 5;
}

// Workflow node
message WorkflowNode {
  string id = 1;
  string name = 2;
  NodeType type = 3;
  repeated string required_capabilities = 4;
  map<string, string> task_payload = 5;
  google.protobuf.Duration timeout = 6;
  int32 retry_count = 7;
  repeated string dependencies = 8;
  map<string, string> configuration = 9;
}

// Workflow edge
message WorkflowEdge {
  string from = 1;
  string to = 2;
  string condition = 3;
  map<string, string> parameters = 4;
}

// Error handling configuration
message ErrorHandling {
  RetryPolicy retry_policy = 1;
  repeated string failure_actions = 2;
  string error_node = 3;
}

// Retry policy
message RetryPolicy {
  int32 max_retries = 1;
  string backoff = 2;  // exponential, linear, fixed
  google.protobuf.Duration initial_delay = 3;
  google.protobuf.Duration max_delay = 4;
}

// Trigger configuration
message Trigger {
  string id = 1;
  TriggerType type = 2;
  map<string, string> configuration = 3;
  bool enabled = 4;
}

// Workflow execution
message WorkflowExecution {
  string execution_id = 1;
  string workflow_id = 2;
  ExecutionStatus status = 3;
  map<string, string> input_data = 4;
  map<string, string> output_data = 5;
  google.protobuf.Timestamp started_at = 6;
  google.protobuf.Timestamp completed_at = 7;
  ExecutionProgress progress = 8;
  repeated TaskStatus task_statuses = 9;
  Error error = 10;
  string triggered_by = 11;
}

// Execution progress
message ExecutionProgress {
  int32 completed_tasks = 1;
  int32 total_tasks = 2;
  string current_task = 3;
  float percentage = 4;
}

// Task status within workflow
message TaskStatus {
  string task_id = 1;
  string node_id = 2;
  Status status = 3;
  google.protobuf.Timestamp started_at = 4;
  google.protobuf.Timestamp completed_at = 5;
  google.protobuf.Duration duration = 6;
  map<string, string> output = 7;
  Error error = 8;
  string assigned_agent = 9;
  int32 retry_count = 10;
}

// Create workflow request
message CreateWorkflowRequest {
  string name = 1;
  string description = 2;
  string version = 3;
  WorkflowDefinition definition = 4;
  repeated Trigger triggers = 5;
}

// Create workflow response
message CreateWorkflowResponse {
  Workflow workflow = 1;
  Error error = 2;
}

// Get workflow request
message GetWorkflowRequest {
  string workflow_id = 1;
}

// Get workflow response
message GetWorkflowResponse {
  Workflow workflow = 1;
  Error error = 2;
}

// List workflows request
message ListWorkflowsRequest {
  WorkflowStatus status = 1;
  int32 page = 2;
  int32 limit = 3;
  string sort = 4;
  string order = 5;
}

// List workflows response
message ListWorkflowsResponse {
  repeated Workflow workflows = 1;
  PageInfo page_info = 2;
  Error error = 3;
}

// Update workflow request
message UpdateWorkflowRequest {
  string workflow_id = 1;
  WorkflowDefinition definition = 2;
  repeated Trigger triggers = 3;
  WorkflowStatus status = 4;
}

// Update workflow response
message UpdateWorkflowResponse {
  Workflow workflow = 1;
  Error error = 2;
}

// Delete workflow request
message DeleteWorkflowRequest {
  string workflow_id = 1;
  bool force = 2;
}

// Delete workflow response
message DeleteWorkflowResponse {
  string message = 1;
  Error error = 2;
}

// Execute workflow request
message ExecuteWorkflowRequest {
  string workflow_id = 1;
  map<string, string> input_data = 2;
  ExecutionOptions execution_options = 3;
  string callback_url = 4;
}

// Execution options
message ExecutionOptions {
  google.protobuf.Duration timeout = 1;
  bool retry_failed_tasks = 2;
  bool parallel_execution = 3;
  map<string, string> variables = 4;
}

// Execute workflow response
message ExecuteWorkflowResponse {
  WorkflowExecution execution = 1;
  Error error = 2;
}

// Get execution request
message GetExecutionRequest {
  string execution_id = 1;
}

// Get execution response
message GetExecutionResponse {
  WorkflowExecution execution = 1;
  Error error = 2;
}

// List executions request
message ListExecutionsRequest {
  string workflow_id = 1;
  ExecutionStatus status = 2;
  google.protobuf.Timestamp from = 3;
  google.protobuf.Timestamp to = 4;
  int32 page = 5;
  int32 limit = 6;
}

// List executions response
message ListExecutionsResponse {
  repeated WorkflowExecution executions = 1;
  PageInfo page_info = 2;
  Error error = 3;
}

// Cancel execution request
message CancelExecutionRequest {
  string execution_id = 1;
  bool force = 2;
}

// Cancel execution response
message CancelExecutionResponse {
  string message = 1;
  Error error = 2;
}

// Get execution status request
message GetExecutionStatusRequest {
  string execution_id = 1;
}

// Get execution status response  
message GetExecutionStatusResponse {
  WorkflowExecution execution = 1;
  Error error = 2;
}

// Get execution logs request
message GetExecutionLogsRequest {
  string execution_id = 1;
  google.protobuf.Timestamp from = 2;
  google.protobuf.Timestamp to = 3;
  string level = 4;
  int32 limit = 5;
}