syntax = "proto3";

package ai.platform.v1;

option go_package = "github.com/ai-platform/shared/proto/v1";
option java_package = "io.platform.proto.v1";
option java_outer_classname = "CommonProto";

import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

// Common enums
enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_PENDING = 1;
  STATUS_RUNNING = 2;
  STATUS_COMPLETED = 3;
  STATUS_FAILED = 4;
  STATUS_CANCELLED = 5;
  STATUS_TIMEOUT = 6;
}

enum Priority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_MEDIUM = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_CRITICAL = 4;
}

// Resource requirements
message ResourceRequirements {
  string cpu = 1;              // e.g., "1000m" for 1 CPU
  string memory = 2;           // e.g., "2Gi" for 2GB RAM
  string storage = 3;          // e.g., "10Gi" for 10GB storage
  int32 gpu_count = 4;         // Number of GPUs required
  map<string, string> custom = 5;  // Custom resource requirements
}

// Error information
message Error {
  string code = 1;
  string message = 2;
  map<string, string> details = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Pagination information
message PageInfo {
  int32 page = 1;
  int32 limit = 2;
  int32 total_pages = 3;
  int32 total_items = 4;
  bool has_next = 5;
  bool has_previous = 6;
}

// Generic key-value pair
message KeyValue {
  string key = 1;
  string value = 2;
}

// Configuration entry
message ConfigEntry {
  string key = 1;
  oneof value {
    string string_value = 2;
    int64 int_value = 3;
    double float_value = 4;
    bool bool_value = 5;
    bytes bytes_value = 6;
  }
  bool encrypted = 7;
  bool required = 8;
}

// Health check information
message HealthCheck {
  Status status = 1;
  string message = 2;
  google.protobuf.Timestamp last_check = 3;
  map<string, string> checks = 4;
}

// Metadata
message Metadata {
  map<string, string> labels = 1;
  map<string, string> annotations = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  string created_by = 5;
  string updated_by = 6;
}