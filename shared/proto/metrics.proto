syntax = "proto3";

package ai.platform.v1;

option go_package = "github.com/ai-platform/shared/proto/v1";
option java_package = "io.platform.proto.v1";
option java_outer_classname = "MetricsProto";

import "common.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

// Metrics service definition
service MetricsService {
  // Metrics collection
  rpc CollectMetrics(CollectMetricsRequest) returns (CollectMetricsResponse);
  rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse);
  rpc GetMetricsByQuery(QueryMetricsRequest) returns (QueryMetricsResponse);
  
  // Alerting
  rpc CreateAlert(CreateAlertRequest) returns (CreateAlertResponse);
  rpc GetAlerts(GetAlertsRequest) returns (GetAlertsResponse);
  rpc UpdateAlert(UpdateAlertRequest) returns (UpdateAlertResponse);
  rpc DeleteAlert(DeleteAlertRequest) returns (DeleteAlertResponse);
  
  // Health monitoring
  rpc GetSystemHealth(GetSystemHealthRequest) returns (GetSystemHealthResponse);
  rpc GetComponentHealth(GetComponentHealthRequest) returns (GetComponentHealthResponse);
}

// Metric type enum
enum MetricType {
  METRIC_TYPE_UNSPECIFIED = 0;
  METRIC_TYPE_COUNTER = 1;
  METRIC_TYPE_GAUGE = 2;
  METRIC_TYPE_HISTOGRAM = 3;
  METRIC_TYPE_SUMMARY = 4;
}

// Alert severity enum
enum AlertSeverity {
  ALERT_SEVERITY_UNSPECIFIED = 0;
  ALERT_SEVERITY_LOW = 1;
  ALERT_SEVERITY_MEDIUM = 2;
  ALERT_SEVERITY_HIGH = 3;
  ALERT_SEVERITY_CRITICAL = 4;
}

// Alert status enum
enum AlertStatus {
  ALERT_STATUS_UNSPECIFIED = 0;
  ALERT_STATUS_ACTIVE = 1;
  ALERT_STATUS_ACKNOWLEDGED = 2;
  ALERT_STATUS_RESOLVED = 3;
  ALERT_STATUS_SUPPRESSED = 4;
}

// Metric definition
message Metric {
  string name = 1;
  MetricType type = 2;
  string description = 3;
  string unit = 4;
  map<string, string> labels = 5;
  repeated MetricSample samples = 6;
  google.protobuf.Timestamp timestamp = 7;
}

// Metric sample
message MetricSample {
  double value = 1;
  google.protobuf.Timestamp timestamp = 2;
  map<string, string> labels = 3;
}

// System metrics
message SystemMetrics {
  PlatformMetrics platform = 1;
  AgentMetrics agents = 2;
  WorkflowMetrics workflows = 3;
  ResourceMetrics resources = 4;
  PerformanceMetrics performance = 5;
}

// Platform metrics
message PlatformMetrics {
  int64 total_requests = 1;
  double requests_per_second = 2;
  double error_rate = 3;
  double average_response_time = 4;
  double p95_response_time = 5;
  double p99_response_time = 6;
  double availability = 7;
}

// Agent metrics (already defined in agent.proto, but adding aggregated view)
message AgentMetrics {
  int32 total_agents = 1;
  int32 active_agents = 2;
  int32 healthy_agents = 3;
  int32 unhealthy_agents = 4;
  int64 total_tasks_processed = 5;
  double average_task_duration = 6;
  double task_success_rate = 7;
  map<string, int32> agents_by_language = 8;
  map<string, int32> agents_by_status = 9;
}

// Workflow metrics
message WorkflowMetrics {
  int32 total_workflows = 1;
  int32 active_workflows = 2;
  int64 total_executions = 3;
  int64 running_executions = 4;
  int64 completed_executions = 5;
  int64 failed_executions = 6;
  double execution_success_rate = 7;
  double average_execution_time = 8;
}

// Resource metrics (extended from common.proto)
message ResourceMetrics {
  ClusterResources cluster = 1;
  repeated NodeResources nodes = 2;
  CostMetrics costs = 3;
}

// Cluster resources
message ClusterResources {
  int32 total_nodes = 1;
  int32 available_nodes = 2;
  ResourceCapacity total_capacity = 3;
  ResourceCapacity allocated_capacity = 4;
  ResourceUtilization utilization = 5;
}

// Node resources
message NodeResources {
  string node_name = 1;
  string node_type = 2;
  string zone = 3;
  ResourceCapacity capacity = 4;
  ResourceCapacity allocated = 5;
  ResourceUtilization utilization = 6;
  string status = 7;
}

// Resource capacity
message ResourceCapacity {
  double cpu_cores = 1;
  double memory_gb = 2;
  double storage_gb = 3;
  int32 gpu_count = 4;
}

// Resource utilization
message ResourceUtilization {
  double cpu_percent = 1;
  double memory_percent = 2;
  double storage_percent = 3;
  double gpu_percent = 4;
  double network_mbps = 5;
}

// Cost metrics
message CostMetrics {
  double total_cost = 1;
  double compute_cost = 2;
  double storage_cost = 3;
  double network_cost = 4;
  double ai_model_cost = 5;
  map<string, double> cost_by_service = 6;
  map<string, double> cost_by_environment = 7;
}

// Performance metrics
message PerformanceMetrics {
  double throughput = 1;
  double latency_p50 = 2;
  double latency_p95 = 3;
  double latency_p99 = 4;
  double error_rate = 5;
  double success_rate = 6;
  int64 concurrent_users = 7;
}

// Alert definition
message Alert {
  string id = 1;
  string name = 2;
  string description = 3;
  AlertSeverity severity = 4;
  AlertStatus status = 5;
  AlertRule rule = 6;
  repeated string notification_channels = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp triggered_at = 9;
  google.protobuf.Timestamp acknowledged_at = 10;
  google.protobuf.Timestamp resolved_at = 11;
  string acknowledged_by = 12;
  map<string, string> labels = 13;
}

// Alert rule
message AlertRule {
  string metric = 1;
  string condition = 2;  // >, <, >=, <=, ==, !=
  double threshold = 3;
  google.protobuf.Duration duration = 4;
  string query = 5;
  map<string, string> labels = 6;
}

// Health status
message HealthStatus {
  string overall_status = 1;  // healthy, degraded, unhealthy
  google.protobuf.Timestamp last_updated = 2;
  map<string, ComponentHealth> components = 3;
  repeated string issues = 4;
}

// Component health
message ComponentHealth {
  string name = 1;
  string status = 2;  // healthy, degraded, unhealthy
  string message = 3;
  google.protobuf.Timestamp last_check = 4;
  map<string, string> details = 5;
}

// Collect metrics request
message CollectMetricsRequest {
  repeated Metric metrics = 1;
  string source = 2;
  map<string, string> labels = 3;
}

// Collect metrics response
message CollectMetricsResponse {
  int32 metrics_received = 1;
  Error error = 2;
}

// Get metrics request
message GetMetricsRequest {
  repeated string metric_names = 1;
  google.protobuf.Timestamp from = 2;
  google.protobuf.Timestamp to = 3;
  string resolution = 4;  // 1m, 5m, 1h, 1d
  map<string, string> labels = 5;
}

// Get metrics response
message GetMetricsResponse {
  repeated Metric metrics = 1;
  TimeRange time_range = 2;
  Error error = 3;
}

// Query metrics request
message QueryMetricsRequest {
  string query = 1;  // PromQL-like query
  google.protobuf.Timestamp from = 2;
  google.protobuf.Timestamp to = 3;
  string resolution = 4;
}

// Query metrics response
message QueryMetricsResponse {
  repeated Metric metrics = 1;
  string query = 2;
  TimeRange time_range = 3;
  Error error = 4;
}

// Create alert request
message CreateAlertRequest {
  string name = 1;
  string description = 2;
  AlertSeverity severity = 3;
  AlertRule rule = 4;
  repeated string notification_channels = 5;
  map<string, string> labels = 6;
}

// Create alert response
message CreateAlertResponse {
  Alert alert = 1;
  Error error = 2;
}

// Get alerts request
message GetAlertsRequest {
  AlertSeverity severity = 1;
  AlertStatus status = 2;
  string component = 3;
  int32 limit = 4;
}

// Get alerts response
message GetAlertsResponse {
  repeated Alert alerts = 1;
  AlertsSummary summary = 2;
  Error error = 3;
}

// Alerts summary
message AlertsSummary {
  int32 total_alerts = 1;
  map<string, int32> by_severity = 2;
  map<string, int32> by_status = 3;
}

// Update alert request
message UpdateAlertRequest {
  string alert_id = 1;
  AlertStatus status = 2;
  string acknowledged_by = 3;
  string notes = 4;
}

// Update alert response
message UpdateAlertResponse {
  Alert alert = 1;
  Error error = 2;
}

// Delete alert request
message DeleteAlertRequest {
  string alert_id = 1;
}

// Delete alert response
message DeleteAlertResponse {
  string message = 1;
  Error error = 2;
}

// Get system health request
message GetSystemHealthRequest {
  bool include_components = 1;
}

// Get system health response
message GetSystemHealthResponse {
  HealthStatus health = 1;
  SystemMetrics metrics = 2;
  Error error = 3;
}

// Get component health request
message GetComponentHealthRequest {
  string component_name = 1;
}

// Get component health response
message GetComponentHealthResponse {
  ComponentHealth health = 1;
  Error error = 2;
}