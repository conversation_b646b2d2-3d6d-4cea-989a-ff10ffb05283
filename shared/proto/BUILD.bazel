load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_java//java:defs.bzl", "java_proto_library")
load("@rules_python//python:proto.bzl", "py_proto_library")

# Protocol buffer definitions
proto_library(
    name = "platform_proto",
    srcs = [
        "agent.proto",
        "workflow.proto", 
        "common.proto",
        "metrics.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

# Go bindings
go_proto_library(
    name = "platform_proto_go",
    importpath = "github.com/ai-platform/shared/proto/v1",
    proto = ":platform_proto",
    visibility = ["//visibility:public"],
)

# Java bindings
java_proto_library(
    name = "platform_proto_java",
    deps = [":platform_proto"],
    visibility = ["//visibility:public"],
)

# Python bindings  
py_proto_library(
    name = "platform_proto_python",
    deps = [":platform_proto"],
    visibility = ["//visibility:public"],
)