load("@rules_java//java:defs.bzl", "java_library", "java_test")

# Configuration package
java_library(
    name = "config",
    srcs = glob(["src/main/java/io/platform/common/config/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
        "@maven//:com_fasterxml_jackson_dataformat_jackson_dataformat_yaml",
        "@maven//:com_fasterxml_jackson_datatype_jackson_datatype_jsr310",
        "@maven//:org_springframework_spring_core",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:jakarta_validation_jakarta_validation_api",
    ],
)

java_test(
    name = "config_test",
    srcs = glob(["src/test/java/io/platform/common/config/**/*.java"]),
    deps = [
        ":config",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_springframework_boot_spring_boot_starter_test",
    ],
)

# Auth package
java_library(
    name = "auth",
    srcs = glob(["src/main/java/io/platform/common/auth/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:com_auth0_java_jwt",
        "@maven//:at_favre_lib_bcrypt",
        "@maven//:org_slf4j_slf4j_api",
    ],
)

java_test(
    name = "auth_test",
    srcs = glob(["src/test/java/io/platform/common/auth/**/*.java"]),
    deps = [
        ":auth",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
    ],
)

# Database package
java_library(
    name = "database",
    srcs = glob(["src/main/java/io/platform/common/database/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:com_zaxxer_HikariCP",
        "@maven//:org_postgresql_postgresql",
        "@maven//:com_mysql_mysql_connector_j",
        "@maven//:org_mongodb_mongodb_driver_sync",
        "@maven//:io_lettuce_lettuce_core",
        "@maven//:org_slf4j_slf4j_api",
    ],
)

java_test(
    name = "database_test",
    srcs = glob(["src/test/java/io/platform/common/database/**/*.java"]),
    deps = [
        ":database",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_testcontainers_junit_jupiter",
        "@maven//:org_testcontainers_postgresql",
    ],
)

# Logging package
java_library(
    name = "logging",
    srcs = glob(["src/main/java/io/platform/common/logging/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:ch_qos_logback_logback_classic",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
    ],
)

java_test(
    name = "logging_test",
    srcs = glob(["src/test/java/io/platform/common/logging/**/*.java"]),
    deps = [
        ":logging",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
    ],
)

# Metrics package
java_library(
    name = "metrics",
    srcs = glob(["src/main/java/io/platform/common/metrics/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:io_micrometer_micrometer_core",
        "@maven//:io_micrometer_micrometer_registry_prometheus",
        "@maven//:org_slf4j_slf4j_api",
    ],
)

java_test(
    name = "metrics_test",
    srcs = glob(["src/test/java/io/platform/common/metrics/**/*.java"]),
    deps = [
        ":metrics",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
    ],
)

# Utils package
java_library(
    name = "utils",
    srcs = glob(["src/main/java/io/platform/common/utils/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_codec_commons_codec",
        "@maven//:jakarta_validation_jakarta_validation_api",
        "@maven//:org_hibernate_validator_hibernate_validator",
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
    ],
)

java_test(
    name = "utils_test",
    srcs = glob(["src/test/java/io/platform/common/utils/**/*.java"]),
    deps = [
        ":utils",
        "@maven//:org_junit_jupiter_junit_jupiter",
        "@maven//:org_mockito_mockito_core",
    ],
)

# All libraries combined
java_library(
    name = "common",
    visibility = ["//visibility:public"],
    exports = [
        ":config",
        ":auth",
        ":database",
        ":logging",
        ":metrics",
        ":utils",
    ],
)