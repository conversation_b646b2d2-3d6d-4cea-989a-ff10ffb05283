# AI Platform - Java Common Libraries

This package provides shared Java libraries for the AI-Native Agent Platform. It contains common functionality that can be used across all Java-based services in the platform.

## Packages

### Config (`io.platform.common.config`)
Configuration management with support for YAML/JSON files and environment variables.

**Features:**
- Multi-source configuration loading (files, classpath, environment variables)
- Jackson-based JSON/YAML parsing
- Spring Environment integration
- Configuration validation with Jakarta Bean Validation
- Hierarchical property resolution

**Usage:**
```java
ConfigurationLoader loader = new ConfigurationLoader(environment);
PlatformConfig config = loader.loadConfiguration(PlatformConfig.class, "application.yml");
```

### Auth (`io.platform.common.auth`)
Authentication and authorization utilities with JWT token support.

**Features:**
- JWT token generation and validation with Auth0 library
- Password hashing with BCrypt
- Token refresh mechanism
- User claims and role-based access control
- Token revocation support

**Usage:**
```java
JwtTokenProvider provider = new JwtTokenProvider(secret, accessValidity, refreshValidity, issuer);
TokenPair tokens = provider.generateToken(userClaims);
UserClaims claims = provider.validateToken(accessToken);
```

### Database (`io.platform.common.database`)
Database connection management for multiple database types.

**Features:**
- Support for PostgreSQL, MySQL, MongoDB, and Redis
- HikariCP connection pooling
- Connection health monitoring
- Repository pattern interfaces
- Transaction management utilities

### Logging (`io.platform.common.logging`)
Structured logging with SLF4J and Logback.

**Features:**
- JSON structured logging with Logstash encoder
- Configurable log levels and outputs
- Context-aware logging with MDC
- Performance-optimized logging
- Spring Boot integration

### Metrics (`io.platform.common.metrics`)
Micrometer-based metrics collection for monitoring.

**Features:**
- Counter, Gauge, Timer, and Distribution Summary metrics
- Prometheus registry integration
- Custom metrics with tags
- JVM and system metrics
- HTTP endpoint for metrics export

### Utils (`io.platform.common.utils`)
Utility functions for validation, encoding, and common operations.

**Features:**
- Jakarta Bean Validation support
- Email, UUID, and custom validators
- Encoding/decoding utilities (Base64, Hex, JSON)
- Encryption utilities with AES
- Data masking for sensitive information

## Technology Stack

- **Java 21**: Latest LTS version with modern language features
- **Spring Boot 3.2**: Modern Spring framework with native support
- **Jackson**: JSON/YAML processing and data binding
- **Micrometer**: Metrics collection and monitoring
- **SLF4J/Logback**: Logging framework
- **HikariCP**: High-performance JDBC connection pooling
- **Auth0 JWT**: JWT token processing
- **BCrypt**: Password hashing
- **JUnit 5**: Testing framework
- **Testcontainers**: Integration testing

## Building

This project uses Maven for dependency management and Bazel for building:

```bash
# Maven build
mvn clean test

# Bazel build
bazel build //shared/common/java:common
bazel test //shared/common/java:all
```

## Testing

Comprehensive test coverage (>80% target) with unit and integration tests:

```bash
# Run all tests
mvn test

# Run specific package tests
mvn test -Dtest="io.platform.common.auth.*Test"

# Integration tests with Testcontainers
mvn verify
```

## Code Quality

The project enforces high code quality standards:

- **Checkstyle**: Code style enforcement
- **SpotBugs**: Static analysis for bug detection
- **JaCoCo**: Code coverage measurement (80% minimum)
- **SonarQube**: Continuous code quality inspection

## Configuration Examples

### Application Configuration (application.yml)
```yaml
database:
  driver: postgresql
  host: localhost
  port: 5432
  database: platform
  username: ${DB_USERNAME:platform}
  password: ${DB_PASSWORD:secret}
  max_pool_size: 10

logging:
  level: INFO
  format: json
  output: stdout

metrics:
  enabled: true
  port: 9090
  path: /metrics

security:
  jwt_secret: ${JWT_SECRET}
  token_duration_hours: 24
  encryption_key: ${ENCRYPTION_KEY}
```

### Spring Boot Integration
```java
@Configuration
@EnableConfigurationProperties
public class PlatformConfiguration {
    
    @Bean
    @ConfigurationProperties("platform")
    public PlatformConfig platformConfig() {
        return new PlatformConfig();
    }
    
    @Bean
    public JwtTokenProvider jwtTokenProvider(PlatformConfig config) {
        return new JwtTokenProvider(
            config.getSecurity().getJwtSecret(),
            config.getSecurity().getTokenDurationHours() * 3600,
            config.getSecurity().getTokenDurationHours() * 3600 * 7,
            "platform"
        );
    }
}
```

## Development Standards

- Java 21+ required
- Spring Boot 3.x compatibility
- Comprehensive JavaDoc documentation
- Thread-safe implementations
- Immutable objects where possible
- Builder patterns for complex objects
- Extensive test coverage
- Modern Java features (records, pattern matching, etc.)

## Integration

Import in other Java services:

```xml
<dependency>
    <groupId>io.platform</groupId>
    <artifactId>common-java</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

Or via Bazel:
```python
java_library(
    name = "my_service",
    deps = ["//shared/common/java:common"],
)
```

## Performance Considerations

- Lazy initialization where appropriate
- Connection pooling for database access
- Caching for expensive operations
- Efficient JSON processing with Jackson
- Minimal memory allocation in hot paths
- Async logging to avoid blocking

This package is designed to provide a solid foundation for building scalable, maintainable Java services in the AI Platform ecosystem.