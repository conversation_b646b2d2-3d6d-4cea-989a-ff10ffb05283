package io.platform.common.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Main platform configuration class that aggregates all component configurations.
 */
public class PlatformConfig {
    
    @Valid
    @JsonProperty("database")
    private DatabaseConfig database = new DatabaseConfig();
    
    @Valid
    @JsonProperty("logging")
    private LoggingConfig logging = new LoggingConfig();
    
    @Valid
    @JsonProperty("metrics")
    private MetricsConfig metrics = new MetricsConfig();
    
    @Valid
    @JsonProperty("security")
    private SecurityConfig security = new SecurityConfig();
    
    @JsonProperty("custom")
    private Map<String, Object> custom = new HashMap<>();
    
    // Getters and setters
    public DatabaseConfig getDatabase() { return database; }
    public void setDatabase(DatabaseConfig database) { this.database = database; }
    
    public LoggingConfig getLogging() { return logging; }
    public void setLogging(LoggingConfig logging) { this.logging = logging; }
    
    public MetricsConfig getMetrics() { return metrics; }
    public void setMetrics(MetricsConfig metrics) { this.metrics = metrics; }
    
    public SecurityConfig getSecurity() { return security; }
    public void setSecurity(SecurityConfig security) { this.security = security; }
    
    public Map<String, Object> getCustom() { return custom; }
    public void setCustom(Map<String, Object> custom) { this.custom = custom; }
    
    /**
     * Database configuration
     */
    public static class DatabaseConfig {
        @NotBlank
        @JsonProperty("driver")
        private String driver = "postgresql";
        
        @NotBlank
        @JsonProperty("host")
        private String host = "localhost";
        
        @Min(1)
        @Max(65535)
        @JsonProperty("port")
        private int port = 5432;
        
        @JsonProperty("database")
        private String database;
        
        @JsonProperty("username")
        private String username;
        
        @JsonProperty("password")
        private String password;
        
        @JsonProperty("options")
        private Map<String, String> options = new HashMap<>();
        
        @Min(1)
        @JsonProperty("max_pool_size")
        private int maxPoolSize = 10;
        
        @Min(0)
        @JsonProperty("min_pool_size")
        private int minPoolSize = 2;
        
        @JsonProperty("connection_timeout")
        private Duration connectionTimeout = Duration.ofSeconds(30);
        
        // Getters and setters
        public String getDriver() { return driver; }
        public void setDriver(String driver) { this.driver = driver; }
        
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        
        public String getDatabase() { return database; }
        public void setDatabase(String database) { this.database = database; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public Map<String, String> getOptions() { return options; }
        public void setOptions(Map<String, String> options) { this.options = options; }
        
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        
        public int getMinPoolSize() { return minPoolSize; }
        public void setMinPoolSize(int minPoolSize) { this.minPoolSize = minPoolSize; }
        
        public Duration getConnectionTimeout() { return connectionTimeout; }
        public void setConnectionTimeout(Duration connectionTimeout) { this.connectionTimeout = connectionTimeout; }
    }
    
    /**
     * Logging configuration
     */
    public static class LoggingConfig {
        @NotBlank
        @JsonProperty("level")
        private String level = "INFO";
        
        @NotBlank
        @JsonProperty("format")
        private String format = "json";
        
        @NotBlank
        @JsonProperty("output")
        private String output = "stdout";
        
        @JsonProperty("file_path")
        private String filePath;
        
        @JsonProperty("development")
        private boolean development = false;
        
        @JsonProperty("sampling_enabled")
        private boolean samplingEnabled = false;
        
        // Getters and setters
        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }
        
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        
        public String getOutput() { return output; }
        public void setOutput(String output) { this.output = output; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public boolean isDevelopment() { return development; }
        public void setDevelopment(boolean development) { this.development = development; }
        
        public boolean isSamplingEnabled() { return samplingEnabled; }
        public void setSamplingEnabled(boolean samplingEnabled) { this.samplingEnabled = samplingEnabled; }
    }
    
    /**
     * Metrics configuration
     */
    public static class MetricsConfig {
        @JsonProperty("enabled")
        private boolean enabled = true;
        
        @Min(1)
        @Max(65535)
        @JsonProperty("port")
        private int port = 9090;
        
        @NotBlank
        @JsonProperty("path")
        private String path = "/metrics";
        
        @JsonProperty("namespace")
        private String namespace = "platform";
        
        @JsonProperty("subsystem")
        private String subsystem;
        
        // Getters and setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public String getNamespace() { return namespace; }
        public void setNamespace(String namespace) { this.namespace = namespace; }
        
        public String getSubsystem() { return subsystem; }
        public void setSubsystem(String subsystem) { this.subsystem = subsystem; }
    }
    
    /**
     * Security configuration
     */
    public static class SecurityConfig {
        @NotBlank
        @JsonProperty("jwt_secret")
        private String jwtSecret;
        
        @Min(1)
        @JsonProperty("token_duration_hours")
        private int tokenDurationHours = 24;
        
        @NotBlank
        @JsonProperty("encryption_key")
        private String encryptionKey;
        
        @JsonProperty("tls_cert_path")
        private String tlsCertPath;
        
        @JsonProperty("tls_key_path")
        private String tlsKeyPath;
        
        @JsonProperty("bcrypt_cost")
        private int bcryptCost = 12;
        
        // Getters and setters
        public String getJwtSecret() { return jwtSecret; }
        public void setJwtSecret(String jwtSecret) { this.jwtSecret = jwtSecret; }
        
        public int getTokenDurationHours() { return tokenDurationHours; }
        public void setTokenDurationHours(int tokenDurationHours) { this.tokenDurationHours = tokenDurationHours; }
        
        public String getEncryptionKey() { return encryptionKey; }
        public void setEncryptionKey(String encryptionKey) { this.encryptionKey = encryptionKey; }
        
        public String getTlsCertPath() { return tlsCertPath; }
        public void setTlsCertPath(String tlsCertPath) { this.tlsCertPath = tlsCertPath; }
        
        public String getTlsKeyPath() { return tlsKeyPath; }
        public void setTlsKeyPath(String tlsKeyPath) { this.tlsKeyPath = tlsKeyPath; }
        
        public int getBcryptCost() { return bcryptCost; }
        public void setBcryptCost(int bcryptCost) { this.bcryptCost = bcryptCost; }
    }
}