package io.platform.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Configuration loader that supports multiple sources (files, environment variables, system properties)
 * with hierarchical property resolution.
 */
public class ConfigurationLoader {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationLoader.class);
    
    private final ObjectMapper yamlMapper;
    private final ObjectMapper jsonMapper;
    private final Map<String, Object> configCache;
    private final Environment environment;
    
    public ConfigurationLoader(Environment environment) {
        this.environment = environment;
        this.configCache = new ConcurrentHashMap<>();
        
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
        this.yamlMapper.registerModule(new JavaTimeModule());
        
        this.jsonMapper = new ObjectMapper();
        this.jsonMapper.registerModule(new JavaTimeModule());
    }
    
    /**
     * Load configuration from multiple sources with priority:
     * 1. System properties
     * 2. Environment variables
     * 3. Configuration files
     * 4. Default values
     */
    public <T> T loadConfiguration(Class<T> configClass, String... configFiles) {
        String cacheKey = configClass.getName();
        
        if (configCache.containsKey(cacheKey)) {
            return configClass.cast(configCache.get(cacheKey));
        }
        
        try {
            T config = loadFromFiles(configClass, configFiles);
            config = applyEnvironmentOverrides(config);
            
            configCache.put(cacheKey, config);
            logger.info("Loaded configuration for {}", configClass.getSimpleName());
            
            return config;
        } catch (Exception e) {
            logger.error("Failed to load configuration for {}", configClass.getSimpleName(), e);
            throw new ConfigurationException("Failed to load configuration", e);
        }
    }
    
    private <T> T loadFromFiles(Class<T> configClass, String... configFiles) throws IOException {
        T config = null;
        
        for (String configFile : configFiles) {
            if (configFile != null && !configFile.isEmpty()) {
                T fileConfig = loadFromFile(configClass, configFile);
                if (fileConfig != null) {
                    config = fileConfig;
                    break;
                }
            }
        }
        
        if (config == null) {
            // Try to create instance with default constructor
            try {
                config = configClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new ConfigurationException("Failed to create default configuration instance", e);
            }
        }
        
        return config;
    }
    
    private <T> T loadFromFile(Class<T> configClass, String configFile) throws IOException {
        Path filePath = Path.of(configFile);
        
        if (Files.exists(filePath)) {
            logger.debug("Loading configuration from file: {}", configFile);
            try (InputStream inputStream = Files.newInputStream(filePath)) {
                if (configFile.endsWith(".yaml") || configFile.endsWith(".yml")) {
                    return yamlMapper.readValue(inputStream, configClass);
                } else if (configFile.endsWith(".json")) {
                    return jsonMapper.readValue(inputStream, configClass);
                }
            }
        } else {
            // Try loading from classpath
            try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(configFile)) {
                if (inputStream != null) {
                    logger.debug("Loading configuration from classpath: {}", configFile);
                    if (configFile.endsWith(".yaml") || configFile.endsWith(".yml")) {
                        return yamlMapper.readValue(inputStream, configClass);
                    } else if (configFile.endsWith(".json")) {
                        return jsonMapper.readValue(inputStream, configClass);
                    }
                }
            }
        }
        
        return null;
    }
    
    private <T> T applyEnvironmentOverrides(T config) {
        if (environment == null) {
            return config;
        }
        
        // This is a simplified implementation
        // In a real implementation, you would use reflection or a mapping strategy
        // to apply environment variable overrides based on naming conventions
        
        return config;
    }
    
    /**
     * Get a property value with environment variable override support
     */
    public String getProperty(String key, String defaultValue) {
        // Check system properties first
        String value = System.getProperty(key);
        if (value != null) {
            return value;
        }
        
        // Check environment variables
        value = System.getenv(key);
        if (value != null) {
            return value;
        }
        
        // Check environment variables with underscore format
        String envKey = key.toUpperCase().replace('.', '_').replace('-', '_');
        value = System.getenv(envKey);
        if (value != null) {
            return value;
        }
        
        // Use Spring environment if available
        if (environment != null) {
            value = environment.getProperty(key);
            if (value != null) {
                return value;
            }
        }
        
        return defaultValue;
    }
    
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key, null);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key, null);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer value for property {}: {}", key, value);
            }
        }
        return defaultValue;
    }
    
    public void clearCache() {
        configCache.clear();
    }
    
    public static class ConfigurationException extends RuntimeException {
        public ConfigurationException(String message) {
            super(message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}