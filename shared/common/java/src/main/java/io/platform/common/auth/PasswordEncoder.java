package io.platform.common.auth;

import at.favre.lib.crypto.bcrypt.BCrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Password encoder using BCrypt
 */
public class PasswordEncoder {
    private static final Logger logger = LoggerFactory.getLogger(PasswordEncoder.class);
    
    private final int cost;
    
    public PasswordEncoder(int cost) {
        this.cost = cost;
    }
    
    public PasswordEncoder() {
        this(12); // Default cost
    }
    
    /**
     * Encode a raw password
     */
    public String encode(String rawPassword) {
        if (rawPassword == null) {
            throw new IllegalArgumentException("Password cannot be null");
        }
        
        try {
            return BCrypt.withDefaults().hashToString(cost, rawPassword.toCharArray());
        } catch (Exception e) {
            logger.error("Failed to encode password", e);
            throw new RuntimeException("Failed to encode password", e);
        }
    }
    
    /**
     * Verify a raw password against an encoded password
     */
    public boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        
        try {
            BCrypt.Result result = BCrypt.verifyer().verify(rawPassword.toCharArray(), encodedPassword);
            return result.verified;
        } catch (Exception e) {
            logger.error("Failed to verify password", e);
            return false;
        }
    }
    
    /**
     * Check if a password needs to be re-encoded (e.g., cost has changed)
     */
    public boolean needsUpgrade(String encodedPassword) {
        if (encodedPassword == null) {
            return true;
        }
        
        try {
            // Extract cost from encoded password
            String[] parts = encodedPassword.split("\\$");
            if (parts.length >= 3) {
                int currentCost = Integer.parseInt(parts[2]);
                return currentCost < this.cost;
            }
        } catch (Exception e) {
            logger.warn("Failed to parse encoded password for upgrade check", e);
        }
        
        return true;
    }
}