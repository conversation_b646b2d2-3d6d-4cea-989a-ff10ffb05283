package io.platform.common.auth;

import java.time.Instant;
import java.util.Objects;

/**
 * Token pair containing access and refresh tokens
 */
public class TokenPair {
    private final String accessToken;
    private final String refreshToken;
    private final Instant expiresAt;
    private final String tokenType;
    
    public TokenPair(String accessToken, String refreshToken, Instant expiresAt, String tokenType) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresAt = expiresAt;
        this.tokenType = tokenType;
    }
    
    public String getAccessToken() { return accessToken; }
    public String getRefreshToken() { return refreshToken; }
    public Instant getExpiresAt() { return expiresAt; }
    public String getTokenType() { return tokenType; }
    
    public boolean isExpired() {
        return expiresAt != null && Instant.now().isAfter(expiresAt);
    }
    
    public long getExpiresInSeconds() {
        if (expiresAt == null) return 0;
        return Math.max(0, expiresAt.getEpochSecond() - Instant.now().getEpochSecond());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TokenPair tokenPair = (TokenPair) o;
        return Objects.equals(accessToken, tokenPair.accessToken) &&
               Objects.equals(refreshToken, tokenPair.refreshToken) &&
               Objects.equals(expiresAt, tokenPair.expiresAt) &&
               Objects.equals(tokenType, tokenPair.tokenType);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(accessToken, refreshToken, expiresAt, tokenType);
    }
    
    @Override
    public String toString() {
        return "TokenPair{" +
               "accessToken='[MASKED]'" +
               ", refreshToken='[MASKED]'" +
               ", expiresAt=" + expiresAt +
               ", tokenType='" + tokenType + '\'' +
               '}';
    }
}