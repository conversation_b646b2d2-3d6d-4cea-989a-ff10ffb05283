package io.platform.common.auth;

import java.util.List;
import java.util.Objects;

/**
 * User claims for authentication and authorization
 */
public class UserClaims {
    private final String userId;
    private final String username;
    private final List<String> roles;
    private final List<String> permissions;
    private final String agentId;
    
    private UserClaims(Builder builder) {
        this.userId = builder.userId;
        this.username = builder.username;
        this.roles = builder.roles;
        this.permissions = builder.permissions;
        this.agentId = builder.agentId;
    }
    
    public String getUserId() { return userId; }
    public String getUsername() { return username; }
    public List<String> getRoles() { return roles; }
    public List<String> getPermissions() { return permissions; }
    public String getAgentId() { return agentId; }
    
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
    
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    public boolean hasAnyRole(String... roles) {
        if (this.roles == null) return false;
        for (String role : roles) {
            if (this.roles.contains(role)) return true;
        }
        return false;
    }
    
    public boolean hasAnyPermission(String... permissions) {
        if (this.permissions == null) return false;
        for (String permission : permissions) {
            if (this.permissions.contains(permission)) return true;
        }
        return false;
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private String userId;
        private String username;
        private List<String> roles;
        private List<String> permissions;
        private String agentId;
        
        public Builder userId(String userId) {
            this.userId = userId;
            return this;
        }
        
        public Builder username(String username) {
            this.username = username;
            return this;
        }
        
        public Builder roles(List<String> roles) {
            this.roles = roles;
            return this;
        }
        
        public Builder permissions(List<String> permissions) {
            this.permissions = permissions;
            return this;
        }
        
        public Builder agentId(String agentId) {
            this.agentId = agentId;
            return this;
        }
        
        public UserClaims build() {
            return new UserClaims(this);
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserClaims that = (UserClaims) o;
        return Objects.equals(userId, that.userId) &&
               Objects.equals(username, that.username) &&
               Objects.equals(roles, that.roles) &&
               Objects.equals(permissions, that.permissions) &&
               Objects.equals(agentId, that.agentId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, username, roles, permissions, agentId);
    }
    
    @Override
    public String toString() {
        return "UserClaims{" +
               "userId='" + userId + '\'' +
               ", username='" + username + '\'' +
               ", roles=" + roles +
               ", permissions=" + permissions +
               ", agentId='" + agentId + '\'' +
               '}';
    }
}