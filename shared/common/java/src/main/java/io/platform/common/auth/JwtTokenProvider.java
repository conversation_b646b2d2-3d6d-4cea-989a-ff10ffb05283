package io.platform.common.auth;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JWT token provider for authentication and authorization.
 */
public class JwtTokenProvider implements TokenProvider {
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);
    
    private final Algorithm algorithm;
    private final JWTVerifier verifier;
    private final long accessTokenValidityInSeconds;
    private final long refreshTokenValidityInSeconds;
    private final String issuer;
    private final Set<String> revokedTokens;
    
    public JwtTokenProvider(String secret, long accessTokenValidityInSeconds, 
                           long refreshTokenValidityInSeconds, String issuer) {
        this.algorithm = Algorithm.HMAC256(secret);
        this.verifier = JWT.require(algorithm).withIssuer(issuer).build();
        this.accessTokenValidityInSeconds = accessTokenValidityInSeconds;
        this.refreshTokenValidityInSeconds = refreshTokenValidityInSeconds;
        this.issuer = issuer;
        this.revokedTokens = ConcurrentHashMap.newKeySet();
    }
    
    @Override
    public TokenPair generateToken(UserClaims claims) {
        try {
            Instant now = Instant.now();
            Instant accessExpiry = now.plus(accessTokenValidityInSeconds, ChronoUnit.SECONDS);
            Instant refreshExpiry = now.plus(refreshTokenValidityInSeconds, ChronoUnit.SECONDS);
            
            // Generate access token
            String accessToken = JWT.create()
                    .withIssuer(issuer)
                    .withSubject(claims.getUserId())
                    .withClaim("username", claims.getUsername())
                    .withClaim("roles", claims.getRoles())
                    .withClaim("permissions", claims.getPermissions())
                    .withClaim("agent_id", claims.getAgentId())
                    .withIssuedAt(Date.from(now))
                    .withExpiresAt(Date.from(accessExpiry))
                    .withJWTId(UUID.randomUUID().toString())
                    .sign(algorithm);
            
            // Generate refresh token (simpler claims)
            String refreshToken = JWT.create()
                    .withIssuer(issuer)
                    .withSubject(claims.getUserId())
                    .withClaim("username", claims.getUsername())
                    .withClaim("type", "refresh")
                    .withIssuedAt(Date.from(now))
                    .withExpiresAt(Date.from(refreshExpiry))
                    .withJWTId(UUID.randomUUID().toString())
                    .sign(algorithm);
            
            return new TokenPair(accessToken, refreshToken, accessExpiry, "Bearer");
            
        } catch (JWTCreationException e) {
            logger.error("Failed to generate JWT token", e);
            throw new TokenException("Failed to generate token", e);
        }
    }
    
    @Override
    public UserClaims validateToken(String token) {
        try {
            if (revokedTokens.contains(token)) {
                throw new TokenException("Token has been revoked");
            }
            
            DecodedJWT jwt = verifier.verify(token);
            
            return UserClaims.builder()
                    .userId(jwt.getSubject())
                    .username(jwt.getClaim("username").asString())
                    .roles(jwt.getClaim("roles").asList(String.class))
                    .permissions(jwt.getClaim("permissions").asList(String.class))
                    .agentId(jwt.getClaim("agent_id").asString())
                    .build();
                    
        } catch (JWTVerificationException e) {
            logger.error("Failed to validate JWT token", e);
            throw new TokenException("Invalid token", e);
        }
    }
    
    @Override
    public TokenPair refreshToken(String refreshToken) {
        try {
            if (revokedTokens.contains(refreshToken)) {
                throw new TokenException("Refresh token has been revoked");
            }
            
            DecodedJWT jwt = verifier.verify(refreshToken);
            
            // Verify it's a refresh token
            String tokenType = jwt.getClaim("type").asString();
            if (!"refresh".equals(tokenType)) {
                throw new TokenException("Invalid token type for refresh");
            }
            
            // Create new claims from refresh token
            UserClaims claims = UserClaims.builder()
                    .userId(jwt.getSubject())
                    .username(jwt.getClaim("username").asString())
                    .roles(Collections.emptyList()) // Will need to be fetched from user service
                    .permissions(Collections.emptyList()) // Will need to be fetched from user service
                    .build();
            
            return generateToken(claims);
            
        } catch (JWTVerificationException e) {
            logger.error("Failed to refresh JWT token", e);
            throw new TokenException("Invalid refresh token", e);
        }
    }
    
    @Override
    public void revokeToken(String token) {
        revokedTokens.add(token);
        logger.debug("Token revoked");
    }
    
    @Override
    public boolean isTokenRevoked(String token) {
        return revokedTokens.contains(token);
    }
    
    /**
     * Extract token ID for logging and tracking
     */
    public String getTokenId(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getId();
        } catch (Exception e) {
            logger.warn("Failed to extract token ID", e);
            return null;
        }
    }
    
    /**
     * Clean up expired revoked tokens periodically
     */
    public void cleanupExpiredTokens() {
        // In a real implementation, you would check expiration times
        // This is a simplified version
        logger.debug("Cleaning up expired revoked tokens");
        // Implementation would require storing expiration times with tokens
    }
    
    /**
     * Get remaining validity time for a token
     */
    public long getTokenValidityRemaining(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            Date expiry = jwt.getExpiresAt();
            if (expiry != null) {
                return expiry.getTime() - System.currentTimeMillis();
            }
        } catch (Exception e) {
            logger.warn("Failed to get token validity", e);
        }
        return 0;
    }
}