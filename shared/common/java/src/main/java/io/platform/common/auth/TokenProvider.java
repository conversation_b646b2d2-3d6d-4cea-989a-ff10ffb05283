package io.platform.common.auth;

/**
 * Interface for token providers (JWT, OAuth, etc.)
 */
public interface TokenProvider {
    
    /**
     * Generate a new token pair (access + refresh tokens)
     */
    TokenPair generateToken(UserClaims claims);
    
    /**
     * Validate a token and return the claims
     */
    UserClaims validateToken(String token) throws TokenException;
    
    /**
     * Refresh an access token using a refresh token
     */
    TokenPair refreshToken(String refreshToken) throws TokenException;
    
    /**
     * Revoke a token
     */
    void revokeToken(String token);
    
    /**
     * Check if a token is revoked
     */
    boolean isTokenRevoked(String token);
}