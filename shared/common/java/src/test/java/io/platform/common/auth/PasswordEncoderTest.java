package io.platform.common.auth;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class PasswordEncoderTest {
    
    private PasswordEncoder passwordEncoder;
    
    @BeforeEach
    void setUp() {
        passwordEncoder = new PasswordEncoder(4); // Lower cost for faster tests
    }
    
    @Test
    void testEncodePassword() {
        String rawPassword = "testPassword123!";
        String encoded = passwordEncoder.encode(rawPassword);
        
        assertNotNull(encoded);
        assertNotEquals(rawPassword, encoded);
        assertTrue(encoded.startsWith("$2a$04$")); // BCrypt format with cost 4
    }
    
    @Test
    void testPasswordMatches() {
        String rawPassword = "testPassword123!";
        String encoded = passwordEncoder.encode(rawPassword);
        
        assertTrue(passwordEncoder.matches(rawPassword, encoded));
        assertFalse(passwordEncoder.matches("wrongPassword", encoded));
        assertFalse(passwordEncoder.matches(rawPassword, "wrongHash"));
    }
    
    @Test
    void testPasswordMatchesWithNullValues() {
        assertFalse(passwordEncoder.matches(null, "hash"));
        assertFalse(passwordEncoder.matches("password", null));
        assertFalse(passwordEncoder.matches(null, null));
    }
    
    @Test
    void testEncodeNullPassword() {
        assertThrows(IllegalArgumentException.class, () -> {
            passwordEncoder.encode(null);
        });
    }
    
    @Test
    void testNeedsUpgrade() {
        // Create encoder with higher cost
        PasswordEncoder higherCostEncoder = new PasswordEncoder(12);
        
        String lowCostHash = passwordEncoder.encode("password"); // Cost 4
        assertTrue(higherCostEncoder.needsUpgrade(lowCostHash));
        
        String highCostHash = higherCostEncoder.encode("password"); // Cost 12
        assertFalse(higherCostEncoder.needsUpgrade(highCostHash));
        
        assertTrue(higherCostEncoder.needsUpgrade(null));
        assertTrue(higherCostEncoder.needsUpgrade("invalid-hash"));
    }
    
    @Test
    void testDefaultConstructor() {
        PasswordEncoder defaultEncoder = new PasswordEncoder();
        String encoded = defaultEncoder.encode("password");
        
        assertTrue(encoded.startsWith("$2a$12$")); // Default cost 12
    }
    
    @Test
    void testMultipleEncodings() {
        String password = "samePassword";
        String encoded1 = passwordEncoder.encode(password);
        String encoded2 = passwordEncoder.encode(password);
        
        // Different salt should produce different hashes
        assertNotEquals(encoded1, encoded2);
        
        // But both should match the original password
        assertTrue(passwordEncoder.matches(password, encoded1));
        assertTrue(passwordEncoder.matches(password, encoded2));
    }
}