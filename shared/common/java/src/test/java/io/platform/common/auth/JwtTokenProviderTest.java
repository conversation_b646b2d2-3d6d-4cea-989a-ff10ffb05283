package io.platform.common.auth;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

class JwtTokenProviderTest {
    
    private JwtTokenProvider tokenProvider;
    private UserClaims testClaims;
    
    @BeforeEach
    void setUp() {
        tokenProvider = new JwtTokenProvider(
            "test-secret-key-for-jwt-tokens-that-is-long-enough",
            3600, // 1 hour access token
            86400, // 24 hours refresh token
            "test-issuer"
        );
        
        testClaims = UserClaims.builder()
            .userId("user123")
            .username("testuser")
            .roles(Arrays.asList("USER", "ADMIN"))
            .permissions(Arrays.asList("READ", "WRITE"))
            .agentId("agent456")
            .build();
    }
    
    @Test
    void testGenerateToken() {
        TokenPair tokenPair = tokenProvider.generateToken(testClaims);
        
        assertNotNull(tokenPair);
        assertNotNull(tokenPair.getAccessToken());
        assertNotNull(tokenPair.getRefreshToken());
        assertNotNull(tokenPair.getExpiresAt());
        assertEquals("Bearer", tokenPair.getTokenType());
        assertFalse(tokenPair.isExpired());
    }
    
    @Test
    void testValidateToken() {
        TokenPair tokenPair = tokenProvider.generateToken(testClaims);
        UserClaims validatedClaims = tokenProvider.validateToken(tokenPair.getAccessToken());
        
        assertNotNull(validatedClaims);
        assertEquals(testClaims.getUserId(), validatedClaims.getUserId());
        assertEquals(testClaims.getUsername(), validatedClaims.getUsername());
        assertEquals(testClaims.getRoles(), validatedClaims.getRoles());
        assertEquals(testClaims.getPermissions(), validatedClaims.getPermissions());
        assertEquals(testClaims.getAgentId(), validatedClaims.getAgentId());
    }
    
    @Test
    void testRefreshToken() {
        TokenPair originalPair = tokenProvider.generateToken(testClaims);
        TokenPair refreshedPair = tokenProvider.refreshToken(originalPair.getRefreshToken());
        
        assertNotNull(refreshedPair);
        assertNotNull(refreshedPair.getAccessToken());
        assertNotNull(refreshedPair.getRefreshToken());
        assertNotEquals(originalPair.getAccessToken(), refreshedPair.getAccessToken());
    }
    
    @Test
    void testRevokeToken() {
        TokenPair tokenPair = tokenProvider.generateToken(testClaims);
        String token = tokenPair.getAccessToken();
        
        assertFalse(tokenProvider.isTokenRevoked(token));
        
        tokenProvider.revokeToken(token);
        assertTrue(tokenProvider.isTokenRevoked(token));
        
        assertThrows(TokenException.class, () -> {
            tokenProvider.validateToken(token);
        });
    }
    
    @Test
    void testInvalidToken() {
        assertThrows(TokenException.class, () -> {
            tokenProvider.validateToken("invalid.token.here");
        });
    }
    
    @Test
    void testExpiredToken() {
        // Create a provider with very short token validity
        JwtTokenProvider shortLivedProvider = new JwtTokenProvider(
            "test-secret-key-for-jwt-tokens-that-is-long-enough",
            -1, // Already expired
            3600,
            "test-issuer"
        );
        
        TokenPair tokenPair = shortLivedProvider.generateToken(testClaims);
        assertTrue(tokenPair.isExpired());
    }
    
    @Test
    void testGetTokenValidityRemaining() {
        TokenPair tokenPair = tokenProvider.generateToken(testClaims);
        long remaining = tokenProvider.getTokenValidityRemaining(tokenPair.getAccessToken());
        
        assertTrue(remaining > 0);
        assertTrue(remaining <= 3600 * 1000); // Should be less than or equal to 1 hour in milliseconds
    }
}