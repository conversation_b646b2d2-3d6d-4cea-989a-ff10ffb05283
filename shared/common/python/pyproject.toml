[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "platform-common"
version = "1.0.0"
description = "Shared Python libraries for the AI-Native Agent Platform"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "AI Platform Team", email = "<EMAIL>"}
]
keywords = ["ai", "platform", "common", "libraries"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # Core dependencies
    "pydantic>=2.6.0",
    "pydantic-settings>=2.2.0",
    "typing-extensions>=4.10.0",
    
    # Configuration
    "pyyaml>=6.0.1",
    "python-dotenv>=1.0.1",
    
    # Authentication
    "pyjwt[crypto]>=2.8.0",
    "bcrypt>=4.1.2",
    "cryptography>=42.0.5",
    
    # Database
    "sqlalchemy>=2.0.29",
    "psycopg2-binary>=2.9.9",
    "pymongo>=4.6.3",
    "redis>=5.0.3",
    "alembic>=1.13.1",
    
    # Logging
    "structlog>=24.1.0",
    "python-json-logger>=2.0.7",
    
    # Metrics
    "prometheus-client>=0.20.0",
    
    # HTTP/API
    "httpx>=0.27.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.29.0",
    
    # Utilities
    "validators>=0.22.0",
    "python-multipart>=0.0.9",
    "orjson>=3.10.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.1.1",
    "pytest-asyncio>=0.23.6",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "pytest-benchmark>=4.0.0",
    "coverage>=7.4.4",
    
    # Code quality
    "black>=24.3.0",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "mypy>=1.9.0",
    "bandit>=1.7.8",
    "pre-commit>=3.7.0",
    
    # Documentation
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.5.15",
    "mkdocstrings[python]>=0.24.1",
]

test = [
    "pytest>=8.1.1",
    "pytest-asyncio>=0.23.6",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "testcontainers>=4.2.0",
    "httpx>=0.27.0",
]

[project.urls]
Homepage = "https://github.com/ai-platform/platform"
Documentation = "https://docs.ai-platform.io"
Repository = "https://github.com/ai-platform/platform.git"
Issues = "https://github.com/ai-platform/platform/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
platform_common = ["py.typed"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2
fail_under = 80

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]