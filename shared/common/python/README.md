# AI Platform - Python Common Libraries

This package provides shared Python libraries for the AI-Native Agent Platform. It contains common functionality that can be used across all Python-based services in the platform.

## Features

### Configuration (`platform_common.config`)
- **Pydantic-based configuration models** with validation
- **Multi-source loading** (files, environment variables, defaults)
- **Hierarchical configuration** with environment overrides
- **Type-safe configuration** with automatic validation

### Authentication (`platform_common.auth`)
- **JWT token management** with access/refresh token pairs
- **BCrypt password hashing** with configurable rounds
- **Role-based access control** with claims validation
- **Token revocation** with in-memory tracking

### Logging (`platform_common.logging`)
- **Structured logging** with structlog
- **JSON formatting** for production environments
- **Context binding** for request tracing
- **Performance optimized** logging

### Metrics (`platform_common.metrics`)
- **Prometheus metrics** with automatic registration
- **Standard metric types** (Counter, Gauge, Histogram)
- **Custom metrics** with labels and tags
- **HTTP endpoint** for metrics export

### Database (`platform_common.database`)
- **Multi-database support** (PostgreSQL, MongoDB, Redis)
- **Connection pooling** with SQLAlchemy
- **Health monitoring** and connection management
- **Migration support** with Alembic

### Utilities (`platform_common.utils`)
- **Input validation** with Pydantic validators
- **Encoding/decoding** utilities (Base64, JSON, etc.)
- **Encryption** support with cryptography
- **Common patterns** for async operations

## Installation

```bash
pip install platform-common
```

Or for development:
```bash
pip install platform-common[dev]
```

## Quick Start

### Configuration
```python
from platform_common.config import ConfigLoader, PlatformConfig

# Load configuration
loader = ConfigLoader()
config = loader.load_platform_config()

# Access configuration
db_host = config.database.host
log_level = config.logging.level
```

### Authentication
```python
from platform_common.auth import JWTProvider, PasswordEncoder, UserClaims

# Setup JWT provider
jwt_provider = JWTProvider(
    secret="your-secret-key",
    access_token_expires=3600
)

# Generate tokens
claims = UserClaims(
    user_id="user123",
    username="alice",
    roles=["user", "admin"]
)
token_pair = jwt_provider.generate_token(claims)

# Validate tokens
validated_claims = jwt_provider.validate_token(token_pair.access_token)

# Password hashing
encoder = PasswordEncoder()
hashed = encoder.encode("password123")
is_valid = encoder.verify("password123", hashed)
```

### Logging
```python
from platform_common.logging import get_logger, setup_logging, LoggerConfig

# Setup logging
config = LoggerConfig(level="INFO", format="json")
setup_logging(config)

# Get logger
logger = get_logger("my_service", service_version="1.0.0")

# Log with context
logger.info("User login", user_id="123", ip_address="***********")
```

### Metrics
```python
from platform_common.metrics import MetricsRegistry

# Create registry
registry = MetricsRegistry()

# Create metrics
request_counter = registry.counter(
    "http_requests_total",
    "Total HTTP requests",
    labels=["method", "endpoint"]
)

response_time = registry.histogram(
    "http_request_duration_seconds",
    "HTTP request duration"
)

# Use metrics
request_counter.inc(method="GET", endpoint="/api/users")
response_time.observe(0.25)
```

## Configuration

### Environment Variables
All configuration can be overridden with environment variables using the `PLATFORM_` prefix:

```bash
# Database configuration
export PLATFORM_DATABASE__HOST=localhost
export PLATFORM_DATABASE__PORT=5432
export PLATFORM_DATABASE__PASSWORD=secret

# Security configuration
export PLATFORM_SECURITY__JWT_SECRET=your-jwt-secret
export PLATFORM_SECURITY__ENCRYPTION_KEY=your-encryption-key

# Logging configuration
export PLATFORM_LOGGING__LEVEL=DEBUG
export PLATFORM_LOGGING__FORMAT=console
```

### Configuration Files
Create `platform.yaml` or `config.yaml`:

```yaml
database:
  host: localhost
  port: 5432
  database: platform
  username: platform_user
  password: ${DATABASE_PASSWORD}

logging:
  level: INFO
  format: json
  output: stdout

metrics:
  enabled: true
  port: 9090
  path: /metrics

security:
  jwt_secret: ${JWT_SECRET}
  token_duration_hours: 24
  encryption_key: ${ENCRYPTION_KEY}
```

## Development

### Setup
```bash
# Clone repository
git clone https://github.com/ai-platform/platform.git
cd platform/shared/common/python

# Install in development mode
pip install -e .[dev]

# Install pre-commit hooks
pre-commit install
```

### Testing
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=platform_common --cov-report=html

# Run specific tests
pytest tests/test_auth.py -v
```

### Code Quality
```bash
# Format code
black src tests
isort src tests

# Lint code
flake8 src tests
mypy src

# Security scan
bandit -r src
```

## Architecture

### Thread Safety
All components are designed to be thread-safe:
- JWT provider uses locks for token revocation
- Logger configuration is immutable after setup
- Metrics use atomic operations

### Performance
- Lazy initialization where appropriate
- Connection pooling for databases
- Efficient JSON serialization with orjson
- Minimal memory allocation in hot paths

### Error Handling
- Comprehensive exception hierarchy
- Structured error responses
- Graceful degradation for non-critical failures

## Compatibility

- **Python**: 3.11+
- **FastAPI**: 0.110+
- **Pydantic**: 2.6+
- **SQLAlchemy**: 2.0+

## Testing

The library includes comprehensive tests with >80% coverage:

```bash
# Unit tests
pytest tests/unit/

# Integration tests (requires Docker)
pytest tests/integration/

# Performance tests
pytest tests/performance/ --benchmark-only
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

---

This package provides the foundation for building scalable, maintainable Python services in the AI Platform ecosystem.