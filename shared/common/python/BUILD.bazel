load("@rules_python//python:defs.bzl", "py_library", "py_test")

# Main library
py_library(
    name = "platform_common",
    srcs = glob(["src/platform_common/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "@pypi//pydantic",
        "@pypi//pydantic_settings",
        "@pypi//pyyaml",
        "@pypi//python_dotenv",
        "@pypi//pyjwt",
        "@pypi//bcrypt",
        "@pypi//cryptography",
        "@pypi//structlog",
        "@pypi//prometheus_client",
        "@pypi//validators",
        "@pypi//orjson",
    ],
)

# Config package
py_library(
    name = "config",
    srcs = glob(["src/platform_common/config/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "@pypi//pydantic",
        "@pypi//pydantic_settings",
        "@pypi//pyyaml",
    ],
)

# Auth package
py_library(
    name = "auth",
    srcs = glob(["src/platform_common/auth/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        ":config",
        "@pypi//pydantic",
        "@pypi//pyjwt",
        "@pypi//bcrypt",
        "@pypi//cryptography",
    ],
)

# Logging package
py_library(
    name = "logging",
    srcs = glob(["src/platform_common/logging/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "@pypi//structlog",
        "@pypi//python_json_logger",
    ],
)

# Metrics package
py_library(
    name = "metrics",
    srcs = glob(["src/platform_common/metrics/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "@pypi//prometheus_client",
    ],
)

# Utils package
py_library(
    name = "utils",
    srcs = glob(["src/platform_common/utils/**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "@pypi//pydantic",
        "@pypi//validators",
        "@pypi//cryptography",
    ],
)

# Tests
py_test(
    name = "test_auth",
    srcs = ["tests/test_auth.py"],
    deps = [
        ":platform_common",
        "@pypi//pytest",
    ],
)

py_test(
    name = "test_config",
    srcs = ["tests/test_config.py"],
    deps = [
        ":platform_common",
        "@pypi//pytest",
    ],
)

# All tests
py_test(
    name = "tests",
    srcs = glob(["tests/**/*.py"]),
    deps = [
        ":platform_common",
        "@pypi//pytest",
        "@pypi//pytest_asyncio",
        "@pypi//pytest_mock",
    ],
)