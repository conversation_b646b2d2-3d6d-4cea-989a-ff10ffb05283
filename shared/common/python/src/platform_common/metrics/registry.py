"""Metrics registry implementation."""

from prometheus_client import Counter as PrometheusCounter, <PERSON><PERSON><PERSON> as PrometheusGauge, Histogram as PrometheusHistogram
from prometheus_client import CollectorRegistry, generate_latest
from typing import Dict, List, Optional


class Counter:
    """Counter metric wrapper."""
    
    def __init__(self, prometheus_counter: PrometheusCounter):
        self._counter = prometheus_counter
    
    def inc(self, amount: float = 1, **labels) -> None:
        """Increment counter."""
        if labels:
            self._counter.labels(**labels).inc(amount)
        else:
            self._counter.inc(amount)


class Gauge:
    """Gauge metric wrapper."""
    
    def __init__(self, prometheus_gauge: PrometheusGauge):
        self._gauge = prometheus_gauge
    
    def set(self, value: float, **labels) -> None:
        """Set gauge value."""
        if labels:
            self._gauge.labels(**labels).set(value)
        else:
            self._gauge.set(value)
    
    def inc(self, amount: float = 1, **labels) -> None:
        """Increment gauge."""
        if labels:
            self._gauge.labels(**labels).inc(amount)
        else:
            self._gauge.inc(amount)
    
    def dec(self, amount: float = 1, **labels) -> None:
        """Decrement gauge."""
        if labels:
            self._gauge.labels(**labels).dec(amount)
        else:
            self._gauge.dec(amount)


class Histogram:
    """Histogram metric wrapper."""
    
    def __init__(self, prometheus_histogram: PrometheusHistogram):
        self._histogram = prometheus_histogram
    
    def observe(self, value: float, **labels) -> None:
        """Observe value."""
        if labels:
            self._histogram.labels(**labels).observe(value)
        else:
            self._histogram.observe(value)


class MetricsRegistry:
    """Metrics registry."""
    
    def __init__(self, namespace: str = "platform"):
        self.namespace = namespace
        self.registry = CollectorRegistry()
        self._metrics: Dict[str, object] = {}
    
    def counter(
        self, 
        name: str, 
        documentation: str, 
        labels: Optional[List[str]] = None
    ) -> Counter:
        """Create counter metric."""
        full_name = f"{self.namespace}_{name}"
        
        if full_name in self._metrics:
            return self._metrics[full_name]
        
        prometheus_counter = PrometheusCounter(
            full_name,
            documentation,
            labelnames=labels or [],
            registry=self.registry
        )
        
        counter = Counter(prometheus_counter)
        self._metrics[full_name] = counter
        return counter
    
    def gauge(
        self, 
        name: str, 
        documentation: str, 
        labels: Optional[List[str]] = None
    ) -> Gauge:
        """Create gauge metric."""
        full_name = f"{self.namespace}_{name}"
        
        if full_name in self._metrics:
            return self._metrics[full_name]
        
        prometheus_gauge = PrometheusGauge(
            full_name,
            documentation,
            labelnames=labels or [],
            registry=self.registry
        )
        
        gauge = Gauge(prometheus_gauge)
        self._metrics[full_name] = gauge
        return gauge
    
    def histogram(
        self, 
        name: str, 
        documentation: str, 
        labels: Optional[List[str]] = None,
        buckets: Optional[List[float]] = None
    ) -> Histogram:
        """Create histogram metric."""
        full_name = f"{self.namespace}_{name}"
        
        if full_name in self._metrics:
            return self._metrics[full_name]
        
        prometheus_histogram = PrometheusHistogram(
            full_name,
            documentation,
            labelnames=labels or [],
            buckets=buckets,
            registry=self.registry
        )
        
        histogram = Histogram(prometheus_histogram)
        self._metrics[full_name] = histogram
        return histogram
    
    def generate_metrics(self) -> bytes:
        """Generate metrics output."""
        return generate_latest(self.registry)