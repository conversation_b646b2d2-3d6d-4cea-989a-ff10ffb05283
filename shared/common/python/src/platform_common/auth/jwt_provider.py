"""JWT token provider implementation."""

import jwt
import uuid
from datetime import datetime, timedelta, timezone
from typing import Set, Optional
from threading import Lock

from .models import UserClaims, TokenPair
from .exceptions import TokenError


class JWTProvider:
    """JWT token provider for authentication."""
    
    def __init__(
        self,
        secret: str,
        access_token_expires: int = 3600,  # 1 hour
        refresh_token_expires: int = 86400 * 7,  # 7 days
        issuer: str = "platform",
        algorithm: str = "HS256"
    ):
        """Initialize JWT provider.
        
        Args:
            secret: Secret key for signing tokens
            access_token_expires: Access token expiration in seconds
            refresh_token_expires: Refresh token expiration in seconds
            issuer: Token issuer
            algorithm: JWT algorithm
        """
        self.secret = secret
        self.access_token_expires = access_token_expires
        self.refresh_token_expires = refresh_token_expires
        self.issuer = issuer
        self.algorithm = algorithm
        self._revoked_tokens: Set[str] = set()
        self._lock = Lock()
    
    def generate_token(self, claims: UserClaims) -> TokenPair:
        """Generate access and refresh token pair.
        
        Args:
            claims: User claims
            
        Returns:
            Token pair
            
        Raises:
            TokenError: If token generation fails
        """
        try:
            now = datetime.now(timezone.utc)
            access_expires = now + timedelta(seconds=self.access_token_expires)
            refresh_expires = now + timedelta(seconds=self.refresh_token_expires)
            
            # Generate access token
            access_payload = {
                "iss": self.issuer,
                "sub": claims.user_id,
                "username": claims.username,
                "roles": claims.roles,
                "permissions": claims.permissions,
                "agent_id": claims.agent_id,
                "iat": now,
                "exp": access_expires,
                "jti": str(uuid.uuid4()),
                "type": "access"
            }
            
            access_token = jwt.encode(
                access_payload,
                self.secret,
                algorithm=self.algorithm
            )
            
            # Generate refresh token (simpler payload)
            refresh_payload = {
                "iss": self.issuer,
                "sub": claims.user_id,
                "username": claims.username,
                "iat": now,
                "exp": refresh_expires,
                "jti": str(uuid.uuid4()),
                "type": "refresh"
            }
            
            refresh_token = jwt.encode(
                refresh_payload,
                self.secret,
                algorithm=self.algorithm
            )
            
            return TokenPair(
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=access_expires.replace(tzinfo=None),
                token_type="Bearer"
            )
            
        except Exception as e:
            raise TokenError(f"Failed to generate token: {e}")
    
    def validate_token(self, token: str) -> UserClaims:
        """Validate token and return claims.
        
        Args:
            token: JWT token
            
        Returns:
            User claims
            
        Raises:
            TokenError: If token is invalid
        """
        try:
            # Check if token is revoked
            if self.is_token_revoked(token):
                raise TokenError("Token has been revoked")
            
            # Decode and verify token
            payload = jwt.decode(
                token,
                self.secret,
                algorithms=[self.algorithm],
                issuer=self.issuer
            )
            
            # Verify token type
            if payload.get("type") != "access":
                raise TokenError("Invalid token type")
            
            return UserClaims(
                user_id=payload["sub"],
                username=payload.get("username", ""),
                roles=payload.get("roles", []),
                permissions=payload.get("permissions", []),
                agent_id=payload.get("agent_id")
            )
            
        except jwt.ExpiredSignatureError:
            raise TokenError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise TokenError(f"Invalid token: {e}")
        except Exception as e:
            raise TokenError(f"Token validation failed: {e}")
    
    def refresh_token(self, refresh_token: str) -> TokenPair:
        """Refresh access token using refresh token.
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            New token pair
            
        Raises:
            TokenError: If refresh fails
        """
        try:
            # Check if refresh token is revoked
            if self.is_token_revoked(refresh_token):
                raise TokenError("Refresh token has been revoked")
            
            # Decode refresh token
            payload = jwt.decode(
                refresh_token,
                self.secret,
                algorithms=[self.algorithm],
                issuer=self.issuer
            )
            
            # Verify token type
            if payload.get("type") != "refresh":
                raise TokenError("Invalid token type for refresh")
            
            # Create new claims (need to fetch current roles/permissions)
            claims = UserClaims(
                user_id=payload["sub"],
                username=payload.get("username", ""),
                roles=[],  # Should be fetched from user service
                permissions=[]  # Should be fetched from user service
            )
            
            return self.generate_token(claims)
            
        except jwt.ExpiredSignatureError:
            raise TokenError("Refresh token has expired")
        except jwt.InvalidTokenError as e:
            raise TokenError(f"Invalid refresh token: {e}")
        except Exception as e:
            raise TokenError(f"Token refresh failed: {e}")
    
    def revoke_token(self, token: str) -> None:
        """Revoke a token.
        
        Args:
            token: Token to revoke
        """
        with self._lock:
            self._revoked_tokens.add(token)
    
    def is_token_revoked(self, token: str) -> bool:
        """Check if token is revoked.
        
        Args:
            token: Token to check
            
        Returns:
            True if revoked
        """
        with self._lock:
            return token in self._revoked_tokens
    
    def get_token_id(self, token: str) -> Optional[str]:
        """Get token ID (JTI) for tracking.
        
        Args:
            token: JWT token
            
        Returns:
            Token ID or None
        """
        try:
            payload = jwt.decode(
                token,
                options={"verify_signature": False}
            )
            return payload.get("jti")
        except Exception:
            return None
    
    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """Get token expiration time.
        
        Args:
            token: JWT token
            
        Returns:
            Expiration datetime or None
        """
        try:
            payload = jwt.decode(
                token,
                options={"verify_signature": False}
            )
            exp = payload.get("exp")
            if exp:
                return datetime.fromtimestamp(exp, tz=timezone.utc)
        except Exception:
            pass
        return None
    
    def cleanup_expired_tokens(self) -> int:
        """Clean up expired revoked tokens.
        
        Returns:
            Number of tokens cleaned up
        """
        with self._lock:
            expired_tokens = set()
            for token in self._revoked_tokens:
                expiry = self.get_token_expiry(token)
                if expiry and expiry < datetime.now(timezone.utc):
                    expired_tokens.add(token)
            
            self._revoked_tokens -= expired_tokens
            return len(expired_tokens)