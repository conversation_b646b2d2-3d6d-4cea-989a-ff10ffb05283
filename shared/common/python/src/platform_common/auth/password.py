"""Password encoding and verification."""

import bcrypt
from typing import Union


class PasswordEncoder:
    """Password encoder using BCrypt."""
    
    def __init__(self, rounds: int = 12):
        """Initialize password encoder.
        
        Args:
            rounds: BCrypt rounds (4-31, default 12)
        """
        if not 4 <= rounds <= 31:
            raise ValueError("BCrypt rounds must be between 4 and 31")
        self.rounds = rounds
    
    def encode(self, password: str) -> str:
        """Encode a password.
        
        Args:
            password: Raw password
            
        Returns:
            Encoded password hash
            
        Raises:
            ValueError: If password is None or empty
        """
        if not password:
            raise ValueError("Password cannot be None or empty")
        
        # Convert to bytes and hash
        password_bytes = password.encode('utf-8')
        salt = bcrypt.gensalt(rounds=self.rounds)
        hashed = bcrypt.hashpw(password_bytes, salt)
        
        return hashed.decode('utf-8')
    
    def verify(self, password: str, hashed: str) -> bool:
        """Verify a password against a hash.
        
        Args:
            password: Raw password
            hashed: Encoded password hash
            
        Returns:
            True if password matches
        """
        if not password or not hashed:
            return False
        
        try:
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception:
            return False
    
    def needs_upgrade(self, hashed: str) -> bool:
        """Check if password hash needs upgrade.
        
        Args:
            hashed: Encoded password hash
            
        Returns:
            True if hash should be upgraded
        """
        if not hashed:
            return True
        
        try:
            # Extract cost from hash
            parts = hashed.split('$')
            if len(parts) >= 3:
                current_rounds = int(parts[2])
                return current_rounds < self.rounds
        except (ValueError, IndexError):
            pass
        
        return True
    
    def get_hash_info(self, hashed: str) -> dict:
        """Get information about a password hash.
        
        Args:
            hashed: Encoded password hash
            
        Returns:
            Dictionary with hash information
        """
        info = {
            "valid": False,
            "algorithm": None,
            "rounds": None,
            "version": None
        }
        
        if not hashed:
            return info
        
        try:
            parts = hashed.split('$')
            if len(parts) >= 4:
                info["valid"] = True
                info["version"] = parts[1]  # e.g., "2a", "2b"
                info["rounds"] = int(parts[2])
                info["algorithm"] = f"bcrypt-{parts[1]}"
        except (ValueError, IndexError):
            pass
        
        return info