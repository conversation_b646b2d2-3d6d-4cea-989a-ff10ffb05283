"""Authentication models."""

from typing import List, Optional, Any, Dict
from datetime import datetime
from pydantic import BaseModel, Field


class UserClaims(BaseModel):
    """User claims for JWT tokens."""
    
    user_id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    roles: List[str] = Field(default_factory=list, description="User roles")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    agent_id: Optional[str] = Field(default=None, description="Associated agent ID")
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return role in self.roles
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission."""
        return permission in self.permissions
    
    def has_any_role(self, *roles: str) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in roles)
    
    def has_any_permission(self, *permissions: str) -> bool:
        """Check if user has any of the specified permissions."""
        return any(permission in self.permissions for permission in permissions)


class User(BaseModel):
    """User model."""
    
    id: str = Field(..., description="User ID")
    username: str = Field(..., description="Username")
    email: str = Field(..., description="Email address")
    roles: List[str] = Field(default_factory=list, description="User roles")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Update timestamp")
    last_login: Optional[datetime] = Field(default=None, description="Last login timestamp")
    active: bool = Field(default=True, description="User active status")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    def to_claims(self, agent_id: Optional[str] = None) -> UserClaims:
        """Convert user to claims."""
        return UserClaims(
            user_id=self.id,
            username=self.username,
            roles=self.roles,
            permissions=self.permissions,
            agent_id=agent_id
        )


class TokenPair(BaseModel):
    """Token pair containing access and refresh tokens."""
    
    access_token: str = Field(..., description="Access token")
    refresh_token: str = Field(..., description="Refresh token")
    expires_at: datetime = Field(..., description="Token expiration time")
    token_type: str = Field(default="Bearer", description="Token type")
    
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at
    
    def expires_in_seconds(self) -> int:
        """Get seconds until token expires."""
        delta = self.expires_at - datetime.utcnow()
        return max(0, int(delta.total_seconds()))
    
    class Config:
        # Don't include actual tokens in string representation
        def __str__(self) -> str:
            return f"TokenPair(token_type={self.token_type}, expires_at={self.expires_at})"