"""Configuration models using Pydantic."""

from typing import Any, Dict, Optional
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseModel):
    """Database configuration."""
    
    driver: str = Field(default="postgresql", description="Database driver")
    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, ge=1, le=65535, description="Database port")
    database: Optional[str] = Field(default=None, description="Database name")
    username: Optional[str] = Field(default=None, description="Database username")
    password: Optional[str] = Field(default=None, description="Database password")
    options: Dict[str, str] = Field(default_factory=dict, description="Additional options")
    max_pool_size: int = Field(default=10, ge=1, description="Maximum pool size")
    min_pool_size: int = Field(default=2, ge=0, description="Minimum pool size")
    connection_timeout: int = Field(default=30, ge=1, description="Connection timeout in seconds")
    
    @validator('min_pool_size')
    def validate_pool_sizes(cls, v, values):
        if 'max_pool_size' in values and v > values['max_pool_size']:
            raise ValueError('min_pool_size cannot be greater than max_pool_size')
        return v


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = Field(default="INFO", description="Log level")
    format: str = Field(default="json", description="Log format (json, console)")
    output: str = Field(default="stdout", description="Log output (stdout, stderr, file)")
    file_path: Optional[str] = Field(default=None, description="Log file path")
    development: bool = Field(default=False, description="Development mode")
    sampling_enabled: bool = Field(default=False, description="Enable log sampling")
    
    @validator('level')
    def validate_log_level(cls, v):
        valid_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @validator('format')
    def validate_format(cls, v):
        valid_formats = {'json', 'console'}
        if v not in valid_formats:
            raise ValueError(f'Log format must be one of: {valid_formats}')
        return v
    
    @validator('output')
    def validate_output(cls, v):
        valid_outputs = {'stdout', 'stderr', 'file'}
        if v not in valid_outputs:
            raise ValueError(f'Log output must be one of: {valid_outputs}')
        return v


class MetricsConfig(BaseModel):
    """Metrics configuration."""
    
    enabled: bool = Field(default=True, description="Enable metrics")
    port: int = Field(default=9090, ge=1, le=65535, description="Metrics server port")
    path: str = Field(default="/metrics", description="Metrics endpoint path")
    namespace: str = Field(default="platform", description="Metrics namespace")
    subsystem: Optional[str] = Field(default=None, description="Metrics subsystem")
    
    @validator('path')
    def validate_path(cls, v):
        if not v.startswith('/'):
            raise ValueError('Metrics path must start with /')
        return v


class SecurityConfig(BaseModel):
    """Security configuration."""
    
    jwt_secret: str = Field(..., min_length=32, description="JWT secret key")
    token_duration_hours: int = Field(default=24, ge=1, description="Token duration in hours")
    encryption_key: str = Field(..., min_length=16, description="Encryption key")
    tls_cert_path: Optional[str] = Field(default=None, description="TLS certificate path")
    tls_key_path: Optional[str] = Field(default=None, description="TLS private key path")
    bcrypt_rounds: int = Field(default=12, ge=4, le=31, description="BCrypt rounds")
    
    @validator('jwt_secret')
    def validate_jwt_secret(cls, v):
        if len(v) < 32:
            raise ValueError('JWT secret must be at least 32 characters long')
        return v
    
    @validator('encryption_key')
    def validate_encryption_key(cls, v):
        if len(v) < 16:
            raise ValueError('Encryption key must be at least 16 characters long')
        return v


class PlatformConfig(BaseSettings):
    """Main platform configuration."""
    
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    metrics: MetricsConfig = Field(default_factory=MetricsConfig)
    security: SecurityConfig = Field(default_factory=lambda: SecurityConfig(
        jwt_secret="your-jwt-secret-here-must-be-at-least-32-chars",
        encryption_key="your-encryption-key"
    ))
    custom: Dict[str, Any] = Field(default_factory=dict, description="Custom configuration")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
        
        # Allow environment variable overrides
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str) -> Any:
            if field_name.upper() in ['DATABASE__PASSWORD', 'SECURITY__JWT_SECRET', 'SECURITY__ENCRYPTION_KEY']:
                return raw_val
            return cls.json_loads(raw_val)