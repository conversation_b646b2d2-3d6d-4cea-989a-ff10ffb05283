"""Configuration loader with multiple source support."""

import os
import yaml
from pathlib import Path
from typing import Type, TypeVar, Optional, List, Dict, Any
from pydantic import BaseModel
from pydantic_settings import BaseSettings

from .models import PlatformConfig

T = TypeVar('T', bound=BaseModel)


class ConfigLoader:
    """Configuration loader supporting multiple sources with precedence."""
    
    def __init__(self, env_prefix: str = "PLATFORM"):
        self.env_prefix = env_prefix
        self._cache: Dict[str, Any] = {}
    
    def load_config(
        self, 
        config_class: Type[T], 
        config_files: Optional[List[str]] = None,
        use_cache: bool = True
    ) -> T:
        """Load configuration from multiple sources.
        
        Precedence order (highest to lowest):
        1. Environment variables
        2. Configuration files
        3. Default values
        
        Args:
            config_class: Pydantic model class to load
            config_files: List of configuration file paths to try
            use_cache: Whether to use cached configuration
            
        Returns:
            Configuration instance
        """
        cache_key = f"{config_class.__name__}:{config_files}"
        
        if use_cache and cache_key in self._cache:
            return self._cache[cache_key]
        
        # Start with default values
        config_data = {}
        
        # Load from files (in order)
        if config_files:
            for config_file in config_files:
                file_data = self._load_from_file(config_file)
                if file_data:
                    config_data.update(file_data)
        
        # Apply environment variable overrides
        env_data = self._load_from_env(config_class)
        if env_data:
            config_data.update(env_data)
        
        # Create configuration instance
        if issubclass(config_class, BaseSettings):
            # For BaseSettings, let Pydantic handle env vars
            config = config_class(**config_data)
        else:
            config = config_class(**config_data)
        
        if use_cache:
            self._cache[cache_key] = config
            
        return config
    
    def _load_from_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load configuration from a file."""
        path = Path(file_path)
        
        # Try absolute path first
        if path.exists():
            return self._parse_file(path)
        
        # Try relative to current directory
        cwd_path = Path.cwd() / file_path
        if cwd_path.exists():
            return self._parse_file(cwd_path)
        
        # Try in common config directories
        for config_dir in ['/etc/platform', '/usr/local/etc/platform', '~/.config/platform']:
            config_path = Path(config_dir).expanduser() / file_path
            if config_path.exists():
                return self._parse_file(config_path)
        
        return None
    
    def _parse_file(self, path: Path) -> Dict[str, Any]:
        """Parse configuration file based on extension."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                elif path.suffix.lower() == '.json':
                    import json
                    return json.load(f)
                else:
                    # Try YAML as default
                    return yaml.safe_load(f) or {}
        except Exception as e:
            raise ConfigurationError(f"Failed to parse config file {path}: {e}")
    
    def _load_from_env(self, config_class: Type[T]) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        env_data = {}
        
        # Get all environment variables with our prefix
        prefix = f"{self.env_prefix}_"
        for key, value in os.environ.items():
            if key.startswith(prefix):
                # Remove prefix and convert to nested dict
                nested_key = key[len(prefix):].lower()
                self._set_nested_value(env_data, nested_key, value)
        
        return env_data
    
    def _set_nested_value(self, data: Dict[str, Any], key: str, value: str) -> None:
        """Set a nested dictionary value using dot notation."""
        if '__' in key:
            # Handle nested keys (e.g., 'database__host')
            parts = key.split('__')
            current = data
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = self._convert_env_value(value)
        else:
            data[key] = self._convert_env_value(value)
    
    def _convert_env_value(self, value: str) -> Any:
        """Convert environment variable string to appropriate type."""
        # Handle boolean values
        if value.lower() in ('true', '1', 'yes', 'on'):
            return True
        elif value.lower() in ('false', '0', 'no', 'off'):
            return False
        
        # Handle numeric values
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # Handle JSON values
        if value.startswith(('{', '[', '"')):
            try:
                import json
                return json.loads(value)
            except json.JSONDecodeError:
                pass
        
        # Return as string
        return value
    
    def load_platform_config(
        self, 
        config_files: Optional[List[str]] = None
    ) -> PlatformConfig:
        """Load platform configuration with default file locations."""
        default_files = [
            'platform.yaml',
            'platform.yml', 
            'config.yaml',
            'config.yml',
            'application.yaml',
            'application.yml'
        ]
        
        files_to_try = config_files or default_files
        return self.load_config(PlatformConfig, files_to_try)
    
    def clear_cache(self) -> None:
        """Clear configuration cache."""
        self._cache.clear()
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """Get a property value with environment variable support."""
        # Check environment variables first
        env_key = f"{self.env_prefix}_{key.upper().replace('.', '_')}"
        env_value = os.getenv(env_key)
        if env_value is not None:
            return self._convert_env_value(env_value)
        
        return default


class ConfigurationError(Exception):
    """Configuration loading error."""
    pass