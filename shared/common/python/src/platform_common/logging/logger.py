"""Structured logging implementation."""

import logging
import sys
from typing import Any, Dict, Optional
from dataclasses import dataclass, field

import structlog
from pythonjsonlogger import jsonlogger


@dataclass
class LoggerConfig:
    """Logger configuration."""
    level: str = "INFO"
    format: str = "json"  # json, console
    output: str = "stdout"  # stdout, stderr, file
    file_path: Optional[str] = None
    development: bool = False
    include_location: bool = True
    extra_fields: Dict[str, Any] = field(default_factory=dict)


def setup_logging(config: LoggerConfig) -> None:
    """Setup structured logging.
    
    Args:
        config: Logger configuration
    """
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=_get_output_stream(config),
        level=getattr(logging, config.level.upper())
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    
    if config.include_location:
        processors.append(structlog.processors.CallsiteParameterAdder(
            parameters=[structlog.processors.CallsiteParameter.FILENAME,
                       structlog.processors.CallsiteParameter.FUNC_NAME,
                       structlog.processors.CallsiteParameter.LINENO]
        ))
    
    # Add development-friendly processor
    if config.development:
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        if config.format == "json":
            processors.append(structlog.processors.JSONRenderer())
        else:
            processors.append(structlog.dev.ConsoleRenderer())
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure JSON formatter for standard logging if needed
    if config.format == "json" and not config.development:
        formatter = jsonlogger.JsonFormatter(
            '%(levelname)s %(name)s %(asctime)s %(filename)s %(funcName)s %(lineno)d %(message)s'
        )
        
        # Get root logger handler
        root_logger = logging.getLogger()
        if root_logger.handlers:
            for handler in root_logger.handlers:
                handler.setFormatter(formatter)


def _get_output_stream(config: LoggerConfig):
    """Get output stream based on config."""
    if config.output == "stderr":
        return sys.stderr
    elif config.output == "file" and config.file_path:
        return open(config.file_path, 'a', encoding='utf-8')
    else:
        return sys.stdout


def get_logger(name: str, **context) -> structlog.BoundLogger:
    """Get a structured logger.
    
    Args:
        name: Logger name
        **context: Additional context fields
        
    Returns:
        Bound logger with context
    """
    logger = structlog.get_logger(name)
    if context:
        logger = logger.bind(**context)
    return logger


# Context management
def bind_context(**kwargs) -> None:
    """Bind context to current logger."""
    structlog.contextvars.bind_contextvars(**kwargs)


def clear_context() -> None:
    """Clear logger context."""
    structlog.contextvars.clear_contextvars()


def get_context() -> Dict[str, Any]:
    """Get current logger context."""
    return dict(structlog.contextvars.get_contextvars())


# Convenience functions
def log_request(
    method: str,
    path: str,
    status_code: int,
    duration: float,
    **extra
) -> None:
    """Log HTTP request."""
    logger = get_logger("request")
    logger.info(
        "HTTP request",
        method=method,
        path=path,
        status_code=status_code,
        duration_ms=round(duration * 1000, 2),
        **extra
    )


def log_database_query(
    query: str,
    duration: float,
    rows_affected: Optional[int] = None,
    **extra
) -> None:
    """Log database query."""
    logger = get_logger("database")
    logger.debug(
        "Database query",
        query=query[:200] + "..." if len(query) > 200 else query,
        duration_ms=round(duration * 1000, 2),
        rows_affected=rows_affected,
        **extra
    )


def log_task_execution(
    task_id: str,
    task_type: str,
    status: str,
    duration: Optional[float] = None,
    **extra
) -> None:
    """Log task execution."""
    logger = get_logger("task")
    log_data = {
        "task_id": task_id,
        "task_type": task_type,
        "status": status,
        **extra
    }
    
    if duration is not None:
        log_data["duration_ms"] = round(duration * 1000, 2)
    
    if status == "completed":
        logger.info("Task completed", **log_data)
    elif status == "failed":
        logger.error("Task failed", **log_data)
    else:
        logger.info("Task status", **log_data)


def log_agent_lifecycle(
    agent_id: str,
    event: str,
    **extra
) -> None:
    """Log agent lifecycle events."""
    logger = get_logger("agent")
    logger.info(
        f"Agent {event}",
        agent_id=agent_id,
        event=event,
        **extra
    )