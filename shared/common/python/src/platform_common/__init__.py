"""AI Platform Common Libraries

Shared Python libraries for the AI-Native Agent Platform.
"""

__version__ = "1.0.0"
__author__ = "AI Platform Team"

from platform_common.config import ConfigLoader, PlatformConfig
from platform_common.auth import J<PERSON><PERSON><PERSON>ider, PasswordEncoder, UserClaims
from platform_common.logging import get_logger, setup_logging
from platform_common.metrics import MetricsRegistry, Counter, Gauge, Histogram

__all__ = [
    # Config
    "ConfigLoader",
    "PlatformConfig",
    # Auth
    "JWTProvider", 
    "PasswordEncoder",
    "UserClaims",
    # Logging
    "get_logger",
    "setup_logging",
    # Metrics
    "MetricsRegistry",
    "Counter", 
    "Gauge",
    "Histogram",
]