"""Tests for authentication module."""

import pytest
from datetime import datetime, timedelta, timezone

from platform_common.auth import <PERSON><PERSON><PERSON><PERSON><PERSON>, PasswordEncoder, UserClaims, TokenError


class TestJWTProvider:
    """Test JWT provider."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.provider = JWTProvider(
            secret="test-secret-key-that-is-long-enough-for-testing",
            access_token_expires=3600,
            refresh_token_expires=86400
        )
        self.claims = UserClaims(
            user_id="user123",
            username="testuser",
            roles=["USER", "ADMIN"],
            permissions=["READ", "WRITE"],
            agent_id="agent456"
        )
    
    def test_generate_token(self):
        """Test token generation."""
        token_pair = self.provider.generate_token(self.claims)
        
        assert token_pair.access_token
        assert token_pair.refresh_token
        assert token_pair.token_type == "Bearer"
        assert not token_pair.is_expired()
    
    def test_validate_token(self):
        """Test token validation."""
        token_pair = self.provider.generate_token(self.claims)
        validated_claims = self.provider.validate_token(token_pair.access_token)
        
        assert validated_claims.user_id == self.claims.user_id
        assert validated_claims.username == self.claims.username
        assert validated_claims.roles == self.claims.roles
        assert validated_claims.permissions == self.claims.permissions
        assert validated_claims.agent_id == self.claims.agent_id
    
    def test_refresh_token(self):
        """Test token refresh."""
        token_pair = self.provider.generate_token(self.claims)
        new_pair = self.provider.refresh_token(token_pair.refresh_token)
        
        assert new_pair.access_token != token_pair.access_token
        assert new_pair.refresh_token != token_pair.refresh_token
    
    def test_revoke_token(self):
        """Test token revocation."""
        token_pair = self.provider.generate_token(self.claims)
        
        # Token should be valid initially
        self.provider.validate_token(token_pair.access_token)
        
        # Revoke token
        self.provider.revoke_token(token_pair.access_token)
        assert self.provider.is_token_revoked(token_pair.access_token)
        
        # Token should be invalid now
        with pytest.raises(TokenError):
            self.provider.validate_token(token_pair.access_token)
    
    def test_invalid_token(self):
        """Test invalid token handling."""
        with pytest.raises(TokenError):
            self.provider.validate_token("invalid.token.here")


class TestPasswordEncoder:
    """Test password encoder."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.encoder = PasswordEncoder(rounds=4)  # Lower rounds for faster tests
    
    def test_encode_password(self):
        """Test password encoding."""
        password = "testpassword123!"
        encoded = self.encoder.encode(password)
        
        assert encoded != password
        assert encoded.startswith("$2b$04$")
    
    def test_verify_password(self):
        """Test password verification."""
        password = "testpassword123!"
        encoded = self.encoder.encode(password)
        
        assert self.encoder.verify(password, encoded)
        assert not self.encoder.verify("wrongpassword", encoded)
    
    def test_needs_upgrade(self):
        """Test password upgrade detection."""
        # Create encoder with higher rounds
        high_encoder = PasswordEncoder(rounds=12)
        
        # Low rounds hash should need upgrade
        low_hash = self.encoder.encode("password")
        assert high_encoder.needs_upgrade(low_hash)
        
        # High rounds hash should not need upgrade
        high_hash = high_encoder.encode("password")
        assert not high_encoder.needs_upgrade(high_hash)
    
    def test_invalid_password(self):
        """Test invalid password handling."""
        with pytest.raises(ValueError):
            self.encoder.encode("")
        
        with pytest.raises(ValueError):
            self.encoder.encode(None)


class TestUserClaims:
    """Test user claims."""
    
    def test_role_checking(self):
        """Test role checking methods."""
        claims = UserClaims(
            user_id="user123",
            username="testuser",
            roles=["USER", "ADMIN"],
            permissions=["READ", "WRITE"]
        )
        
        assert claims.has_role("USER")
        assert claims.has_role("ADMIN")
        assert not claims.has_role("SUPERUSER")
        
        assert claims.has_any_role("USER", "SUPERUSER")
        assert not claims.has_any_role("SUPERUSER", "GUEST")
    
    def test_permission_checking(self):
        """Test permission checking methods."""
        claims = UserClaims(
            user_id="user123",
            username="testuser",
            roles=["USER"],
            permissions=["READ", "WRITE"]
        )
        
        assert claims.has_permission("READ")
        assert claims.has_permission("WRITE")
        assert not claims.has_permission("DELETE")
        
        assert claims.has_any_permission("READ", "DELETE")
        assert not claims.has_any_permission("DELETE", "ADMIN")