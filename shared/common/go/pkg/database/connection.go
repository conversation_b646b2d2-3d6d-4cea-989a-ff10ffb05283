package database

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type PostgreSQLConnection struct {
	config *ConnectionConfig
	db     *sql.DB
	mu     sync.RWMutex
}

func NewPostgreSQLConnection(config *ConnectionConfig) *PostgreSQLConnection {
	return &PostgreSQLConnection{config: config}
}

func (p *PostgreSQLConnection) Connect(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		p.config.Host, p.config.Port, p.config.Username, p.config.Password, p.config.Database)

	if sslMode, ok := p.config.Options["sslmode"]; ok {
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			p.config.Host, p.config.Port, p.config.Username, p.config.Password, p.config.Database, sslMode)
	}

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return fmt.Errorf("failed to open PostgreSQL connection: %w", err)
	}

	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	p.db = db
	return nil
}

func (p *PostgreSQLConnection) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.db != nil {
		return p.db.Close()
	}
	return nil
}

func (p *PostgreSQLConnection) Ping(ctx context.Context) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.db == nil {
		return fmt.Errorf("connection not established")
	}
	return p.db.PingContext(ctx)
}

func (p *PostgreSQLConnection) IsConnected() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.db != nil
}

func (p *PostgreSQLConnection) GetDB() *sql.DB {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.db
}

func (p *PostgreSQLConnection) BeginTx(ctx context.Context) (*sql.Tx, error) {
	return p.db.BeginTx(ctx, nil)
}

func (p *PostgreSQLConnection) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return p.db.QueryContext(ctx, query, args...)
}

func (p *PostgreSQLConnection) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return p.db.ExecContext(ctx, query, args...)
}

type MySQLConnection struct {
	config *ConnectionConfig
	db     *sql.DB
	mu     sync.RWMutex
}

func NewMySQLConnection(config *ConnectionConfig) *MySQLConnection {
	return &MySQLConnection{config: config}
}

func (m *MySQLConnection) Connect(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		m.config.Username, m.config.Password, m.config.Host, m.config.Port, m.config.Database)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	if err := db.PingContext(ctx); err != nil {
		return fmt.Errorf("failed to ping MySQL: %w", err)
	}

	m.db = db
	return nil
}

func (m *MySQLConnection) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.db != nil {
		return m.db.Close()
	}
	return nil
}

func (m *MySQLConnection) Ping(ctx context.Context) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.db == nil {
		return fmt.Errorf("connection not established")
	}
	return m.db.PingContext(ctx)
}

func (m *MySQLConnection) IsConnected() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.db != nil
}

func (m *MySQLConnection) GetDB() *sql.DB {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.db
}

func (m *MySQLConnection) BeginTx(ctx context.Context) (*sql.Tx, error) {
	return m.db.BeginTx(ctx, nil)
}

func (m *MySQLConnection) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return m.db.QueryContext(ctx, query, args...)
}

func (m *MySQLConnection) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return m.db.ExecContext(ctx, query, args...)
}

type MongoDBConnection struct {
	config *ConnectionConfig
	client *mongo.Client
	mu     sync.RWMutex
}

func NewMongoDBConnection(config *ConnectionConfig) *MongoDBConnection {
	return &MongoDBConnection{config: config}
}

func (m *MongoDBConnection) Connect(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	uri := fmt.Sprintf("mongodb://%s:%s@%s:%d/%s",
		m.config.Username, m.config.Password, m.config.Host, m.config.Port, m.config.Database)

	clientOptions := options.Client().ApplyURI(uri)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	if err := client.Ping(ctx, nil); err != nil {
		return fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	m.client = client
	return nil
}

func (m *MongoDBConnection) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.client != nil {
		return m.client.Disconnect(context.Background())
	}
	return nil
}

func (m *MongoDBConnection) Ping(ctx context.Context) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.client == nil {
		return fmt.Errorf("connection not established")
	}
	return m.client.Ping(ctx, nil)
}

func (m *MongoDBConnection) IsConnected() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.client != nil
}

func (m *MongoDBConnection) GetClient() *mongo.Client {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.client
}

func (m *MongoDBConnection) GetDatabase(name string) *mongo.Database {
	return m.client.Database(name)
}

func (m *MongoDBConnection) GetCollection(db, collection string) *mongo.Collection {
	return m.client.Database(db).Collection(collection)
}

type RedisConnectionImpl struct {
	config *ConnectionConfig
	client *redis.Client
	mu     sync.RWMutex
}

func NewRedisConnection(config *ConnectionConfig) *RedisConnectionImpl {
	return &RedisConnectionImpl{config: config}
}

func (r *RedisConnectionImpl) Connect(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	addr := fmt.Sprintf("%s:%d", r.config.Host, r.config.Port)
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: r.config.Password,
		DB:       0,
	})

	if err := client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	r.client = client
	return nil
}

func (r *RedisConnectionImpl) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

func (r *RedisConnectionImpl) Ping(ctx context.Context) error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.client == nil {
		return fmt.Errorf("connection not established")
	}
	return r.client.Ping(ctx).Err()
}

func (r *RedisConnectionImpl) IsConnected() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.client != nil
}

func (r *RedisConnectionImpl) GetClient() *redis.Client {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.client
}

func (r *RedisConnectionImpl) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

func (r *RedisConnectionImpl) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

func (r *RedisConnectionImpl) Del(ctx context.Context, keys ...string) error {
	return r.client.Del(ctx, keys...).Err()
}

func (r *RedisConnectionImpl) Exists(ctx context.Context, keys ...string) (int64, error) {
	return r.client.Exists(ctx, keys...).Result()
}

type ConnectionPoolImpl struct {
	connections map[string]Connection
	mu          sync.RWMutex
}

func NewConnectionPool() *ConnectionPoolImpl {
	return &ConnectionPoolImpl{
		connections: make(map[string]Connection),
	}
}

func (cp *ConnectionPoolImpl) GetConnection(name string) (Connection, error) {
	cp.mu.RLock()
	defer cp.mu.RUnlock()

	conn, exists := cp.connections[name]
	if !exists {
		return nil, fmt.Errorf("connection '%s' not found", name)
	}
	return conn, nil
}

func (cp *ConnectionPoolImpl) AddConnection(name string, conn Connection) error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	cp.connections[name] = conn
	return nil
}

func (cp *ConnectionPoolImpl) RemoveConnection(name string) error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	if conn, exists := cp.connections[name]; exists {
		conn.Close()
		delete(cp.connections, name)
	}
	return nil
}

func (cp *ConnectionPoolImpl) CloseAll() error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	for name, conn := range cp.connections {
		if err := conn.Close(); err != nil {
			return fmt.Errorf("failed to close connection '%s': %w", name, err)
		}
	}
	cp.connections = make(map[string]Connection)
	return nil
}

func (cp *ConnectionPoolImpl) HealthCheck(ctx context.Context) map[string]error {
	cp.mu.RLock()
	defer cp.mu.RUnlock()

	results := make(map[string]error)
	for name, conn := range cp.connections {
		results[name] = conn.Ping(ctx)
	}
	return results
}