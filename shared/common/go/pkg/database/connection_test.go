package database

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewConnectionPool(t *testing.T) {
	pool := NewConnectionPool()
	assert.NotNil(t, pool)
	assert.Empty(t, pool.connections)
}

func TestConnectionPool_AddAndGetConnection(t *testing.T) {
	pool := NewConnectionPool()
	
	config := &ConnectionConfig{
		Driver:   "postgres",
		Host:     "localhost",
		Port:     5432,
		Database: "test",
		Username: "user",
		Password: "pass",
	}
	
	conn := NewPostgreSQLConnection(config)
	
	err := pool.AddConnection("test-conn", conn)
	require.NoError(t, err)
	
	retrievedConn, err := pool.GetConnection("test-conn")
	require.NoError(t, err)
	assert.Equal(t, conn, retrievedConn)
}

func TestConnectionPool_GetNonExistentConnection(t *testing.T) {
	pool := NewConnectionPool()
	
	_, err := pool.GetConnection("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestConnectionPool_RemoveConnection(t *testing.T) {
	pool := NewConnectionPool()
	
	config := &ConnectionConfig{
		Driver: "postgres",
		Host:   "localhost",
		Port:   5432,
	}
	
	conn := NewPostgreSQLConnection(config)
	pool.AddConnection("test-conn", conn)
	
	err := pool.RemoveConnection("test-conn")
	require.NoError(t, err)
	
	_, err = pool.GetConnection("test-conn")
	assert.Error(t, err)
}

func TestConnectionPool_HealthCheck(t *testing.T) {
	pool := NewConnectionPool()
	
	config := &ConnectionConfig{
		Driver: "postgres",
		Host:   "localhost",
		Port:   5432,
	}
	
	conn := NewPostgreSQLConnection(config)
	pool.AddConnection("test-conn", conn)
	
	ctx := context.Background()
	results := pool.HealthCheck(ctx)
	
	assert.Len(t, results, 1)
	assert.Contains(t, results, "test-conn")
	// The connection will fail since we're not actually connecting to a real DB
	assert.Error(t, results["test-conn"])
}

func TestPostgreSQLConnection_Creation(t *testing.T) {
	config := &ConnectionConfig{
		Driver:   "postgres",
		Host:     "localhost",
		Port:     5432,
		Database: "test",
		Username: "user",
		Password: "pass",
	}
	
	conn := NewPostgreSQLConnection(config)
	assert.NotNil(t, conn)
	assert.Equal(t, config, conn.config)
	assert.False(t, conn.IsConnected())
}

func TestMySQLConnection_Creation(t *testing.T) {
	config := &ConnectionConfig{
		Driver:   "mysql",
		Host:     "localhost",
		Port:     3306,
		Database: "test",
		Username: "user",
		Password: "pass",
	}
	
	conn := NewMySQLConnection(config)
	assert.NotNil(t, conn)
	assert.Equal(t, config, conn.config)
	assert.False(t, conn.IsConnected())
}

func TestMongoDBConnection_Creation(t *testing.T) {
	config := &ConnectionConfig{
		Driver:   "mongodb",
		Host:     "localhost",
		Port:     27017,
		Database: "test",
		Username: "user",
		Password: "pass",
	}
	
	conn := NewMongoDBConnection(config)
	assert.NotNil(t, conn)
	assert.Equal(t, config, conn.config)
	assert.False(t, conn.IsConnected())
}

func TestRedisConnection_Creation(t *testing.T) {
	config := &ConnectionConfig{
		Driver:   "redis",
		Host:     "localhost",
		Port:     6379,
		Password: "pass",
	}
	
	conn := NewRedisConnection(config)
	assert.NotNil(t, conn)
	assert.Equal(t, config, conn.config)
	assert.False(t, conn.IsConnected())
}

func TestConnectionPool_CloseAll(t *testing.T) {
	pool := NewConnectionPool()
	
	config1 := &ConnectionConfig{Driver: "postgres", Host: "localhost", Port: 5432}
	config2 := &ConnectionConfig{Driver: "mysql", Host: "localhost", Port: 3306}
	
	conn1 := NewPostgreSQLConnection(config1)
	conn2 := NewMySQLConnection(config2)
	
	pool.AddConnection("postgres", conn1)
	pool.AddConnection("mysql", conn2)
	
	err := pool.CloseAll()
	require.NoError(t, err)
	
	// After closing all, connections should be removed
	_, err = pool.GetConnection("postgres")
	assert.Error(t, err)
	
	_, err = pool.GetConnection("mysql")
	assert.Error(t, err)
}

// Integration tests would require actual database instances
// These are unit tests that test the structure and basic functionality