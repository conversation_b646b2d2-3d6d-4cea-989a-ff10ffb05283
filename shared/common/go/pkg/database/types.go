package database

import (
	"context"
	"database/sql"
	"time"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
)

type DatabaseType string

const (
	PostgreSQL DatabaseType = "postgres"
	MySQL      DatabaseType = "mysql"
	MongoDB    DatabaseType = "mongodb"
	Redis      DatabaseType = "redis"
)

type ConnectionConfig struct {
	Driver   string `yaml:"driver"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Options  map[string]string `yaml:"options"`
}

type Connection interface {
	Connect(ctx context.Context) error
	Close() error
	Ping(ctx context.Context) error
	IsConnected() bool
}

type SQLConnection interface {
	Connection
	GetDB() *sql.DB
	BeginTx(ctx context.Context) (*sql.Tx, error)
	Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
}

type MongoConnection interface {
	Connection
	GetClient() *mongo.Client
	GetDatabase(name string) *mongo.Database
	GetCollection(db, collection string) *mongo.Collection
}

type RedisConnection interface {
	Connection
	GetClient() *redis.Client
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Del(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, keys ...string) (int64, error)
}

type ConnectionPool interface {
	GetConnection(name string) (Connection, error)
	AddConnection(name string, conn Connection) error
	RemoveConnection(name string) error
	CloseAll() error
	HealthCheck(ctx context.Context) map[string]error
}

type QueryBuilder interface {
	Select(columns ...string) QueryBuilder
	From(table string) QueryBuilder
	Where(condition string, args ...interface{}) QueryBuilder
	Join(table, condition string) QueryBuilder
	OrderBy(column, direction string) QueryBuilder
	Limit(limit int) QueryBuilder
	Offset(offset int) QueryBuilder
	Build() (string, []interface{})
}

type Repository interface {
	Create(ctx context.Context, entity interface{}) error
	GetByID(ctx context.Context, id string, entity interface{}) error
	Update(ctx context.Context, entity interface{}) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filter map[string]interface{}, entities interface{}) error
	Count(ctx context.Context, filter map[string]interface{}) (int64, error)
}

type TransactionManager interface {
	WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error
}