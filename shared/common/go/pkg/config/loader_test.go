package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewLoader(t *testing.T) {
	loader, err := NewLoader()
	require.NoError(t, err)
	assert.NotNil(t, loader)
}

func TestLoadConfig(t *testing.T) {
	loader, err := NewLoader()
	require.NoError(t, err)

	cfg, err := loader.LoadConfig()
	require.NoError(t, err)
	assert.NotNil(t, cfg)

	// Test defaults
	assert.Equal(t, "postgres", cfg.Database.Driver)
	assert.Equal(t, "localhost", cfg.Database.Host)
	assert.Equal(t, 5432, cfg.Database.Port)
	assert.Equal(t, "info", cfg.Logging.Level)
	assert.Equal(t, "json", cfg.Logging.Format)
	assert.True(t, cfg.Metrics.Enabled)
	assert.Equal(t, 9090, cfg.Metrics.Port)
}

func TestLoadConfigWithEnv(t *testing.T) {
	// Set environment variables
	os.Setenv("DB_HOST", "testhost")
	os.Setenv("DB_PORT", "3306")
	os.Setenv("LOG_LEVEL", "debug")
	os.Setenv("JWT_SECRET", "testsecret")
	defer func() {
		os.Unsetenv("DB_HOST")
		os.Unsetenv("DB_PORT")
		os.Unsetenv("LOG_LEVEL")
		os.Unsetenv("JWT_SECRET")
	}()

	loader, err := NewLoader()
	require.NoError(t, err)

	cfg, err := loader.LoadConfig()
	require.NoError(t, err)

	assert.Equal(t, "testhost", cfg.Database.Host)
	assert.Equal(t, 3306, cfg.Database.Port)
	assert.Equal(t, "debug", cfg.Logging.Level)
	assert.Equal(t, "testsecret", cfg.Security.JWTSecret)
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: Config{
				Security: SecurityConfig{
					JWTSecret:     "secret",
					EncryptionKey: "key",
				},
				Database: DatabaseConfig{
					Host: "localhost",
				},
			},
			wantErr: false,
		},
		{
			name: "missing JWT secret",
			config: Config{
				Security: SecurityConfig{
					EncryptionKey: "key",
				},
				Database: DatabaseConfig{
					Host: "localhost",
				},
			},
			wantErr: true,
		},
		{
			name: "missing encryption key",
			config: Config{
				Security: SecurityConfig{
					JWTSecret: "secret",
				},
				Database: DatabaseConfig{
					Host: "localhost",
				},
			},
			wantErr: true,
		},
		{
			name: "missing database host",
			config: Config{
				Security: SecurityConfig{
					JWTSecret:     "secret",
					EncryptionKey: "key",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}