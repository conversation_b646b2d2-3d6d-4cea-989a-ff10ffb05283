package config

import (
	"fmt"
	"os"
	"strings"

	"go.uber.org/config"
	"gopkg.in/yaml.v3"
)

type Loader struct {
	provider config.Provider
}

type DatabaseConfig struct {
	Driver   string `yaml:"driver"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Options  map[string]string `yaml:"options"`
}

type LoggingConfig struct {
	Level       string `yaml:"level"`
	Format      string `yaml:"format"`
	Output      string `yaml:"output"`
	Development bool   `yaml:"development"`
}

type MetricsConfig struct {
	Enabled bool   `yaml:"enabled"`
	Port    int    `yaml:"port"`
	Path    string `yaml:"path"`
}

type SecurityConfig struct {
	JWTSecret      string `yaml:"jwt_secret"`
	TokenDuration  int    `yaml:"token_duration_hours"`
	EncryptionKey  string `yaml:"encryption_key"`
	TLSCertPath    string `yaml:"tls_cert_path"`
	TLSKeyPath     string `yaml:"tls_key_path"`
}

type Config struct {
	Database DatabaseConfig  `yaml:"database"`
	Logging  LoggingConfig   `yaml:"logging"`
	Metrics  MetricsConfig   `yaml:"metrics"`
	Security SecurityConfig  `yaml:"security"`
	Custom   map[string]interface{} `yaml:"custom"`
}

func NewLoader(configPaths ...string) (*Loader, error) {
	var providers []config.Provider

	// Environment variables provider
	providers = append(providers, config.NewYAMLProviderFromReader(strings.NewReader("")))

	// File providers
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			provider, err := config.NewYAMLProviderFromFiles(path)
			if err != nil {
				return nil, fmt.Errorf("failed to load config from %s: %w", path, err)
			}
			providers = append(providers, provider)
		}
	}

	// Environment variable overrides
	envProvider := config.NewProviderGroup("env", config.NewEnvProvider())
	providers = append(providers, envProvider)

	provider, err := config.NewProviderGroup("root", providers...)
	if err != nil {
		return nil, fmt.Errorf("failed to create config provider: %w", err)
	}

	return &Loader{provider: provider}, nil
}

func (l *Loader) Load(target interface{}) error {
	return l.provider.Get("").Populate(target)
}

func (l *Loader) LoadConfig() (*Config, error) {
	cfg := &Config{
		Database: DatabaseConfig{
			Driver: "postgres",
			Host:   "localhost",
			Port:   5432,
		},
		Logging: LoggingConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		},
		Metrics: MetricsConfig{
			Enabled: true,
			Port:    9090,
			Path:    "/metrics",
		},
		Security: SecurityConfig{
			TokenDuration: 24,
		},
		Custom: make(map[string]interface{}),
	}

	if err := l.Load(cfg); err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	// Override with environment variables
	if err := l.loadFromEnv(cfg); err != nil {
		return nil, fmt.Errorf("failed to load environment variables: %w", err)
	}

	return cfg, nil
}

func (l *Loader) loadFromEnv(cfg *Config) error {
	// Database
	if val := os.Getenv("DB_DRIVER"); val != "" {
		cfg.Database.Driver = val
	}
	if val := os.Getenv("DB_HOST"); val != "" {
		cfg.Database.Host = val
	}
	if val := os.Getenv("DB_PORT"); val != "" {
		var port int
		if _, err := fmt.Sscanf(val, "%d", &port); err == nil {
			cfg.Database.Port = port
		}
	}
	if val := os.Getenv("DB_DATABASE"); val != "" {
		cfg.Database.Database = val
	}
	if val := os.Getenv("DB_USERNAME"); val != "" {
		cfg.Database.Username = val
	}
	if val := os.Getenv("DB_PASSWORD"); val != "" {
		cfg.Database.Password = val
	}

	// Logging
	if val := os.Getenv("LOG_LEVEL"); val != "" {
		cfg.Logging.Level = val
	}
	if val := os.Getenv("LOG_FORMAT"); val != "" {
		cfg.Logging.Format = val
	}

	// Security
	if val := os.Getenv("JWT_SECRET"); val != "" {
		cfg.Security.JWTSecret = val
	}
	if val := os.Getenv("ENCRYPTION_KEY"); val != "" {
		cfg.Security.EncryptionKey = val
	}

	return nil
}

func (cfg *Config) Validate() error {
	if cfg.Security.JWTSecret == "" {
		return fmt.Errorf("JWT secret is required")
	}
	if cfg.Security.EncryptionKey == "" {
		return fmt.Errorf("encryption key is required")
	}
	if cfg.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	return nil
}