package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

// Base64 encoding/decoding
func EncodeBase64(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

func DecodeBase64(encoded string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(encoded)
}

func EncodeBase64URL(data []byte) string {
	return base64.URLEncoding.EncodeToString(data)
}

func DecodeBase64URL(encoded string) ([]byte, error) {
	return base64.URLEncoding.DecodeString(encoded)
}

// Hex encoding/decoding
func EncodeHex(data []byte) string {
	return hex.EncodeToString(data)
}

func DecodeHex(encoded string) ([]byte, error) {
	return hex.DecodeString(encoded)
}

// JSON encoding/decoding
func EncodeJSON(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

func DecodeJSON(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

func EncodeJSONPretty(v interface{}) ([]byte, error) {
	return json.MarshalIndent(v, "", "  ")
}

// Hash functions
func HashSHA256(data []byte) []byte {
	hash := sha256.Sum256(data)
	return hash[:]
}

func HashSHA256String(data string) string {
	hash := HashSHA256([]byte(data))
	return EncodeHex(hash)
}

// Password hashing with bcrypt
func HashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(hash), nil
}

func ComparePassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// AES encryption/decryption
type AESEncryptor struct {
	key []byte
}

func NewAESEncryptor(key []byte) (*AESEncryptor, error) {
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return nil, fmt.Errorf("invalid key size: must be 16, 24, or 32 bytes")
	}
	return &AESEncryptor{key: key}, nil
}

func NewAESEncryptorFromString(key string) (*AESEncryptor, error) {
	keyBytes := []byte(key)
	if len(keyBytes) < 32 {
		// Pad the key to 32 bytes
		padded := make([]byte, 32)
		copy(padded, keyBytes)
		keyBytes = padded
	} else if len(keyBytes) > 32 {
		// Truncate to 32 bytes
		keyBytes = keyBytes[:32]
	}
	return NewAESEncryptor(keyBytes)
}

func (e *AESEncryptor) Encrypt(plaintext []byte) ([]byte, error) {
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Use Galois Counter Mode (GCM)
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Generate a random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

func (e *AESEncryptor) Decrypt(ciphertext []byte) ([]byte, error) {
	block, err := aes.NewCipher(e.key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}

	return plaintext, nil
}

func (e *AESEncryptor) EncryptString(plaintext string) (string, error) {
	encrypted, err := e.Encrypt([]byte(plaintext))
	if err != nil {
		return "", err
	}
	return EncodeBase64(encrypted), nil
}

func (e *AESEncryptor) DecryptString(encrypted string) (string, error) {
	ciphertext, err := DecodeBase64(encrypted)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	plaintext, err := e.Decrypt(ciphertext)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// Generate secure random key
func GenerateAESKey(size int) ([]byte, error) {
	if size != 16 && size != 24 && size != 32 {
		return nil, fmt.Errorf("invalid key size: must be 16, 24, or 32 bytes")
	}

	key := make([]byte, size)
	if _, err := rand.Read(key); err != nil {
		return nil, fmt.Errorf("failed to generate random key: %w", err)
	}

	return key, nil
}

func GenerateAESKeyString(size int) (string, error) {
	key, err := GenerateAESKey(size)
	if err != nil {
		return "", err
	}
	return EncodeHex(key), nil
}

// Safe string conversion functions
func ToString(v interface{}) string {
	switch val := v.(type) {
	case string:
		return val
	case []byte:
		return string(val)
	case int:
		return fmt.Sprintf("%d", val)
	case int64:
		return fmt.Sprintf("%d", val)
	case float64:
		return fmt.Sprintf("%f", val)
	case bool:
		return fmt.Sprintf("%t", val)
	default:
		return fmt.Sprintf("%v", val)
	}
}

func ToBytes(v interface{}) []byte {
	switch val := v.(type) {
	case []byte:
		return val
	case string:
		return []byte(val)
	default:
		return []byte(ToString(v))
	}
}

// URL-safe encoding for tokens and IDs
func GenerateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	return EncodeBase64URL(bytes), nil
}

// Mask sensitive data for logging
func MaskString(s string, visibleChars int) string {
	if len(s) <= visibleChars {
		return strings.Repeat("*", len(s))
	}
	
	visible := s[:visibleChars]
	masked := strings.Repeat("*", len(s)-visibleChars)
	return visible + masked
}

func MaskEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return MaskString(email, 2)
	}
	
	username := parts[0]
	domain := parts[1]
	
	if len(username) <= 2 {
		return MaskString(username, 1) + "@" + domain
	}
	
	return MaskString(username, 2) + "@" + domain
}

// Data integrity verification
func ComputeChecksum(data []byte) string {
	hash := HashSHA256(data)
	return EncodeHex(hash)
}

func VerifyChecksum(data []byte, checksum string) bool {
	computed := ComputeChecksum(data)
	return computed == checksum
}