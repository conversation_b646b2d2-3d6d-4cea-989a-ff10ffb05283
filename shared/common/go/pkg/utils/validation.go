package utils

import (
	"fmt"
	"net/mail"
	"regexp"
	"strings"
	"unicode"
)

var (
	// Common regex patterns
	alphanumericRegex = regexp.MustCompile(`^[a-zA-Z0-9]+$`)
	uuidRegex         = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$`)
	slugRegex         = regexp.MustCompile(`^[a-z0-9]+(?:-[a-z0-9]+)*$`)
	semverRegex       = regexp.MustCompile(`^v?(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$`)
)

// Validation errors
type ValidationError struct {
	Field   string
	Message string
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return "no validation errors"
	}
	
	var messages []string
	for _, err := range e {
		messages = append(messages, err.Error())
	}
	return strings.Join(messages, "; ")
}

func (e ValidationErrors) HasErrors() bool {
	return len(e) > 0
}

// String validation functions
func IsEmpty(value string) bool {
	return strings.TrimSpace(value) == ""
}

func IsNotEmpty(value string) bool {
	return !IsEmpty(value)
}

func HasMinLength(value string, min int) bool {
	return len(strings.TrimSpace(value)) >= min
}

func HasMaxLength(value string, max int) bool {
	return len(strings.TrimSpace(value)) <= max
}

func HasExactLength(value string, length int) bool {
	return len(strings.TrimSpace(value)) == length
}

func IsAlphanumeric(value string) bool {
	return alphanumericRegex.MatchString(value)
}

func IsValidEmail(email string) bool {
	_, err := mail.ParseAddress(email)
	return err == nil
}

func IsValidUUID(uuid string) bool {
	return uuidRegex.MatchString(strings.ToLower(uuid))
}

func IsValidSlug(slug string) bool {
	return slugRegex.MatchString(strings.ToLower(slug))
}

func IsValidSemVer(version string) bool {
	return semverRegex.MatchString(version)
}

func ContainsOnlyLetters(value string) bool {
	for _, r := range value {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return true
}

func ContainsOnlyNumbers(value string) bool {
	for _, r := range value {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

func HasUppercase(value string) bool {
	for _, r := range value {
		if unicode.IsUpper(r) {
			return true
		}
	}
	return false
}

func HasLowercase(value string) bool {
	for _, r := range value {
		if unicode.IsLower(r) {
			return true
		}
	}
	return false
}

func HasDigit(value string) bool {
	for _, r := range value {
		if unicode.IsDigit(r) {
			return true
		}
	}
	return false
}

func HasSpecialChar(value string) bool {
	for _, r := range value {
		if unicode.IsPunct(r) || unicode.IsSymbol(r) {
			return true
		}
	}
	return false
}

// Validator interface
type Validator interface {
	Validate() ValidationErrors
}

// Field validator
type FieldValidator struct {
	field  string
	value  interface{}
	errors ValidationErrors
}

func NewFieldValidator(field string, value interface{}) *FieldValidator {
	return &FieldValidator{
		field: field,
		value: value,
	}
}

func (v *FieldValidator) Required() *FieldValidator {
	if str, ok := v.value.(string); ok && IsEmpty(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "is required",
		})
	} else if v.value == nil {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "is required",
		})
	}
	return v
}

func (v *FieldValidator) MinLength(min int) *FieldValidator {
	if str, ok := v.value.(string); ok && !HasMinLength(str, min) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: fmt.Sprintf("must be at least %d characters long", min),
		})
	}
	return v
}

func (v *FieldValidator) MaxLength(max int) *FieldValidator {
	if str, ok := v.value.(string); ok && !HasMaxLength(str, max) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: fmt.Sprintf("must be at most %d characters long", max),
		})
	}
	return v
}

func (v *FieldValidator) Email() *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) && !IsValidEmail(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "must be a valid email address",
		})
	}
	return v
}

func (v *FieldValidator) UUID() *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) && !IsValidUUID(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "must be a valid UUID",
		})
	}
	return v
}

func (v *FieldValidator) Slug() *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) && !IsValidSlug(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "must be a valid slug (lowercase alphanumeric with hyphens)",
		})
	}
	return v
}

func (v *FieldValidator) SemVer() *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) && !IsValidSemVer(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "must be a valid semantic version (e.g., 1.0.0)",
		})
	}
	return v
}

func (v *FieldValidator) Alphanumeric() *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) && !IsAlphanumeric(str) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: "must contain only alphanumeric characters",
		})
	}
	return v
}

func (v *FieldValidator) OneOf(validValues ...string) *FieldValidator {
	if str, ok := v.value.(string); ok && IsNotEmpty(str) {
		valid := false
		for _, validValue := range validValues {
			if str == validValue {
				valid = true
				break
			}
		}
		if !valid {
			v.errors = append(v.errors, ValidationError{
				Field:   v.field,
				Message: fmt.Sprintf("must be one of: %s", strings.Join(validValues, ", ")),
			})
		}
	}
	return v
}

func (v *FieldValidator) Custom(validatorFunc func(interface{}) bool, message string) *FieldValidator {
	if !validatorFunc(v.value) {
		v.errors = append(v.errors, ValidationError{
			Field:   v.field,
			Message: message,
		})
	}
	return v
}

func (v *FieldValidator) Errors() ValidationErrors {
	return v.errors
}

// Password validation
func ValidatePassword(password string) ValidationErrors {
	var errors ValidationErrors
	
	if len(password) < 8 {
		errors = append(errors, ValidationError{
			Field:   "password",
			Message: "must be at least 8 characters long",
		})
	}
	
	if !HasUppercase(password) {
		errors = append(errors, ValidationError{
			Field:   "password",
			Message: "must contain at least one uppercase letter",
		})
	}
	
	if !HasLowercase(password) {
		errors = append(errors, ValidationError{
			Field:   "password",
			Message: "must contain at least one lowercase letter",
		})
	}
	
	if !HasDigit(password) {
		errors = append(errors, ValidationError{
			Field:   "password",
			Message: "must contain at least one digit",
		})
	}
	
	if !HasSpecialChar(password) {
		errors = append(errors, ValidationError{
			Field:   "password",
			Message: "must contain at least one special character",
		})
	}
	
	return errors
}

// Agent ID validation
func ValidateAgentID(agentID string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("agent_id", agentID)
	errors = append(errors, validator.Required().MinLength(3).MaxLength(50).Slug().Errors()...)
	
	return errors
}

// Workflow ID validation
func ValidateWorkflowID(workflowID string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("workflow_id", workflowID)
	errors = append(errors, validator.Required().UUID().Errors()...)
	
	return errors
}

// Task ID validation
func ValidateTaskID(taskID string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("task_id", taskID)
	errors = append(errors, validator.Required().UUID().Errors()...)
	
	return errors
}

// User ID validation
func ValidateUserID(userID string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("user_id", userID)
	errors = append(errors, validator.Required().UUID().Errors()...)
	
	return errors
}

// Version validation
func ValidateVersion(version string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("version", version)
	errors = append(errors, validator.Required().SemVer().Errors()...)
	
	return errors
}

// Environment validation (dev, staging, prod)
func ValidateEnvironment(env string) ValidationErrors {
	var errors ValidationErrors
	
	validator := NewFieldValidator("environment", env)
	errors = append(errors, validator.Required().OneOf("dev", "development", "staging", "prod", "production").Errors()...)
	
	return errors
}