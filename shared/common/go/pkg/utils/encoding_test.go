package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBase64Encoding(t *testing.T) {
	data := []byte("hello world")
	
	encoded := EncodeBase64(data)
	assert.NotEmpty(t, encoded)
	
	decoded, err := DecodeBase64(encoded)
	require.NoError(t, err)
	assert.Equal(t, data, decoded)
}

func TestBase64URLEncoding(t *testing.T) {
	data := []byte("hello world with special chars +/=")
	
	encoded := EncodeBase64URL(data)
	assert.NotEmpty(t, encoded)
	assert.NotContains(t, encoded, "+")
	assert.NotContains(t, encoded, "/")
	
	decoded, err := DecodeBase64URL(encoded)
	require.NoError(t, err)
	assert.Equal(t, data, decoded)
}

func TestHexEncoding(t *testing.T) {
	data := []byte("hello")
	
	encoded := EncodeHex(data)
	assert.Equal(t, "68656c6c6f", encoded)
	
	decoded, err := DecodeHex(encoded)
	require.NoError(t, err)
	assert.Equal(t, data, decoded)
}

func TestJSONEncoding(t *testing.T) {
	data := map[string]interface{}{
		"name": "test",
		"age":  30,
		"tags": []string{"go", "testing"},
	}
	
	encoded, err := EncodeJSON(data)
	require.NoError(t, err)
	assert.NotEmpty(t, encoded)
	
	var decoded map[string]interface{}
	err = DecodeJSON(encoded, &decoded)
	require.NoError(t, err)
	assert.Equal(t, "test", decoded["name"])
	assert.Equal(t, float64(30), decoded["age"]) // JSON numbers become float64
}

func TestJSONPrettyEncoding(t *testing.T) {
	data := map[string]string{"key": "value"}
	
	encoded, err := EncodeJSONPretty(data)
	require.NoError(t, err)
	assert.Contains(t, string(encoded), "\n")
	assert.Contains(t, string(encoded), "  ")
}

func TestSHA256Hashing(t *testing.T) {
	data := []byte("hello world")
	
	hash := HashSHA256(data)
	assert.Len(t, hash, 32) // SHA256 produces 32 bytes
	
	hashString := HashSHA256String("hello world")
	assert.Len(t, hashString, 64) // 32 bytes * 2 hex chars per byte
	assert.Equal(t, EncodeHex(hash), hashString)
}

func TestPasswordHashing(t *testing.T) {
	password := "mySecurePassword123!"
	
	hash, err := HashPassword(password)
	require.NoError(t, err)
	assert.NotEmpty(t, hash)
	assert.NotEqual(t, password, hash)
	
	// Test comparison
	assert.True(t, ComparePassword(password, hash))
	assert.False(t, ComparePassword("wrongPassword", hash))
}

func TestAESEncryption(t *testing.T) {
	key := []byte("this-is-a-32-byte-key-for-aes256") // 32 bytes for AES-256
	
	encryptor, err := NewAESEncryptor(key)
	require.NoError(t, err)
	
	plaintext := []byte("secret message")
	
	// Test encryption/decryption
	ciphertext, err := encryptor.Encrypt(plaintext)
	require.NoError(t, err)
	assert.NotEqual(t, plaintext, ciphertext)
	
	decrypted, err := encryptor.Decrypt(ciphertext)
	require.NoError(t, err)
	assert.Equal(t, plaintext, decrypted)
	
	// Test string encryption/decryption
	encrypted, err := encryptor.EncryptString("secret message")
	require.NoError(t, err)
	assert.NotEmpty(t, encrypted)
	
	decryptedString, err := encryptor.DecryptString(encrypted)
	require.NoError(t, err)
	assert.Equal(t, "secret message", decryptedString)
}

func TestAESEncryptorFromString(t *testing.T) {
	key := "mySecretKey"
	
	encryptor, err := NewAESEncryptorFromString(key)
	require.NoError(t, err)
	
	plaintext := "test message"
	encrypted, err := encryptor.EncryptString(plaintext)
	require.NoError(t, err)
	
	decrypted, err := encryptor.DecryptString(encrypted)
	require.NoError(t, err)
	assert.Equal(t, plaintext, decrypted)
}

func TestAESEncryptorInvalidKey(t *testing.T) {
	// Test invalid key sizes
	_, err := NewAESEncryptor([]byte("short"))
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid key size")
	
	_, err = NewAESEncryptor([]byte("this-is-too-long-for-aes-key-size-limits"))
	assert.Error(t, err)
}

func TestGenerateAESKey(t *testing.T) {
	// Test valid key sizes
	for _, size := range []int{16, 24, 32} {
		key, err := GenerateAESKey(size)
		require.NoError(t, err)
		assert.Len(t, key, size)
		
		keyString, err := GenerateAESKeyString(size)
		require.NoError(t, err)
		assert.Len(t, keyString, size*2) // hex encoding doubles the length
	}
	
	// Test invalid key size
	_, err := GenerateAESKey(10)
	assert.Error(t, err)
}

func TestToString(t *testing.T) {
	tests := []struct {
		input    interface{}
		expected string
	}{
		{"hello", "hello"},
		{[]byte("hello"), "hello"},
		{123, "123"},
		{int64(456), "456"},
		{123.45, "123.450000"},
		{true, "true"},
		{false, "false"},
	}
	
	for _, test := range tests {
		result := ToString(test.input)
		assert.Equal(t, test.expected, result)
	}
}

func TestToBytes(t *testing.T) {
	tests := []struct {
		input    interface{}
		expected []byte
	}{
		{[]byte("hello"), []byte("hello")},
		{"hello", []byte("hello")},
		{123, []byte("123")},
	}
	
	for _, test := range tests {
		result := ToBytes(test.input)
		assert.Equal(t, test.expected, result)
	}
}

func TestGenerateSecureToken(t *testing.T) {
	token, err := GenerateSecureToken(32)
	require.NoError(t, err)
	assert.NotEmpty(t, token)
	
	// Generate another token to ensure uniqueness
	token2, err := GenerateSecureToken(32)
	require.NoError(t, err)
	assert.NotEqual(t, token, token2)
}

func TestMaskString(t *testing.T) {
	tests := []struct {
		input       string
		visibleChars int
		expected    string
	}{
		{"password", 2, "pa******"},
		{"short", 10, "*****"}, // shorter than visible chars
		{"test", 2, "te**"},
	}
	
	for _, test := range tests {
		result := MaskString(test.input, test.visibleChars)
		assert.Equal(t, test.expected, result)
	}
}

func TestMaskEmail(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"<EMAIL>", "us**@example.com"},
		{"<EMAIL>", "*@example.com"},
		{"invalid-email", "in************"},
	}
	
	for _, test := range tests {
		result := MaskEmail(test.input)
		assert.Equal(t, test.expected, result)
	}
}

func TestComputeAndVerifyChecksum(t *testing.T) {
	data := []byte("test data for checksum")
	
	checksum := ComputeChecksum(data)
	assert.NotEmpty(t, checksum)
	assert.Len(t, checksum, 64) // SHA256 hex string length
	
	// Verify correct checksum
	assert.True(t, VerifyChecksum(data, checksum))
	
	// Verify incorrect checksum
	assert.False(t, VerifyChecksum(data, "wrong-checksum"))
	
	// Verify with modified data
	modifiedData := []byte("modified data")
	assert.False(t, VerifyChecksum(modifiedData, checksum))
}

func TestAESDecryptionError(t *testing.T) {
	key := []byte("this-is-a-32-byte-key-for-aes256")
	encryptor, err := NewAESEncryptor(key)
	require.NoError(t, err)
	
	// Test decryption with invalid data
	_, err = encryptor.Decrypt([]byte("invalid"))
	assert.Error(t, err)
	
	// Test string decryption with invalid base64
	_, err = encryptor.DecryptString("invalid-base64!")
	assert.Error(t, err)
}