package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsEmpty(t *testing.T) {
	assert.True(t, IsEmpty(""))
	assert.True(t, IsEmpty("   "))
	assert.True(t, <PERSON>E<PERSON><PERSON>("\t\n"))
	assert.<PERSON>als<PERSON>(t, <PERSON><PERSON><PERSON><PERSON>("test"))
	assert.False(t, <PERSON><PERSON><PERSON><PERSON>("  test  "))
}

func TestIsNotEmpty(t *testing.T) {
	assert.False(t, IsNotEmpty(""))
	assert.False(t, IsNotEmpty("   "))
	assert.True(t, IsNotEmpty("test"))
	assert.True(t, IsNotEmpty("  test  "))
}

func TestHasMinLength(t *testing.T) {
	assert.True(t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("hello", 5))
	assert.True(t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("hello world", 5))
	assert.False(t, <PERSON><PERSON><PERSON><PERSON>ength("hi", 5))
	assert.True(t, <PERSON><PERSON><PERSON><PERSON>ength("  hello  ", 5)) // trimmed length
}

func TestHasMaxLength(t *testing.T) {
	assert.True(t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("hello", 10))
	assert.True(t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("hello", 5))
	assert.<PERSON>alse(t, HasMaxLength("hello world", 5))
	assert.True(t, HasMaxLength("  hello  ", 10)) // trimmed length
}

func TestIsAlphanumeric(t *testing.T) {
	assert.True(t, IsAlphanumeric("abc123"))
	assert.True(t, IsAlphanumeric("ABC123"))
	assert.True(t, IsAlphanumeric("123"))
	assert.True(t, IsAlphanumeric("abc"))
	assert.False(t, IsAlphanumeric("abc-123"))
	assert.False(t, IsAlphanumeric("abc 123"))
	assert.False(t, IsAlphanumeric("abc@123"))
}

func TestIsValidEmail(t *testing.T) {
	assert.True(t, IsValidEmail("<EMAIL>"))
	assert.True(t, IsValidEmail("<EMAIL>"))
	assert.True(t, IsValidEmail("<EMAIL>"))
	assert.False(t, IsValidEmail("invalid-email"))
	assert.False(t, IsValidEmail("@example.com"))
	assert.False(t, IsValidEmail("test@"))
}

func TestIsValidUUID(t *testing.T) {
	assert.True(t, IsValidUUID("550e8400-e29b-41d4-a716-************"))
	assert.True(t, IsValidUUID("6ba7b810-9dad-11d1-80b4-00c04fd430c8"))
	assert.False(t, IsValidUUID("not-a-uuid"))
	assert.False(t, IsValidUUID("550e8400-e29b-41d4-a716"))
	assert.False(t, IsValidUUID("550e8400-e29b-41d4-a716-************-extra"))
}

func TestIsValidSlug(t *testing.T) {
	assert.True(t, IsValidSlug("hello-world"))
	assert.True(t, IsValidSlug("test123"))
	assert.True(t, IsValidSlug("simple"))
	assert.False(t, IsValidSlug("Hello-World")) // uppercase
	assert.False(t, IsValidSlug("hello_world")) // underscore
	assert.False(t, IsValidSlug("hello world")) // space
	assert.False(t, IsValidSlug("-hello"))      // starts with hyphen
	assert.False(t, IsValidSlug("hello-"))      // ends with hyphen
}

func TestIsValidSemVer(t *testing.T) {
	assert.True(t, IsValidSemVer("1.0.0"))
	assert.True(t, IsValidSemVer("v1.0.0"))
	assert.True(t, IsValidSemVer("1.0.0-alpha"))
	assert.True(t, IsValidSemVer("1.0.0-alpha.1"))
	assert.True(t, IsValidSemVer("1.0.0+build.1"))
	assert.True(t, IsValidSemVer("1.0.0-alpha+build.1"))
	assert.False(t, IsValidSemVer("1.0"))
	assert.False(t, IsValidSemVer("1"))
	assert.False(t, IsValidSemVer("v1.0"))
	assert.False(t, IsValidSemVer("1.0.0.0"))
}

func TestPasswordValidation(t *testing.T) {
	assert.True(t, HasUppercase("Hello"))
	assert.False(t, HasUppercase("hello"))

	assert.True(t, HasLowercase("Hello"))
	assert.False(t, HasLowercase("HELLO"))

	assert.True(t, HasDigit("Hello123"))
	assert.False(t, HasDigit("Hello"))

	assert.True(t, HasSpecialChar("Hello!"))
	assert.False(t, HasSpecialChar("Hello123"))
}

func TestFieldValidator(t *testing.T) {
	// Test required validation
	validator := NewFieldValidator("username", "")
	errors := validator.Required().Errors()
	assert.Len(t, errors, 1)
	assert.Equal(t, "username", errors[0].Field)
	assert.Contains(t, errors[0].Message, "required")

	// Test min length validation
	validator = NewFieldValidator("password", "123")
	errors = validator.MinLength(8).Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "at least 8 characters")

	// Test max length validation
	validator = NewFieldValidator("username", "verylongusernamethatexceedslimit")
	errors = validator.MaxLength(10).Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "at most 10 characters")

	// Test email validation
	validator = NewFieldValidator("email", "invalid-email")
	errors = validator.Email().Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "valid email")

	// Test UUID validation
	validator = NewFieldValidator("id", "not-a-uuid")
	errors = validator.UUID().Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "valid UUID")

	// Test slug validation
	validator = NewFieldValidator("slug", "Invalid Slug")
	errors = validator.Slug().Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "valid slug")

	// Test semver validation
	validator = NewFieldValidator("version", "1.0")
	errors = validator.SemVer().Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "semantic version")

	// Test alphanumeric validation
	validator = NewFieldValidator("code", "abc-123")
	errors = validator.Alphanumeric().Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "alphanumeric")

	// Test one of validation
	validator = NewFieldValidator("status", "invalid")
	errors = validator.OneOf("active", "inactive", "pending").Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "must be one of")

	// Test custom validation
	validator = NewFieldValidator("custom", "test")
	errors = validator.Custom(func(v interface{}) bool {
		str, ok := v.(string)
		return ok && str == "valid"
	}, "must be 'valid'").Errors()
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "must be 'valid'")
}

func TestValidatePassword(t *testing.T) {
	// Valid password
	errors := ValidatePassword("SecurePass123!")
	assert.Len(t, errors, 0)

	// Too short
	errors = ValidatePassword("Short1!")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "at least 8 characters")

	// Missing uppercase
	errors = ValidatePassword("password123!")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "uppercase letter")

	// Missing lowercase
	errors = ValidatePassword("PASSWORD123!")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "lowercase letter")

	// Missing digit
	errors = ValidatePassword("SecurePass!")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "digit")

	// Missing special character
	errors = ValidatePassword("SecurePass123")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "special character")

	// Multiple issues
	errors = ValidatePassword("pass")
	assert.True(t, len(errors) > 1)
}

func TestValidateAgentID(t *testing.T) {
	// Valid agent ID
	errors := ValidateAgentID("my-agent")
	assert.Len(t, errors, 0)

	// Empty
	errors = ValidateAgentID("")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "required")

	// Too short
	errors = ValidateAgentID("ab")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "at least 3 characters")

	// Invalid format
	errors = ValidateAgentID("My_Agent")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "valid slug")
}

func TestValidateWorkflowID(t *testing.T) {
	// Valid workflow ID
	errors := ValidateWorkflowID("550e8400-e29b-41d4-a716-************")
	assert.Len(t, errors, 0)

	// Invalid UUID
	errors = ValidateWorkflowID("not-a-uuid")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "valid UUID")
}

func TestValidateEnvironment(t *testing.T) {
	// Valid environments
	validEnvs := []string{"dev", "development", "staging", "prod", "production"}
	for _, env := range validEnvs {
		errors := ValidateEnvironment(env)
		assert.Len(t, errors, 0, "Environment %s should be valid", env)
	}

	// Invalid environment
	errors := ValidateEnvironment("invalid")
	assert.Len(t, errors, 1)
	assert.Contains(t, errors[0].Message, "must be one of")
}

func TestValidationErrors(t *testing.T) {
	var errors ValidationErrors

	// Empty errors
	assert.False(t, errors.HasErrors())
	assert.Equal(t, "no validation errors", errors.Error())

	// Add errors
	errors = append(errors, ValidationError{Field: "field1", Message: "error1"})
	errors = append(errors, ValidationError{Field: "field2", Message: "error2"})

	assert.True(t, errors.HasErrors())
	assert.Contains(t, errors.Error(), "field1")
	assert.Contains(t, errors.Error(), "field2")
	assert.Contains(t, errors.Error(), "error1")
	assert.Contains(t, errors.Error(), "error2")
}

func TestValidationError(t *testing.T) {
	err := ValidationError{Field: "username", Message: "is required"}
	expected := "validation error for field 'username': is required"
	assert.Equal(t, expected, err.Error())
}