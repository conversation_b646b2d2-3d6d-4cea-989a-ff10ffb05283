package metrics

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewPrometheusRegistry(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	assert.NotNil(t, registry)
	assert.Equal(t, "test", registry.namespace)
	assert.Equal(t, "subsystem", registry.subsystem)
}

func TestPrometheusRegistry_NewCounter(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	
	counter := registry.NewCounter("test_counter", "Test counter", Labels{"env": "test"})
	require.NotNil(t, counter)
	
	assert.Equal(t, "test_counter", counter.Name())
	assert.Equal(t, CounterType, counter.Type())
	assert.Equal(t, "Test counter", counter.Help())
	
	// Test counter operations
	counter.Inc()
	assert.Equal(t, float64(1), counter.Get())
	
	counter.Add(5)
	assert.Equal(t, float64(6), counter.Get())
}

func TestPrometheusRegistry_NewGauge(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	
	gauge := registry.NewGauge("test_gauge", "Test gauge", Labels{"env": "test"})
	require.NotNil(t, gauge)
	
	assert.Equal(t, "test_gauge", gauge.Name())
	assert.Equal(t, GaugeType, gauge.Type())
	assert.Equal(t, "Test gauge", gauge.Help())
	
	// Test gauge operations
	gauge.Set(10)
	assert.Equal(t, float64(10), gauge.Get())
	
	gauge.Inc()
	assert.Equal(t, float64(11), gauge.Get())
	
	gauge.Dec()
	assert.Equal(t, float64(10), gauge.Get())
	
	gauge.Add(5)
	assert.Equal(t, float64(15), gauge.Get())
	
	gauge.Sub(3)
	assert.Equal(t, float64(12), gauge.Get())
}

func TestPrometheusRegistry_NewHistogram(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	buckets := []float64{0.1, 0.5, 1.0, 2.5, 5.0}
	
	histogram := registry.NewHistogram("test_histogram", "Test histogram", Labels{"env": "test"}, buckets)
	require.NotNil(t, histogram)
	
	assert.Equal(t, "test_histogram", histogram.Name())
	assert.Equal(t, HistogramType, histogram.Type())
	assert.Equal(t, "Test histogram", histogram.Help())
	
	// Test histogram operations
	histogram.Observe(0.3)
	histogram.Observe(1.2)
	histogram.Observe(0.8)
}

func TestPrometheusRegistry_NewSummary(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	objectives := map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.99: 0.001}
	
	summary := registry.NewSummary("test_summary", "Test summary", Labels{"env": "test"}, objectives)
	require.NotNil(t, summary)
	
	assert.Equal(t, "test_summary", summary.Name())
	assert.Equal(t, SummaryType, summary.Type())
	assert.Equal(t, "Test summary", summary.Help())
	
	// Test summary operations
	summary.Observe(0.3)
	summary.Observe(1.2)
	summary.Observe(0.8)
}

func TestPrometheusRegistry_NewCounterVec(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	
	counterVec := registry.NewCounterVec("test_counter_vec", "Test counter vector", []string{"method", "status"})
	require.NotNil(t, counterVec)
	
	// Test vector operations
	counter1 := counterVec.WithLabelValues("GET", "200")
	counter1.Inc()
	assert.Equal(t, float64(1), counter1.Get())
	
	counter2 := counterVec.With(Labels{"method": "POST", "status": "201"})
	counter2.Add(3)
	assert.Equal(t, float64(3), counter2.Get())
}

func TestPrometheusRegistry_NewGaugeVec(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	
	gaugeVec := registry.NewGaugeVec("test_gauge_vec", "Test gauge vector", []string{"instance", "type"})
	require.NotNil(t, gaugeVec)
	
	// Test vector operations
	gauge1 := gaugeVec.WithLabelValues("server1", "cpu")
	gauge1.Set(75.5)
	assert.Equal(t, float64(75.5), gauge1.Get())
	
	gauge2 := gaugeVec.With(Labels{"instance": "server2", "type": "memory"})
	gauge2.Set(80.2)
	assert.Equal(t, float64(80.2), gauge2.Get())
}

func TestPrometheusRegistry_NewHistogramVec(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	buckets := []float64{0.1, 0.5, 1.0, 2.5, 5.0}
	
	histogramVec := registry.NewHistogramVec("test_histogram_vec", "Test histogram vector", []string{"method", "endpoint"}, buckets)
	require.NotNil(t, histogramVec)
	
	// Test vector operations
	histogram1 := histogramVec.WithLabelValues("GET", "/api/users")
	histogram1.Observe(0.3)
	histogram1.Observe(0.8)
	
	histogram2 := histogramVec.With(Labels{"method": "POST", "endpoint": "/api/users"})
	histogram2.Observe(1.2)
}

func TestPrometheusRegistry_NewSummaryVec(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	objectives := map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.99: 0.001}
	
	summaryVec := registry.NewSummaryVec("test_summary_vec", "Test summary vector", []string{"method", "endpoint"}, objectives)
	require.NotNil(t, summaryVec)
	
	// Test vector operations
	summary1 := summaryVec.WithLabelValues("GET", "/api/users")
	summary1.Observe(0.3)
	summary1.Observe(0.8)
	
	summary2 := summaryVec.With(Labels{"method": "POST", "endpoint": "/api/users"})
	summary2.Observe(1.2)
}

func TestMetricsServer(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	server := NewMetricsServer(9999, "/test-metrics", registry.registry)
	
	assert.NotNil(t, server)
	assert.Equal(t, 9999, server.Port())
	assert.Equal(t, "/test-metrics", server.Path())
	
	// Test server lifecycle
	err := server.Start()
	require.NoError(t, err)
	
	// Give server time to start
	time.Sleep(100 * time.Millisecond)
	
	err = server.Stop()
	require.NoError(t, err)
	
	// Test double start error
	server2 := NewMetricsServer(9998, "/metrics", registry.registry)
	err = server2.Start()
	require.NoError(t, err)
	
	err = server2.Start()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already running")
	
	server2.Stop()
}

func TestNewPrometheusProvider(t *testing.T) {
	config := &Config{
		Enabled:   true,
		Port:      9090,
		Path:      "/metrics",
		Namespace: "test",
		Subsystem: "platform",
	}
	
	provider := NewPrometheusProvider(config)
	require.NotNil(t, provider)
	
	assert.NotNil(t, provider.GetRegistry())
	assert.NotNil(t, provider.GetStandardMetrics())
	assert.NotNil(t, provider.GetServer())
	
	// Test standard metrics initialization
	metrics := provider.GetStandardMetrics()
	assert.NotNil(t, metrics.RequestsTotal)
	assert.NotNil(t, metrics.RequestDuration)
	assert.NotNil(t, metrics.AgentsTotal)
	assert.NotNil(t, metrics.TasksTotal)
	assert.NotNil(t, metrics.WorkflowsTotal)
	assert.NotNil(t, metrics.CPUUsage)
	assert.NotNil(t, metrics.DBConnections)
	assert.NotNil(t, metrics.ErrorsTotal)
}

func TestPrometheusProvider_ServerOperations(t *testing.T) {
	config := &Config{
		Enabled:   true,
		Port:      9091,
		Path:      "/metrics",
		Namespace: "test",
		Subsystem: "platform",
	}
	
	provider := NewPrometheusProvider(config)
	require.NotNil(t, provider)
	
	// Test server start
	err := provider.StartServer()
	require.NoError(t, err)
	
	time.Sleep(100 * time.Millisecond)
	
	// Test server stop
	err = provider.StopServer()
	require.NoError(t, err)
	
	// Test shutdown
	err = provider.Shutdown()
	require.NoError(t, err)
}

func TestPrometheusProvider_DisabledServer(t *testing.T) {
	config := &Config{
		Enabled:   false,
		Namespace: "test",
		Subsystem: "platform",
	}
	
	provider := NewPrometheusProvider(config)
	require.NotNil(t, provider)
	
	assert.Nil(t, provider.GetServer())
	
	// Test operations on disabled server
	err := provider.StartServer()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not configured")
	
	err = provider.StopServer()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not configured")
}

func TestRegistry_Gather(t *testing.T) {
	registry := NewPrometheusRegistry("test", "subsystem")
	
	// Create some metrics
	counter := registry.NewCounter("test_counter", "Test counter", nil)
	gauge := registry.NewGauge("test_gauge", "Test gauge", nil)
	
	counter.Inc()
	gauge.Set(42)
	
	families, err := registry.Gather()
	require.NoError(t, err)
	assert.NotEmpty(t, families)
}