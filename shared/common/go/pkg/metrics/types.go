package metrics

import (
	"time"
)

type MetricType string

const (
	CounterType   MetricType = "counter"
	GaugeType     MetricType = "gauge"
	HistogramType MetricType = "histogram"
	SummaryType   MetricType = "summary"
)

type Labels map[string]string

type Metric interface {
	Name() string
	Type() MetricType
	Help() string
	Labels() Labels
}

type Counter interface {
	Metric
	Inc()
	Add(delta float64)
	Get() float64
}

type Gauge interface {
	Metric
	Set(value float64)
	Inc()
	Dec()
	Add(delta float64)
	Sub(delta float64)
	Get() float64
}

type Histogram interface {
	Metric
	Observe(value float64)
	ObserveWithLabels(value float64, labels Labels)
}

type Summary interface {
	Metric
	Observe(value float64)
	ObserveWithLabels(value float64, labels Labels)
}

type Timer interface {
	ObserveDuration()
	ObserveDurationWithLabels(labels Labels)
}

type Registry interface {
	Register(metric Metric) error
	Unregister(metric Metric) error
	MustRegister(metrics ...Metric)
	
	NewCounter(name, help string, labels Labels) Counter
	NewGauge(name, help string, labels Labels) Gauge
	NewHistogram(name, help string, labels Labels, buckets []float64) Histogram
	NewSummary(name, help string, labels Labels, objectives map[float64]float64) Summary
	
	NewCounterVec(name, help string, labelNames []string) CounterVec
	NewGaugeVec(name, help string, labelNames []string) GaugeVec
	NewHistogramVec(name, help string, labelNames []string, buckets []float64) HistogramVec
	NewSummaryVec(name, help string, labelNames []string, objectives map[float64]float64) SummaryVec
	
	Gather() ([]*MetricFamily, error)
}

type CounterVec interface {
	With(labels Labels) Counter
	WithLabelValues(values ...string) Counter
	GetMetricWith(labels Labels) (Counter, error)
	Delete(labels Labels) bool
	DeleteLabelValues(values ...string) bool
	Reset()
}

type GaugeVec interface {
	With(labels Labels) Gauge
	WithLabelValues(values ...string) Gauge
	GetMetricWith(labels Labels) (Gauge, error)
	Delete(labels Labels) bool
	DeleteLabelValues(values ...string) bool
	Reset()
}

type HistogramVec interface {
	With(labels Labels) Histogram
	WithLabelValues(values ...string) Histogram
	GetMetricWith(labels Labels) (Histogram, error)
	Delete(labels Labels) bool
	DeleteLabelValues(values ...string) bool
	Reset()
}

type SummaryVec interface {
	With(labels Labels) Summary
	WithLabelValues(values ...string) Summary
	GetMetricWith(labels Labels) (Summary, error)
	Delete(labels Labels) bool
	DeleteLabelValues(values ...string) bool
	Reset()
}

type MetricFamily struct {
	Name    string        `json:"name"`
	Help    string        `json:"help"`
	Type    MetricType    `json:"type"`
	Metrics []*MetricData `json:"metrics"`
}

type MetricData struct {
	Labels    Labels    `json:"labels,omitempty"`
	Value     float64   `json:"value"`
	Timestamp time.Time `json:"timestamp"`
}

type Config struct {
	Enabled    bool   `yaml:"enabled"`
	Port       int    `yaml:"port"`
	Path       string `yaml:"path"`
	Namespace  string `yaml:"namespace"`
	Subsystem  string `yaml:"subsystem"`
}

type Server interface {
	Start() error
	Stop() error
	Port() int
	Path() string
}

// Standard metrics for platform components
type PlatformMetrics struct {
	// Request metrics
	RequestsTotal    CounterVec
	RequestDuration  HistogramVec
	RequestSize      HistogramVec
	ResponseSize     HistogramVec
	
	// Agent metrics
	AgentsTotal      GaugeVec
	AgentsActive     GaugeVec
	TasksTotal       CounterVec
	TaskDuration     HistogramVec
	TasksActive      GaugeVec
	
	// Workflow metrics
	WorkflowsTotal     CounterVec
	WorkflowDuration   HistogramVec
	WorkflowsActive    GaugeVec
	
	// System metrics
	CPUUsage         GaugeVec
	MemoryUsage      GaugeVec
	DiskUsage        GaugeVec
	NetworkIO        CounterVec
	
	// Database metrics
	DBConnections    GaugeVec
	DBQueries        CounterVec
	DBQueryDuration  HistogramVec
	
	// Error metrics
	ErrorsTotal      CounterVec
	ErrorRate        GaugeVec
}

type MetricsProvider interface {
	GetRegistry() Registry
	GetStandardMetrics() *PlatformMetrics
	GetServer() Server
	StartServer() error
	StopServer() error
	Shutdown() error
}