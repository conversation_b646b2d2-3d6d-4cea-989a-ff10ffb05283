package metrics

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	dto "github.com/prometheus/client_model/go"
)

type PrometheusRegistry struct {
	registry  *prometheus.Registry
	namespace string
	subsystem string
}

func NewPrometheusRegistry(namespace, subsystem string) *PrometheusRegistry {
	return &PrometheusRegistry{
		registry:  prometheus.NewRegistry(),
		namespace: namespace,
		subsystem: subsystem,
	}
}

func (r *PrometheusRegistry) Register(metric Metric) error {
	if promMetric, ok := metric.(prometheus.Collector); ok {
		return r.registry.Register(promMetric)
	}
	return fmt.Errorf("metric does not implement prometheus.Collector")
}

func (r *PrometheusRegistry) Unregister(metric Metric) error {
	if promMetric, ok := metric.(prometheus.Collector); ok {
		return r.registry.Unregister(promMetric)
	}
	return fmt.Errorf("metric does not implement prometheus.Collector")
}

func (r *PrometheusRegistry) MustRegister(metrics ...Metric) {
	for _, metric := range metrics {
		if err := r.Register(metric); err != nil {
			panic(err)
		}
	}
}

func (r *PrometheusRegistry) NewCounter(name, help string, labels Labels) Counter {
	opts := prometheus.CounterOpts{
		Namespace:   r.namespace,
		Subsystem:   r.subsystem,
		Name:        name,
		Help:        help,
		ConstLabels: prometheus.Labels(labels),
	}
	counter := prometheus.NewCounter(opts)
	r.registry.MustRegister(counter)
	return &PrometheusCounter{counter: counter, name: name, help: help, labels: labels}
}

func (r *PrometheusRegistry) NewGauge(name, help string, labels Labels) Gauge {
	opts := prometheus.GaugeOpts{
		Namespace:   r.namespace,
		Subsystem:   r.subsystem,
		Name:        name,
		Help:        help,
		ConstLabels: prometheus.Labels(labels),
	}
	gauge := prometheus.NewGauge(opts)
	r.registry.MustRegister(gauge)
	return &PrometheusGauge{gauge: gauge, name: name, help: help, labels: labels}
}

func (r *PrometheusRegistry) NewHistogram(name, help string, labels Labels, buckets []float64) Histogram {
	opts := prometheus.HistogramOpts{
		Namespace:   r.namespace,
		Subsystem:   r.subsystem,
		Name:        name,
		Help:        help,
		ConstLabels: prometheus.Labels(labels),
		Buckets:     buckets,
	}
	histogram := prometheus.NewHistogram(opts)
	r.registry.MustRegister(histogram)
	return &PrometheusHistogram{histogram: histogram, name: name, help: help, labels: labels}
}

func (r *PrometheusRegistry) NewSummary(name, help string, labels Labels, objectives map[float64]float64) Summary {
	opts := prometheus.SummaryOpts{
		Namespace:   r.namespace,
		Subsystem:   r.subsystem,
		Name:        name,
		Help:        help,
		ConstLabels: prometheus.Labels(labels),
		Objectives:  objectives,
	}
	summary := prometheus.NewSummary(opts)
	r.registry.MustRegister(summary)
	return &PrometheusSummary{summary: summary, name: name, help: help, labels: labels}
}

func (r *PrometheusRegistry) NewCounterVec(name, help string, labelNames []string) CounterVec {
	opts := prometheus.CounterOpts{
		Namespace: r.namespace,
		Subsystem: r.subsystem,
		Name:      name,
		Help:      help,
	}
	counterVec := prometheus.NewCounterVec(opts, labelNames)
	r.registry.MustRegister(counterVec)
	return &PrometheusCounterVec{counterVec: counterVec}
}

func (r *PrometheusRegistry) NewGaugeVec(name, help string, labelNames []string) GaugeVec {
	opts := prometheus.GaugeOpts{
		Namespace: r.namespace,
		Subsystem: r.subsystem,
		Name:      name,
		Help:      help,
	}
	gaugeVec := prometheus.NewGaugeVec(opts, labelNames)
	r.registry.MustRegister(gaugeVec)
	return &PrometheusGaugeVec{gaugeVec: gaugeVec}
}

func (r *PrometheusRegistry) NewHistogramVec(name, help string, labelNames []string, buckets []float64) HistogramVec {
	opts := prometheus.HistogramOpts{
		Namespace: r.namespace,
		Subsystem: r.subsystem,
		Name:      name,
		Help:      help,
		Buckets:   buckets,
	}
	histogramVec := prometheus.NewHistogramVec(opts, labelNames)
	r.registry.MustRegister(histogramVec)
	return &PrometheusHistogramVec{histogramVec: histogramVec}
}

func (r *PrometheusRegistry) NewSummaryVec(name, help string, labelNames []string, objectives map[float64]float64) SummaryVec {
	opts := prometheus.SummaryOpts{
		Namespace:  r.namespace,
		Subsystem:  r.subsystem,
		Name:       name,
		Help:       help,
		Objectives: objectives,
	}
	summaryVec := prometheus.NewSummaryVec(opts, labelNames)
	r.registry.MustRegister(summaryVec)
	return &PrometheusSummaryVec{summaryVec: summaryVec}
}

func (r *PrometheusRegistry) Gather() ([]*MetricFamily, error) {
	families, err := r.registry.Gather()
	if err != nil {
		return nil, err
	}

	result := make([]*MetricFamily, len(families))
	for i, family := range families {
		result[i] = &MetricFamily{
			Name: family.GetName(),
			Help: family.GetHelp(),
			Type: MetricType(family.GetType().String()),
		}
	}
	return result, nil
}

// Prometheus metric implementations
type PrometheusCounter struct {
	counter prometheus.Counter
	name    string
	help    string
	labels  Labels
}

func (c *PrometheusCounter) Name() string     { return c.name }
func (c *PrometheusCounter) Type() MetricType { return CounterType }
func (c *PrometheusCounter) Help() string     { return c.help }
func (c *PrometheusCounter) Labels() Labels   { return c.labels }
func (c *PrometheusCounter) Inc()             { c.counter.Inc() }
func (c *PrometheusCounter) Add(delta float64) { c.counter.Add(delta) }
func (c *PrometheusCounter) Get() float64 {
	metric := &dto.Metric{}
	c.counter.Write(metric)
	return metric.GetCounter().GetValue()
}

type PrometheusGauge struct {
	gauge  prometheus.Gauge
	name   string
	help   string
	labels Labels
}

func (g *PrometheusGauge) Name() string     { return g.name }
func (g *PrometheusGauge) Type() MetricType { return GaugeType }
func (g *PrometheusGauge) Help() string     { return g.help }
func (g *PrometheusGauge) Labels() Labels   { return g.labels }
func (g *PrometheusGauge) Set(value float64) { g.gauge.Set(value) }
func (g *PrometheusGauge) Inc()             { g.gauge.Inc() }
func (g *PrometheusGauge) Dec()             { g.gauge.Dec() }
func (g *PrometheusGauge) Add(delta float64) { g.gauge.Add(delta) }
func (g *PrometheusGauge) Sub(delta float64) { g.gauge.Sub(delta) }
func (g *PrometheusGauge) Get() float64 {
	metric := &dto.Metric{}
	g.gauge.Write(metric)
	return metric.GetGauge().GetValue()
}

type PrometheusHistogram struct {
	histogram prometheus.Histogram
	name      string
	help      string
	labels    Labels
}

func (h *PrometheusHistogram) Name() string     { return h.name }
func (h *PrometheusHistogram) Type() MetricType { return HistogramType }
func (h *PrometheusHistogram) Help() string     { return h.help }
func (h *PrometheusHistogram) Labels() Labels   { return h.labels }
func (h *PrometheusHistogram) Observe(value float64) { h.histogram.Observe(value) }
func (h *PrometheusHistogram) ObserveWithLabels(value float64, labels Labels) {
	// Note: For histograms with constant labels, this is the same as Observe
	h.histogram.Observe(value)
}

type PrometheusSummary struct {
	summary prometheus.Summary
	name    string
	help    string
	labels  Labels
}

func (s *PrometheusSummary) Name() string     { return s.name }
func (s *PrometheusSummary) Type() MetricType { return SummaryType }
func (s *PrometheusSummary) Help() string     { return s.help }
func (s *PrometheusSummary) Labels() Labels   { return s.labels }
func (s *PrometheusSummary) Observe(value float64) { s.summary.Observe(value) }
func (s *PrometheusSummary) ObserveWithLabels(value float64, labels Labels) {
	s.summary.Observe(value)
}

// Vector implementations
type PrometheusCounterVec struct {
	counterVec *prometheus.CounterVec
}

func (cv *PrometheusCounterVec) With(labels Labels) Counter {
	counter := cv.counterVec.With(prometheus.Labels(labels))
	return &PrometheusCounter{counter: counter}
}

func (cv *PrometheusCounterVec) WithLabelValues(values ...string) Counter {
	counter := cv.counterVec.WithLabelValues(values...)
	return &PrometheusCounter{counter: counter}
}

func (cv *PrometheusCounterVec) GetMetricWith(labels Labels) (Counter, error) {
	counter, err := cv.counterVec.GetMetricWith(prometheus.Labels(labels))
	if err != nil {
		return nil, err
	}
	return &PrometheusCounter{counter: counter}, nil
}

func (cv *PrometheusCounterVec) Delete(labels Labels) bool {
	return cv.counterVec.Delete(prometheus.Labels(labels))
}

func (cv *PrometheusCounterVec) DeleteLabelValues(values ...string) bool {
	return cv.counterVec.DeleteLabelValues(values...)
}

func (cv *PrometheusCounterVec) Reset() {
	cv.counterVec.Reset()
}

type PrometheusGaugeVec struct {
	gaugeVec *prometheus.GaugeVec
}

func (gv *PrometheusGaugeVec) With(labels Labels) Gauge {
	gauge := gv.gaugeVec.With(prometheus.Labels(labels))
	return &PrometheusGauge{gauge: gauge}
}

func (gv *PrometheusGaugeVec) WithLabelValues(values ...string) Gauge {
	gauge := gv.gaugeVec.WithLabelValues(values...)
	return &PrometheusGauge{gauge: gauge}
}

func (gv *PrometheusGaugeVec) GetMetricWith(labels Labels) (Gauge, error) {
	gauge, err := gv.gaugeVec.GetMetricWith(prometheus.Labels(labels))
	if err != nil {
		return nil, err
	}
	return &PrometheusGauge{gauge: gauge}, nil
}

func (gv *PrometheusGaugeVec) Delete(labels Labels) bool {
	return gv.gaugeVec.Delete(prometheus.Labels(labels))
}

func (gv *PrometheusGaugeVec) DeleteLabelValues(values ...string) bool {
	return gv.gaugeVec.DeleteLabelValues(values...)
}

func (gv *PrometheusGaugeVec) Reset() {
	gv.gaugeVec.Reset()
}

type PrometheusHistogramVec struct {
	histogramVec *prometheus.HistogramVec
}

func (hv *PrometheusHistogramVec) With(labels Labels) Histogram {
	histogram := hv.histogramVec.With(prometheus.Labels(labels))
	return &PrometheusHistogram{histogram: histogram}
}

func (hv *PrometheusHistogramVec) WithLabelValues(values ...string) Histogram {
	histogram := hv.histogramVec.WithLabelValues(values...)
	return &PrometheusHistogram{histogram: histogram}
}

func (hv *PrometheusHistogramVec) GetMetricWith(labels Labels) (Histogram, error) {
	histogram, err := hv.histogramVec.GetMetricWith(prometheus.Labels(labels))
	if err != nil {
		return nil, err
	}
	return &PrometheusHistogram{histogram: histogram}, nil
}

func (hv *PrometheusHistogramVec) Delete(labels Labels) bool {
	return hv.histogramVec.Delete(prometheus.Labels(labels))
}

func (hv *PrometheusHistogramVec) DeleteLabelValues(values ...string) bool {
	return hv.histogramVec.DeleteLabelValues(values...)
}

func (hv *PrometheusHistogramVec) Reset() {
	hv.histogramVec.Reset()
}

type PrometheusSummaryVec struct {
	summaryVec *prometheus.SummaryVec
}

func (sv *PrometheusSummaryVec) With(labels Labels) Summary {
	summary := sv.summaryVec.With(prometheus.Labels(labels))
	return &PrometheusSummary{summary: summary}
}

func (sv *PrometheusSummaryVec) WithLabelValues(values ...string) Summary {
	summary := sv.summaryVec.WithLabelValues(values...)
	return &PrometheusSummary{summary: summary}
}

func (sv *PrometheusSummaryVec) GetMetricWith(labels Labels) (Summary, error) {
	summary, err := sv.summaryVec.GetMetricWith(prometheus.Labels(labels))
	if err != nil {
		return nil, err
	}
	return &PrometheusSummary{summary: summary}, nil
}

func (sv *PrometheusSummaryVec) Delete(labels Labels) bool {
	return sv.summaryVec.Delete(prometheus.Labels(labels))
}

func (sv *PrometheusSummaryVec) DeleteLabelValues(values ...string) bool {
	return sv.summaryVec.DeleteLabelValues(values...)
}

func (sv *PrometheusSummaryVec) Reset() {
	sv.summaryVec.Reset()
}

// HTTP Server for metrics
type MetricsServer struct {
	server   *http.Server
	port     int
	path     string
	registry *prometheus.Registry
	mu       sync.RWMutex
	running  bool
}

func NewMetricsServer(port int, path string, registry *prometheus.Registry) *MetricsServer {
	return &MetricsServer{
		port:     port,
		path:     path,
		registry: registry,
	}
}

func (s *MetricsServer) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("metrics server is already running")
	}

	mux := http.NewServeMux()
	mux.Handle(s.path, promhttp.HandlerFor(s.registry, promhttp.HandlerOpts{}))
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	s.server = &http.Server{
		Addr:         ":" + strconv.Itoa(s.port),
		Handler:      mux,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
	}

	go func() {
		if err := s.server.ListenAndServe(); err != http.ErrServerClosed {
			// Log error in real implementation
			_ = err
		}
	}()

	s.running = true
	return nil
}

func (s *MetricsServer) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return fmt.Errorf("metrics server is not running")
	}

	if s.server != nil {
		if err := s.server.Close(); err != nil {
			return fmt.Errorf("failed to stop metrics server: %w", err)
		}
	}

	s.running = false
	return nil
}

func (s *MetricsServer) Port() int {
	return s.port
}

func (s *MetricsServer) Path() string {
	return s.path
}

// Metrics Provider implementation
type PrometheusProvider struct {
	registry        *PrometheusRegistry
	server          *MetricsServer
	standardMetrics *PlatformMetrics
	config          *Config
}

func NewPrometheusProvider(config *Config) *PrometheusProvider {
	registry := NewPrometheusRegistry(config.Namespace, config.Subsystem)
	
	var server *MetricsServer
	if config.Enabled {
		server = NewMetricsServer(config.Port, config.Path, registry.registry)
	}

	provider := &PrometheusProvider{
		registry: registry,
		server:   server,
		config:   config,
	}

	provider.initStandardMetrics()
	return provider
}

func (p *PrometheusProvider) GetRegistry() Registry {
	return p.registry
}

func (p *PrometheusProvider) GetStandardMetrics() *PlatformMetrics {
	return p.standardMetrics
}

func (p *PrometheusProvider) GetServer() Server {
	return p.server
}

func (p *PrometheusProvider) StartServer() error {
	if p.server == nil {
		return fmt.Errorf("metrics server is not configured")
	}
	return p.server.Start()
}

func (p *PrometheusProvider) StopServer() error {
	if p.server == nil {
		return fmt.Errorf("metrics server is not configured")
	}
	return p.server.Stop()
}

func (p *PrometheusProvider) Shutdown() error {
	if p.server != nil {
		return p.server.Stop()
	}
	return nil
}

func (p *PrometheusProvider) initStandardMetrics() {
	buckets := []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10}
	
	p.standardMetrics = &PlatformMetrics{
		RequestsTotal:    p.registry.NewCounterVec("requests_total", "Total number of requests", []string{"method", "endpoint", "status"}),
		RequestDuration:  p.registry.NewHistogramVec("request_duration_seconds", "Request duration in seconds", []string{"method", "endpoint"}, buckets),
		RequestSize:      p.registry.NewHistogramVec("request_size_bytes", "Request size in bytes", []string{"method", "endpoint"}, buckets),
		ResponseSize:     p.registry.NewHistogramVec("response_size_bytes", "Response size in bytes", []string{"method", "endpoint"}, buckets),
		
		AgentsTotal:      p.registry.NewGaugeVec("agents_total", "Total number of agents", []string{"status", "language"}),
		AgentsActive:     p.registry.NewGaugeVec("agents_active", "Number of active agents", []string{"language"}),
		TasksTotal:       p.registry.NewCounterVec("tasks_total", "Total number of tasks", []string{"agent_id", "status"}),
		TaskDuration:     p.registry.NewHistogramVec("task_duration_seconds", "Task duration in seconds", []string{"agent_id", "task_type"}, buckets),
		TasksActive:      p.registry.NewGaugeVec("tasks_active", "Number of active tasks", []string{"agent_id"}),
		
		WorkflowsTotal:   p.registry.NewCounterVec("workflows_total", "Total number of workflows", []string{"status"}),
		WorkflowDuration: p.registry.NewHistogramVec("workflow_duration_seconds", "Workflow duration in seconds", []string{"workflow_id"}, buckets),
		WorkflowsActive:  p.registry.NewGaugeVec("workflows_active", "Number of active workflows", []string{}),
		
		CPUUsage:         p.registry.NewGaugeVec("cpu_usage_percent", "CPU usage percentage", []string{"instance"}),
		MemoryUsage:      p.registry.NewGaugeVec("memory_usage_bytes", "Memory usage in bytes", []string{"instance", "type"}),
		DiskUsage:        p.registry.NewGaugeVec("disk_usage_bytes", "Disk usage in bytes", []string{"instance", "device"}),
		NetworkIO:        p.registry.NewCounterVec("network_io_bytes_total", "Network IO in bytes", []string{"instance", "direction"}),
		
		DBConnections:    p.registry.NewGaugeVec("db_connections", "Number of database connections", []string{"database", "status"}),
		DBQueries:        p.registry.NewCounterVec("db_queries_total", "Total number of database queries", []string{"database", "operation"}),
		DBQueryDuration:  p.registry.NewHistogramVec("db_query_duration_seconds", "Database query duration in seconds", []string{"database", "operation"}, buckets),
		
		ErrorsTotal:      p.registry.NewCounterVec("errors_total", "Total number of errors", []string{"component", "type"}),
		ErrorRate:        p.registry.NewGaugeVec("error_rate", "Error rate percentage", []string{"component"}),
	}
}