package auth

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJWTProvider_GenerateToken(t *testing.T) {
	provider := NewJWTProvider("test-secret", time.Hour, time.Hour*24)

	claims := &Claims{
		UserID:      "user123",
		Username:    "testuser",
		Roles:       []string{"admin"},
		Permissions: []string{"read", "write"},
	}

	tokenPair, err := provider.GenerateToken(claims)
	require.NoError(t, err)
	assert.NotEmpty(t, tokenPair.AccessToken)
	assert.NotEmpty(t, tokenPair.RefreshToken)
	assert.Equal(t, "Bearer", tokenPair.TokenType)
	assert.True(t, tokenPair.ExpiresAt.After(time.Now()))
}

func TestJWTProvider_ValidateToken(t *testing.T) {
	provider := NewJWTProvider("test-secret", time.Hour, time.Hour*24)

	originalClaims := &Claims{
		UserID:      "user123",
		Username:    "testuser",
		Roles:       []string{"admin"},
		Permissions: []string{"read", "write"},
		AgentID:     "agent456",
	}

	tokenPair, err := provider.GenerateToken(originalClaims)
	require.NoError(t, err)

	validatedClaims, err := provider.ValidateToken(tokenPair.AccessToken)
	require.NoError(t, err)

	assert.Equal(t, originalClaims.UserID, validatedClaims.UserID)
	assert.Equal(t, originalClaims.Username, validatedClaims.Username)
	assert.Equal(t, originalClaims.Roles, validatedClaims.Roles)
	assert.Equal(t, originalClaims.Permissions, validatedClaims.Permissions)
	assert.Equal(t, originalClaims.AgentID, validatedClaims.AgentID)
}

func TestJWTProvider_RefreshToken(t *testing.T) {
	provider := NewJWTProvider("test-secret", time.Hour, time.Hour*24)

	claims := &Claims{
		UserID:   "user123",
		Username: "testuser",
		Roles:    []string{"user"},
	}

	originalPair, err := provider.GenerateToken(claims)
	require.NoError(t, err)

	newPair, err := provider.RefreshToken(originalPair.RefreshToken)
	require.NoError(t, err)

	assert.NotEqual(t, originalPair.AccessToken, newPair.AccessToken)
	assert.NotEmpty(t, newPair.AccessToken)
	assert.NotEmpty(t, newPair.RefreshToken)
}

func TestJWTProvider_RevokeToken(t *testing.T) {
	provider := NewJWTProvider("test-secret", time.Hour, time.Hour*24)

	claims := &Claims{
		UserID:   "user123",
		Username: "testuser",
	}

	tokenPair, err := provider.GenerateToken(claims)
	require.NoError(t, err)

	// Token should be valid initially
	_, err = provider.ValidateToken(tokenPair.AccessToken)
	require.NoError(t, err)

	// Revoke the token
	err = provider.RevokeToken(tokenPair.AccessToken)
	require.NoError(t, err)

	// Token should be invalid after revocation
	_, err = provider.ValidateToken(tokenPair.AccessToken)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "revoked")
}

func TestHashPassword(t *testing.T) {
	password := "testpassword123"
	hash, err := HashPassword(password)
	require.NoError(t, err)
	assert.NotEmpty(t, hash)
	assert.NotEqual(t, password, hash)
}

func TestCheckPassword(t *testing.T) {
	password := "testpassword123"
	hash, err := HashPassword(password)
	require.NoError(t, err)

	// Correct password should pass
	assert.True(t, CheckPassword(password, hash))

	// Incorrect password should fail
	assert.False(t, CheckPassword("wrongpassword", hash))
}

func TestGenerateSecureSecret(t *testing.T) {
	secret, err := GenerateSecureSecret(32)
	require.NoError(t, err)
	assert.Len(t, secret, 64) // hex encoding doubles the length
	assert.NotEmpty(t, secret)

	// Generate another secret to ensure randomness
	secret2, err := GenerateSecureSecret(32)
	require.NoError(t, err)
	assert.NotEqual(t, secret, secret2)
}

func TestJWTProvider_InvalidToken(t *testing.T) {
	provider := NewJWTProvider("test-secret", time.Hour, time.Hour*24)

	// Test with invalid token
	_, err := provider.ValidateToken("invalid.token.here")
	assert.Error(t, err)

	// Test with token signed with different secret
	otherProvider := NewJWTProvider("other-secret", time.Hour, time.Hour*24)
	claims := &Claims{UserID: "user123", Username: "testuser"}
	
	tokenPair, err := otherProvider.GenerateToken(claims)
	require.NoError(t, err)

	_, err = provider.ValidateToken(tokenPair.AccessToken)
	assert.Error(t, err)
}