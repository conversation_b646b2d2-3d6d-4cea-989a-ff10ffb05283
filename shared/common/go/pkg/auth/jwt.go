package auth

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

type JWTProvider struct {
	secret           []byte
	accessDuration   time.Duration
	refreshDuration  time.Duration
	revokedTokens    map[string]bool // In production, use Redis or similar
}

func NewJWTProvider(secret string, accessDuration, refreshDuration time.Duration) *JWTProvider {
	return &JWTProvider{
		secret:          []byte(secret),
		accessDuration:  accessDuration,
		refreshDuration: refreshDuration,
		revokedTokens:   make(map[string]bool),
	}
}

func (j *JWTProvider) GenerateToken(claims *Claims) (*TokenPair, error) {
	now := time.Now()
	
	// Set standard claims
	claims.IssuedAt = jwt.NewNumericDate(now)
	claims.ExpiresAt = jwt.NewNumericDate(now.Add(j.accessDuration))
	claims.NotBefore = jwt.NewNumericDate(now)

	// Generate access token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	accessToken, err := token.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// Generate refresh token
	refreshClaims := &Claims{
		UserID:   claims.UserID,
		Username: claims.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshDuration)),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	refreshTokenJWT := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshToken, err := refreshTokenJWT.SignedString(j.secret)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    claims.ExpiresAt.Time,
		TokenType:    "Bearer",
	}, nil
}

func (j *JWTProvider) ValidateToken(tokenString string) (*Claims, error) {
	// Check if token is revoked
	if j.revokedTokens[tokenString] {
		return nil, fmt.Errorf("token has been revoked")
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

func (j *JWTProvider) RefreshToken(refreshToken string) (*TokenPair, error) {
	claims, err := j.ValidateToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Create new claims with updated expiration
	newClaims := &Claims{
		UserID:      claims.UserID,
		Username:    claims.Username,
		Roles:       claims.Roles,
		Permissions: claims.Permissions,
		AgentID:     claims.AgentID,
	}

	return j.GenerateToken(newClaims)
}

func (j *JWTProvider) RevokeToken(token string) error {
	j.revokedTokens[token] = true
	return nil
}

func GenerateSecureSecret(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(bytes), nil
}

func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func (j *JWTProvider) IsTokenRevoked(token string) bool {
	return j.revokedTokens[token]
}

func (j *JWTProvider) CleanupExpiredTokens() {
	// In production, implement cleanup of expired revoked tokens
	// This is a simplified version for demonstration
	for token := range j.revokedTokens {
		claims, err := j.ValidateToken(token)
		if err != nil || claims.ExpiresAt.Before(time.Now()) {
			delete(j.revokedTokens, token)
		}
	}
}