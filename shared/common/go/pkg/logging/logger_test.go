package logging

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewZapLogger(t *testing.T) {
	config := &Config{
		Level:       InfoLevel,
		Format:      "json",
		Output:      "stdout",
		Development: false,
	}

	logger, err := NewZapLogger(config)
	require.NoError(t, err)
	assert.NotNil(t, logger)
	assert.Equal(t, InfoLevel, logger.GetLevel())
}

func TestZapLogger_SetLevel(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewZapLogger(config)
	require.NoError(t, err)

	logger.SetLevel(DebugLevel)
	assert.Equal(t, DebugLevel, logger.GetLevel())

	logger.SetLevel(ErrorLevel)
	assert.Equal(t, ErrorLevel, logger.GetLevel())
}

func TestZapLogger_With(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewZapLogger(config)
	require.NoError(t, err)

	childLogger := logger.With(
		Field{Key: "component", Value: "test"},
		Field{Key: "version", Value: "1.0.0"},
	)

	assert.NotNil(t, childLogger)
	assert.NotEqual(t, logger, childLogger)
}

func TestZapLogger_WithContext(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewZapLogger(config)
	require.NoError(t, err)

	ctx := context.Background()
	ctx = context.WithValue(ctx, TraceIDKey, "trace-123")
	ctx = context.WithValue(ctx, UserIDKey, "user-456")
	ctx = context.WithValue(ctx, AgentIDKey, "agent-789")

	contextLogger := logger.WithContext(ctx)
	assert.NotNil(t, contextLogger)
	assert.NotEqual(t, logger, contextLogger)
}

func TestLoggerFactory(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "stdout",
	}

	factory := NewLoggerFactory(config)
	assert.NotNil(t, factory)

	// Create a logger
	logger, err := factory.CreateLogger("test-logger", config)
	require.NoError(t, err)
	assert.NotNil(t, logger)

	// Get the logger
	retrievedLogger, err := factory.GetLogger("test-logger")
	require.NoError(t, err)
	assert.Equal(t, logger, retrievedLogger)

	// Try to get non-existent logger
	_, err = factory.GetLogger("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestLoggerFactory_SetGlobalLevel(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "stdout",
	}

	factory := NewLoggerFactory(config)
	
	logger1, err := factory.CreateLogger("logger1", config)
	require.NoError(t, err)
	
	logger2, err := factory.CreateLogger("logger2", config)
	require.NoError(t, err)

	// Set global level
	factory.SetGlobalLevel(ErrorLevel)

	assert.Equal(t, ErrorLevel, logger1.GetLevel())
	assert.Equal(t, ErrorLevel, logger2.GetLevel())
}

func TestLevelConversions(t *testing.T) {
	tests := []struct {
		level    Level
		expected string
	}{
		{DebugLevel, "debug"},
		{InfoLevel, "info"},
		{WarnLevel, "warn"},
		{ErrorLevel, "error"},
		{FatalLevel, "fatal"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.level.String())
		})
	}
}

func TestZapLogger_LogMethods(t *testing.T) {
	config := &Config{
		Level:  DebugLevel,
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewZapLogger(config)
	require.NoError(t, err)

	// Test that logging methods don't panic
	assert.NotPanics(t, func() {
		logger.Debug("debug message", Field{Key: "key", Value: "value"})
		logger.Info("info message", Field{Key: "count", Value: 42})
		logger.Warn("warn message", Field{Key: "warning", Value: true})
		logger.Error("error message", Field{Key: "error", Value: "test error"})
	})
}

func TestGlobalLoggerFunctions(t *testing.T) {
	// Test that global functions don't panic
	assert.NotPanics(t, func() {
		Debug("global debug")
		Info("global info")
		Warn("global warn")
		Error("global error")
	})

	// Test With function
	childLogger := With(Field{Key: "global", Value: true})
	assert.NotNil(t, childLogger)

	// Test WithContext function
	ctx := context.WithValue(context.Background(), UserIDKey, "global-user")
	contextLogger := WithContext(ctx)
	assert.NotNil(t, contextLogger)
}

func TestZapLogger_InvalidFileOutput(t *testing.T) {
	config := &Config{
		Level:  InfoLevel,
		Format: "json",
		Output: "file",
		// FilePath is intentionally missing
	}

	_, err := NewZapLogger(config)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "file path is required")
}