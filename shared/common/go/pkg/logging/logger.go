package logging

import (
	"context"
	"fmt"
	"os"
	"sync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type ZapLogger struct {
	logger *zap.Logger
	sugar  *zap.SugaredLogger
	level  zap.AtomicLevel
	config *Config
}

func NewZapLogger(config *Config) (*ZapLogger, error) {
	level := zap.NewAtomicLevel()
	level.SetLevel(levelToZapLevel(config.Level))

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		Call<PERSON><PERSON><PERSON>:      "caller",
		MessageKey:     "message",
		StacktraceKey:  "stack",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var encoder zapcore.Encoder
	if config.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	var writeSyncer zapcore.WriteSyncer
	switch config.Output {
	case "stdout":
		writeSyncer = zapcore.AddSync(os.Stdout)
	case "stderr":
		writeSyncer = zapcore.AddSync(os.Stderr)
	case "file":
		if config.FilePath == "" {
			return nil, fmt.Errorf("file path is required when output is 'file'")
		}
		file, err := os.OpenFile(config.FilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
		writeSyncer = zapcore.AddSync(file)
	default:
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	core := zapcore.NewCore(encoder, writeSyncer, level)

	var options []zap.Option
	if config.Development {
		options = append(options, zap.Development())
	}
	options = append(options, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	logger := zap.New(core, options...)

	return &ZapLogger{
		logger: logger,
		sugar:  logger.Sugar(),
		level:  level,
		config: config,
	}, nil
}

func (z *ZapLogger) Debug(msg string, fields ...Field) {
	z.logger.Debug(msg, fieldsToZapFields(fields)...)
}

func (z *ZapLogger) Info(msg string, fields ...Field) {
	z.logger.Info(msg, fieldsToZapFields(fields)...)
}

func (z *ZapLogger) Warn(msg string, fields ...Field) {
	z.logger.Warn(msg, fieldsToZapFields(fields)...)
}

func (z *ZapLogger) Error(msg string, fields ...Field) {
	z.logger.Error(msg, fieldsToZapFields(fields)...)
}

func (z *ZapLogger) Fatal(msg string, fields ...Field) {
	z.logger.Fatal(msg, fieldsToZapFields(fields)...)
}

func (z *ZapLogger) With(fields ...Field) Logger {
	newLogger := z.logger.With(fieldsToZapFields(fields)...)
	return &ZapLogger{
		logger: newLogger,
		sugar:  newLogger.Sugar(),
		level:  z.level,
		config: z.config,
	}
}

func (z *ZapLogger) WithContext(ctx context.Context) Logger {
	var fields []Field

	if traceID := ctx.Value(TraceIDKey); traceID != nil {
		fields = append(fields, Field{Key: "trace_id", Value: traceID})
	}
	if spanID := ctx.Value(SpanIDKey); spanID != nil {
		fields = append(fields, Field{Key: "span_id", Value: spanID})
	}
	if userID := ctx.Value(UserIDKey); userID != nil {
		fields = append(fields, Field{Key: "user_id", Value: userID})
	}
	if requestID := ctx.Value(RequestIDKey); requestID != nil {
		fields = append(fields, Field{Key: "request_id", Value: requestID})
	}
	if agentID := ctx.Value(AgentIDKey); agentID != nil {
		fields = append(fields, Field{Key: "agent_id", Value: agentID})
	}
	if workflowID := ctx.Value(WorkflowIDKey); workflowID != nil {
		fields = append(fields, Field{Key: "workflow_id", Value: workflowID})
	}
	if taskID := ctx.Value(TaskIDKey); taskID != nil {
		fields = append(fields, Field{Key: "task_id", Value: taskID})
	}

	return z.With(fields...)
}

func (z *ZapLogger) SetLevel(level Level) {
	z.level.SetLevel(levelToZapLevel(level))
}

func (z *ZapLogger) GetLevel() Level {
	return zapLevelToLevel(z.level.Level())
}

type LoggerFactoryImpl struct {
	loggers map[string]Logger
	mu      sync.RWMutex
	config  *Config
}

func NewLoggerFactory(config *Config) *LoggerFactoryImpl {
	return &LoggerFactoryImpl{
		loggers: make(map[string]Logger),
		config:  config,
	}
}

func (f *LoggerFactoryImpl) CreateLogger(name string, config *Config) (Logger, error) {
	f.mu.Lock()
	defer f.mu.Unlock()

	logger, err := NewZapLogger(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create logger '%s': %w", name, err)
	}

	f.loggers[name] = logger
	return logger, nil
}

func (f *LoggerFactoryImpl) GetLogger(name string) (Logger, error) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	logger, exists := f.loggers[name]
	if !exists {
		return nil, fmt.Errorf("logger '%s' not found", name)
	}
	return logger, nil
}

func (f *LoggerFactoryImpl) SetGlobalLevel(level Level) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	for _, logger := range f.loggers {
		logger.SetLevel(level)
	}
}

func (f *LoggerFactoryImpl) Shutdown() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	for _, logger := range f.loggers {
		if zapLogger, ok := logger.(*ZapLogger); ok {
			zapLogger.logger.Sync()
		}
	}
	return nil
}

// Helper functions
func levelToZapLevel(level Level) zapcore.Level {
	switch level {
	case DebugLevel:
		return zapcore.DebugLevel
	case InfoLevel:
		return zapcore.InfoLevel
	case WarnLevel:
		return zapcore.WarnLevel
	case ErrorLevel:
		return zapcore.ErrorLevel
	case FatalLevel:
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

func zapLevelToLevel(level zapcore.Level) Level {
	switch level {
	case zapcore.DebugLevel:
		return DebugLevel
	case zapcore.InfoLevel:
		return InfoLevel
	case zapcore.WarnLevel:
		return WarnLevel
	case zapcore.ErrorLevel:
		return ErrorLevel
	case zapcore.FatalLevel:
		return FatalLevel
	default:
		return InfoLevel
	}
}

func fieldsToZapFields(fields []Field) []zap.Field {
	zapFields := make([]zap.Field, len(fields))
	for i, field := range fields {
		zapFields[i] = zap.Any(field.Key, field.Value)
	}
	return zapFields
}

// Global logger instance
var globalLogger Logger

func init() {
	config := &Config{
		Level:       InfoLevel,
		Format:      "json",
		Output:      "stdout",
		Development: false,
	}
	
	logger, err := NewZapLogger(config)
	if err != nil {
		panic(fmt.Sprintf("failed to initialize global logger: %v", err))
	}
	globalLogger = logger
}

// Global functions
func Debug(msg string, fields ...Field) {
	globalLogger.Debug(msg, fields...)
}

func Info(msg string, fields ...Field) {
	globalLogger.Info(msg, fields...)
}

func Warn(msg string, fields ...Field) {
	globalLogger.Warn(msg, fields...)
}

func Error(msg string, fields ...Field) {
	globalLogger.Error(msg, fields...)
}

func Fatal(msg string, fields ...Field) {
	globalLogger.Fatal(msg, fields...)
}

func With(fields ...Field) Logger {
	return globalLogger.With(fields...)
}

func WithContext(ctx context.Context) Logger {
	return globalLogger.WithContext(ctx)
}

func SetGlobalLogger(logger Logger) {
	globalLogger = logger
}

func GetGlobalLogger() Logger {
	return globalLogger
}