package logging

import (
	"context"
)

type Level int

const (
	DebugLevel Level = iota
	InfoLevel
	WarnLevel
	ErrorLevel
	FatalLevel
)

func (l Level) String() string {
	switch l {
	case DebugLevel:
		return "debug"
	case InfoLevel:
		return "info"
	case WarnLevel:
		return "warn"
	case ErrorLevel:
		return "error"
	case FatalLevel:
		return "fatal"
	default:
		return "unknown"
	}
}

type Field struct {
	Key   string
	Value interface{}
}

type Logger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	
	With(fields ...Field) Logger
	WithContext(ctx context.Context) Logger
	
	SetLevel(level Level)
	GetLevel() Level
}

type Config struct {
	Level       Level  `yaml:"level"`
	Format      string `yaml:"format"` // json, console
	Output      string `yaml:"output"` // stdout, stderr, file
	FilePath    string `yaml:"file_path,omitempty"`
	Development bool   `yaml:"development"`
	
	// Sampling configuration
	SamplingEnabled    bool `yaml:"sampling_enabled"`
	SamplingThereafter int  `yaml:"sampling_thereafter"`
	SamplingInitial    int  `yaml:"sampling_initial"`
}

type ContextKey string

const (
	TraceIDKey     ContextKey = "trace_id"
	SpanIDKey      ContextKey = "span_id"
	UserIDKey      ContextKey = "user_id"
	RequestIDKey   ContextKey = "request_id"
	AgentIDKey     ContextKey = "agent_id"
	WorkflowIDKey  ContextKey = "workflow_id"
	TaskIDKey      ContextKey = "task_id"
)

type LogEntry struct {
	Timestamp string                 `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
	Caller    string                 `json:"caller,omitempty"`
	Stack     string                 `json:"stack,omitempty"`
}

type LoggerFactory interface {
	CreateLogger(name string, config *Config) (Logger, error)
	GetLogger(name string) (Logger, error)
	SetGlobalLevel(level Level)
	Shutdown() error
}