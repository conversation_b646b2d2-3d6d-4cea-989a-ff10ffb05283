load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

# Config package
go_library(
    name = "config",
    srcs = ["pkg/config/loader.go"],
    importpath = "github.com/ai-platform/shared/common/go/pkg/config",
    visibility = ["//visibility:public"],
    deps = [
        "@org_uber_go_config//:config",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)

go_test(
    name = "config_test",
    srcs = ["pkg/config/loader_test.go"],
    deps = [
        ":config",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Auth package
go_library(
    name = "auth",
    srcs = [
        "pkg/auth/jwt.go",
        "pkg/auth/types.go",
    ],
    importpath = "github.com/ai-platform/shared/common/go/pkg/auth",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@org_golang_x_crypto//bcrypt",
    ],
)

go_test(
    name = "auth_test",
    srcs = ["pkg/auth/jwt_test.go"],
    deps = [
        ":auth",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Database package
go_library(
    name = "database",
    srcs = [
        "pkg/database/connection.go",
        "pkg/database/types.go",
    ],
    importpath = "github.com/ai-platform/shared/common/go/pkg/database",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_lib_pq//:pq",
        "@io_go_mongodb_org_mongo_driver//mongo",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)

go_test(
    name = "database_test",
    srcs = ["pkg/database/connection_test.go"],
    deps = [
        ":database",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Logging package
go_library(
    name = "logging",
    srcs = [
        "pkg/logging/logger.go",
        "pkg/logging/types.go",
    ],
    importpath = "github.com/ai-platform/shared/common/go/pkg/logging",
    visibility = ["//visibility:public"],
    deps = [
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "logging_test",
    srcs = ["pkg/logging/logger_test.go"],
    deps = [
        ":logging",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Metrics package
go_library(
    name = "metrics",
    srcs = [
        "pkg/metrics/prometheus.go",
        "pkg/metrics/types.go",
    ],
    importpath = "github.com/ai-platform/shared/common/go/pkg/metrics",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
    ],
)

go_test(
    name = "metrics_test",
    srcs = ["pkg/metrics/prometheus_test.go"],
    deps = [
        ":metrics",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

# Utils package
go_library(
    name = "utils",
    srcs = [
        "pkg/utils/validation.go",
        "pkg/utils/encoding.go",
    ],
    importpath = "github.com/ai-platform/shared/common/go/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "@org_golang_x_crypto//bcrypt",
    ],
)

go_test(
    name = "utils_test",
    srcs = [
        "pkg/utils/validation_test.go",
        "pkg/utils/encoding_test.go",
    ],
    deps = [
        ":utils",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)