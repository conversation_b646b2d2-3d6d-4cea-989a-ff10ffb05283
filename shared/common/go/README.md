# AI Platform - Go Common Libraries

This package provides shared Go libraries for the AI-Native Agent Platform. It contains common functionality that can be used across all Go-based services in the platform.

## Packages

### Config (`pkg/config`)
Configuration management with support for YAML files and environment variables.

**Features:**
- Multi-source configuration loading (files, environment variables)
- Automatic environment variable override
- Configuration validation
- Support for database, logging, metrics, and security configuration

**Usage:**
```go
loader, err := config.NewLoader("config.yaml")
cfg, err := loader.LoadConfig()
```

### Auth (`pkg/auth`)
Authentication and authorization utilities with JWT token support.

**Features:**
- JWT token generation and validation
- Password hashing with bcrypt
- Token refresh mechanism
- User and claims management
- Secure secret generation

**Usage:**
```go
provider := auth.NewJWTProvider("secret", time.Hour, time.Hour*24)
tokenPair, err := provider.GenerateToken(claims)
```

### Database (`pkg/database`)
Database connection management for multiple database types.

**Features:**
- Support for PostgreSQL, MySQL, MongoDB, and Redis
- Connection pooling
- Health checking
- Transaction management
- Repository pattern interfaces

**Usage:**
```go
pool := database.NewConnectionPool()
conn := database.NewPostgreSQLConnection(config)
pool.AddConnection("main", conn)
```

### Logging (`pkg/logging`)
Structured logging with Zap backend.

**Features:**
- JSON and console output formats
- Log level management
- Context-aware logging
- Structured fields
- Logger factory pattern

**Usage:**
```go
logger, err := logging.NewZapLogger(config)
logger.Info("Message", logging.Field{Key: "user_id", Value: "123"})
```

### Metrics (`pkg/metrics`)
Prometheus-based metrics collection.

**Features:**
- Counter, Gauge, Histogram, and Summary metrics
- Metric vectors with labels
- HTTP metrics server
- Standard platform metrics
- Registry management

**Usage:**
```go
provider := metrics.NewPrometheusProvider(config)
counter := provider.GetRegistry().NewCounter("requests_total", "Total requests", nil)
```

### Utils (`pkg/utils`)
Utility functions for validation and encoding.

**Features:**
- Field validation with chaining
- Email, UUID, slug, and semantic version validation
- Password strength validation
- AES encryption/decryption
- Base64, hex, and JSON encoding
- Data masking for sensitive information

**Usage:**
```go
errors := utils.NewFieldValidator("email", email).Required().Email().Errors()
encryptor, err := utils.NewAESEncryptor(key)
encrypted, err := encryptor.EncryptString("secret")
```

## Testing

Each package includes comprehensive test coverage (>80% target). Tests can be run using:

```bash
go test ./...
```

For specific packages:
```bash
go test ./pkg/config
go test ./pkg/auth
go test ./pkg/database
go test ./pkg/logging
go test ./pkg/metrics
go test ./pkg/utils
```

## Dependencies

Key dependencies include:
- **Zap**: High-performance logging
- **Prometheus**: Metrics collection
- **JWT**: Token authentication
- **Database drivers**: PostgreSQL, MySQL, MongoDB, Redis
- **Crypto**: Encryption and hashing utilities

## Development Standards

- Go 1.21+ required
- All public functions must have documentation
- Comprehensive error handling
- Thread-safe implementations where applicable
- Consistent naming conventions
- Extensive test coverage

## Integration

This package is designed to be imported by other Go services in the AI Platform:

```go
import (
    "github.com/ai-platform/shared/common/go/pkg/auth"
    "github.com/ai-platform/shared/common/go/pkg/config"
    "github.com/ai-platform/shared/common/go/pkg/logging"
    // ... other packages
)
```

## Build

This package uses Bazel for build management. See the `BUILD.bazel` file for build targets and dependencies.