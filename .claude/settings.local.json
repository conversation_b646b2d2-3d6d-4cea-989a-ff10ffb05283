{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(go mod:*)", "Bash(find:*)", "Bash(grep:*)", "WebFetch(domain:github.com)", "Bash(go build:*)", "Bash(brew install:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(sudo installer:*)", "<PERSON><PERSON>(open:*)", "Bash(export PATH=$PATH:~/go-local/bin)", "<PERSON><PERSON>(go:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(echo:*)", "Bash(rm:*)", "Bash(python -m pytest tests/test_validation.py::run_validation_tests -v)", "<PERSON><PERSON>(python3:*)"], "deny": []}}