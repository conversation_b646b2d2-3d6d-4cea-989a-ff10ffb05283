{"main": {"id": "6130b53e5d46209d", "type": "split", "children": [{"id": "6999ba7c2af1fd81", "type": "tabs", "children": [{"id": "2e18f0dfebae7aa1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "docs/development/agents/08-supreme-platform-intelligence-agent-dev-plan.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "08-supreme-platform-intelligence-agent-dev-plan"}}]}], "direction": "vertical"}, "left": {"id": "3d4a79f46db8d787", "type": "split", "children": [{"id": "2d6ebd12fbbf551c", "type": "tabs", "children": [{"id": "f86524ddd69a8a06", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "e1ec44e3610b1e0f", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "c15d92fadc036923", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "22c0a48429acb810", "type": "split", "children": [{"id": "050b10916710fd27", "type": "tabs", "children": [{"id": "2c5f6027b6035f85", "type": "leaf", "state": {"type": "backlink", "state": {"file": "docs/architecture/system-design.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for system-design"}}, {"id": "178ea10955241552", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "docs/architecture/system-design.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from system-design"}}, {"id": "aa30fb2392251ade", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "be28d50d469f952b", "type": "leaf", "state": {"type": "outline", "state": {"file": "docs/architecture/system-design.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of system-design"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "f86524ddd69a8a06", "lastOpenFiles": ["implementation/agents/02-discovery-registry-agent/src/dra/analytics/__init__.py", "implementation/agents/02-discovery-registry-agent/src/dra/analytics", "implementation/agents/02-discovery-registry-agent/src/dra/mesh/consul_adapter.py", "implementation/agents/02-discovery-registry-agent/src/dra/mesh/linkerd_adapter.py", "implementation/agents/02-discovery-registry-agent/src/dra/mesh/istio_adapter.py", "implementation/agents/02-discovery-registry-agent/src/dra/mesh/mesh_adapter.py", "implementation/agents/02-discovery-registry-agent/src/dra/mesh/__init__.py", "implementation/agents/02-discovery-registry-agent/src/dra/mesh", "implementation/agents/02-discovery-registry-agent/src/dra/communication/message_handler.py", "implementation/agents/02-discovery-registry-agent/src/dra/communication/cba_client.py", "implementation/agents/02-discovery-registry-agent/src/dra/communication/__init__.py", "implementation/agents/02-discovery-registry-agent/dev-progress.md", "implementation/agents/02-discovery-registry-agent/README.md", "implementation/agents/01-communication-broker-agent/go/src/cmd/compile/internal/types2/README.md", "implementation/agents/01-communication-broker-agent/go/src/cmd/compile/internal/ssa/README.md", "implementation/agents/01-communication-broker-agent/go/src/cmd/compile/abi-internal.md", "implementation/agents/01-communication-broker-agent/go/src/cmd/compile/README.md", "implementation/agents/01-communication-broker-agent/go/src/archive/zip/testdata/gophercolor16x16.png", "implementation/agents/01-communication-broker-agent/go/misc/chrome/gophertool/gopher.png", "implementation/agents/01-communication-broker-agent/go/lib/fips140/README.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/6-stdlib/99-minor/0-heading.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/6-stdlib/0-heading.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/7-ports.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/5-toolchain.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/4-runtime.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/3-tools.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/2-language.md", "implementation/agents/01-communication-broker-agent/go/doc/initial/1-intro.md", "implementation/agents/01-communication-broker-agent/go/doc/godebug.md", "implementation/agents/01-communication-broker-agent/go/doc/README.md", "implementation/agents/01-communication-broker-agent/go/SECURITY.md", "implementation/agents/01-communication-broker-agent/go/README.md", "implementation/agents/01-communication-broker-agent/go/CONTRIBUTING.md", "implementation/agents/01-communication-broker-agent/dev-progress.md", "implementation/README.md", "docs/development/agents/07-agent-factory-agent-dev-plan.md", "docs/development/agents/06-task-orchestrator-agent-dev-plan.md", "docs/development/agents/05-knowledge-base-agent-dev-plan.md"]}